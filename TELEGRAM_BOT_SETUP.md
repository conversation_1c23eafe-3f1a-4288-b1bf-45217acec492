# 🤖 Telegram 机器人设置指南

## 📋 功能概述

我们的 Telegram 机器人提供以下功能：
- ✅ **获取用户 Telegram ID** - 用户可以轻松获取自己的 ID
- ✅ **接收证件到期提醒** - 自动发送到期通知
- ✅ **系统绑定指导** - 帮助用户完成绑定流程
- ✅ **交互式命令** - 支持多种便民命令

## 🚀 部署步骤

### 1. 创建 Telegram Bot
1. 在 Telegram 中搜索 `@BotFather`
2. 发送 `/newbot` 命令
3. 按提示设置机器人名称和用户名
4. 获取 Bot Token（格式：`123456789:ABCdefGHIjklMNOpqrsTUVwxyz`）

### 2. 配置系统
1. 登录管理员后台
2. 进入 `/admin/notifications` 页面
3. 在 "Telegram配置" 部分：
   - 开启 Telegram 通知开关
   - 输入获取的 Bot Token
   - 设置机器人名称（可选）
   - 保存配置

### 3. 启动机器人服务

#### 开发环境
```bash
npm run telegram:bot
```

#### 生产环境
```bash
# 方式1：直接启动
node scripts/start-telegram-bot.js

# 方式2：使用 PM2
pm2 start scripts/start-telegram-bot.js --name telegram-bot

# 方式3：使用 Docker
docker run -d --name telegram-bot \
  -e DATABASE_URL="your-database-url" \
  your-app:latest npm run telegram:bot
```

### 4. 验证机器人状态
1. 在管理后台查看 "Telegram Bot 状态"
2. 确认显示 "✅ 运行中"
3. 可以看到机器人信息（名称、用户名、ID）

## 👥 用户使用流程

### 1. 用户获取 Telegram ID
1. 在 Telegram 中搜索你的机器人（@your_bot_username）
2. 发送 `/start` 或 `/id` 命令
3. 机器人会回复用户的 Telegram ID

### 2. 绑定到系统
1. 用户登录证件提醒系统
2. 进入 "通知设置" 页面
3. 在 Telegram 配置中粘贴获取的 ID
4. 开启 Telegram 通知开关
5. 保存设置

### 3. 接收通知
- 证件到期提醒会自动发送到用户的 Telegram
- 用户可以发送 `/help` 查看帮助信息

## 🤖 机器人命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `/start` | 开始使用，显示欢迎信息和用户ID | `/start` |
| `/id` | 获取用户的 Telegram ID | `/id` |
| `/help` | 查看帮助信息 | `/help` |
| `/bind` | 显示绑定指导 | `/bind` |

## 🔧 管理功能

### 重启机器人
- 在管理后台点击 "重启机器人" 按钮
- 或使用 API：`POST /api/trpc/systemNotification.restartTelegramBot`

### 查看状态
- 管理后台实时显示机器人运行状态
- 包含机器人信息和连接状态

## 🚨 故障排除

### 机器人无响应
1. 检查 Bot Token 是否正确
2. 确认机器人服务正在运行
3. 查看服务器日志
4. 尝试重启机器人

### 用户收不到通知
1. 确认用户已正确绑定 Telegram ID
2. 检查用户是否启用了 Telegram 通知
3. 确认机器人有发送消息权限
4. 用户需要先与机器人对话（发送 `/start`）

### 常见错误
- `403 Forbidden` - 用户阻止了机器人
- `400 Bad Request` - Chat ID 格式错误
- `401 Unauthorized` - Bot Token 无效

## 📱 用户界面集成

系统已集成以下用户界面：
- 通知设置页面的 Telegram 配置
- "获取我的Telegram ID" 按钮
- 自动打开机器人对话链接

## 🔒 安全注意事项

1. **保护 Bot Token** - 不要在客户端暴露
2. **验证用户身份** - 确保 ID 绑定的安全性
3. **限制权限** - 机器人只需要发送消息权限
4. **监控使用** - 定期检查机器人活动日志

## 📈 监控和维护

- 定期检查机器人运行状态
- 监控消息发送成功率
- 备份重要配置信息
- 更新机器人功能和安全补丁

**现在用户可以轻松获取自己的 Telegram ID 并接收证件到期提醒了！** 🎉
