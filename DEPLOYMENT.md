# 🚀 生产环境部署指南

## 快速部署

### 环境变量设置
只需要设置4个环境变量：

```bash
NEXTAUTH_SECRET="your-super-secret-key-here-at-least-32-chars"
DATABASE_URL="postgresql://username:password@host:port/database"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="YourSecurePassword123!"
```

### 一键部署（推荐）
```bash
npm run deploy:simple
```

### 高级部署（保留数据）
```bash
npm run deploy:prod
```

## 支持的平台

### Vercel（推荐）
1. 连接 GitHub 仓库
2. 设置环境变量
3. 自动部署

### Railway
1. 连接 GitHub 仓库
2. 添加 PostgreSQL 数据库
3. 设置环境变量
4. 自动部署

### Render
1. 连接 GitHub 仓库
2. 选择 Web Service
3. 设置构建命令：`npm run deploy:prod`
4. 设置启动命令：`npm start`

### VPS 部署
```bash
# 克隆代码
git clone your-repo
cd certificate-reminder

# 设置环境变量
cp .env.production .env
# 编辑 .env 文件

# 一键部署
npm run deploy:prod

# 启动服务
npm start
```

## 部署后配置

1. **登录管理员账户**
   - 访问：`https://yourdomain.com/auth/signin`
   - 使用设置的 ADMIN_EMAIL 和 ADMIN_PASSWORD

2. **立即修改密码**
   - 进入用户设置修改默认密码

3. **配置系统服务**
   - 访问：`/admin/notifications`
   - 配置邮件服务（SMTP）
   - 配置 Telegram Bot（可选）
   - 配置 PWA 推送（可选）

4. **检查订阅计划**
   - 访问：`/admin/plans`
   - 调整价格和功能

## 故障排除

### 常见问题
- **数据库连接失败**：检查 DATABASE_URL 格式
- **认证失败**：检查 NEXTAUTH_SECRET 设置
- **构建失败**：确保 Node.js 版本 18+

### 获取帮助
如果遇到问题，请检查：
1. 环境变量是否正确设置
2. 数据库是否可访问
3. Node.js 版本是否兼容
