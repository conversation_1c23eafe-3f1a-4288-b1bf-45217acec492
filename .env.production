# 生产环境配置模板
# 复制此文件并填入真实值

# ===== 必需的环境变量 =====

# Next Auth - 必须设置一个强密钥（至少32字符）
NEXTAUTH_SECRET="your-super-secret-key-here-change-this-in-production-at-least-32-chars"

# 数据库连接 - PostgreSQL 连接字符串
# 格式：postgresql://username:password@host:port/database
# 示例：postgresql://user:<EMAIL>:5432/mydb
DATABASE_URL="postgresql://username:password@host:port/database"

# 超级管理员账户 - 仅首次部署时需要
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="YourSecurePassword123!"

# ===== 可选环境变量 =====
NODE_ENV="production"

# ===== 重要提醒 =====
# 1. 首次部署后立即登录并修改管理员密码
# 2. 其他配置（邮件、Telegram、PWA推送等）都在管理后台设置
# 3. 登录后访问：/admin/notifications 进行系统配置
# 4. 确保 NEXTAUTH_SECRET 是一个强随机字符串
