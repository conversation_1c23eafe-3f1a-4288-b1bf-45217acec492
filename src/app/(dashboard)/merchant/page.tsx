"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
} from "@mui/material";
import {
  Business,
  Person,
  Edit,
  Delete,
  PersonAdd,
  Upgrade,
  Refresh,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { usePermissions } from "~/hooks/usePermissions";
import { StatsCardSkeleton, ListItemSkeleton, FormSkeleton } from "~/components/ui/Skeleton";
import UpgradeDialog from "~/app/_components/upgrade-dialog";
import MerchantOrders from "~/app/_components/merchant-orders";

export default function MerchantPage() {
  const { data: session, status } = useSession();
  const permissions = usePermissions();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState({
    name: '',
    email: '',
    role: 'TENANT_MEMBER' as const,
  });

  const tenantId = session?.user?.currentTenantId;

  // 获取商户信息
  const { data: merchantInfo, refetch: refetchMerchant } = api.merchant.getInfo.useQuery({
    tenantId: tenantId!,
  }, {
    enabled: !!tenantId,
    refetchInterval: 30000, // 每30秒刷新一次
  });

  // 获取订阅信息
  const { data: subscription, refetch: refetchSubscription } = api.subscription.getCurrent.useQuery(
    { tenantId: tenantId! },
    {
      enabled: !!tenantId,
      refetchInterval: 30000, // 每30秒刷新一次
    }
  );

  // 获取使用统计
  const { data: usage } = api.subscription.getUsage.useQuery(
    { tenantId: tenantId! },
    { enabled: !!tenantId }
  );

  // 添加用户 - 所有mutation hooks必须在条件性返回之前调用
  const addUserMutation = api.merchant.inviteUser.useMutation({
    onSuccess: () => {
      refetch();
      setAddUserDialogOpen(false);
      setNewUserData({ name: '', email: '', role: 'TENANT_MEMBER' });
    },
  });

  // 删除用户
  const removeUserMutation = api.merchant.removeUser.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  // 升级商户
  const upgradeMutation = api.merchant.upgrade.useMutation({
    onSuccess: () => {
      refetch();
      setUpgradeDialogOpen(false);
    },
  });

  // 早期返回检查 - 所有hooks调用完成后才能进行条件性返回
  if (status === "loading") {
    return <MerchantPageSkeleton />;
  }

  if (!session) {
    redirect("/auth/signin");
  }

  if (!tenantId) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Alert severity="warning">
          未找到关联的商户，请联系管理员
        </Alert>
      </Box>
    );
  }

  if (!merchantInfo) {
    return <MerchantPageSkeleton />;
  }

  const isAdmin = permissions.isTenantAdmin;

  const handleAddUser = () => {
    addUserMutation.mutate({
      tenantId,
      ...newUserData,
    });
  };

  const handleRemoveUser = (userId: string) => {
    if (confirm('确定要移除此用户吗？')) {
      removeUserMutation.mutate({
        tenantId,
        userId,
      });
    }
  };

  const handleUpgrade = (type: 'PERSONAL' | 'ENTERPRISE') => {
    upgradeMutation.mutate({
      tenantId,
      type,
    });
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: 1200, mx: 'auto' }}>
      {/* 商户基本信息 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              {merchantInfo.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
              <Chip
                icon={merchantInfo.type === 'ENTERPRISE' ? <Business /> :
                      merchantInfo.type === 'PERSONAL' ? <Person /> : <Person />}
                label={merchantInfo.type === 'ENTERPRISE' ? '企业商户' :
                       merchantInfo.type === 'PERSONAL' ? '个人商户' : '免费用户'}
                color={merchantInfo.type === 'FREE' ? 'default' : 'primary'}
                size="small"
              />
              {subscription && (
                <Chip
                  label={subscription.plan === 'FREE' ?
                         (subscription.status === 'EXPIRED' ? '免费试用已过期' : `免费试用 (${subscription.daysLeft}天)`) :
                         subscription.status === 'TRIAL' ? '试用中' :
                         subscription.status === 'ACTIVE' ? '已订阅' :
                         subscription.status === 'PENDING' ? '待审核' : '已过期'}
                  color={subscription.plan === 'FREE' ?
                         (subscription.status === 'EXPIRED' ? 'error' : 'default') :
                         subscription.status === 'ACTIVE' ? 'success' :
                         subscription.status === 'TRIAL' ? 'info' :
                         subscription.status === 'PENDING' ? 'warning' : 'error'}
                  size="small"
                />
              )}
              <Button
                variant="outlined"
                size="small"
                startIcon={<Refresh />}
                onClick={() => {
                  refetchMerchant();
                  refetchSubscription();
                }}
              >
                刷新
              </Button>
              {merchantInfo.type === 'FREE' && (
                <Button
                  variant="contained"
                  color="success"
                  size="small"
                  startIcon={<Upgrade />}
                  onClick={() => setUpgradeDialogOpen(true)}
                >
                  升级
                </Button>
              )}
            </Box>
            {merchantInfo.description && (
              <Typography variant="body2" color="text.secondary">
                {merchantInfo.description}
              </Typography>
            )}
          </Box>
          {isAdmin && (
            <Button
              variant="outlined"
              startIcon={<Edit />}
              onClick={() => setEditDialogOpen(true)}
              size="small"
            >
              编辑信息
            </Button>
          )}
        </Box>
      </Paper>

      {/* 套餐使用统计 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          套餐使用情况
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {merchantInfo._count?.documents || 0}
                {subscription?.limits?.maxDocuments && subscription.limits.maxDocuments !== -1 && (
                  <Typography component="span" variant="h6" color="text.secondary">
                    /{subscription.limits.maxDocuments}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                证件数量
                {subscription?.limits?.maxDocuments === -1 && (
                  <Typography component="span" color="success.main"> (无限制)</Typography>
                )}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {merchantInfo.memberships?.length || 0}
                {subscription?.limits?.maxUsers && subscription.limits.maxUsers !== -1 && (
                  <Typography component="span" variant="h6" color="text.secondary">
                    /{subscription.limits.maxUsers}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                用户数量
                {subscription?.limits?.maxUsers === -1 && (
                  <Typography component="span" color="success.main"> (无限制)</Typography>
                )}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {merchantInfo._count?.documentTypes || 0}
                {subscription?.limits?.maxCustomFields && subscription.limits.maxCustomFields !== -1 && (
                  <Typography component="span" variant="h6" color="text.secondary">
                    /{subscription.limits.maxCustomFields}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                证件类型
                {subscription?.limits?.maxCustomFields === -1 && (
                  <Typography component="span" color="success.main"> (无限制)</Typography>
                )}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {merchantInfo._count?.notifications || 0}
                {subscription?.limits?.maxNotifications && subscription.limits.maxNotifications !== -1 && (
                  <Typography component="span" variant="h6" color="text.secondary">
                    /{subscription.limits.maxNotifications}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                本月通知
                {subscription?.limits?.maxNotifications === -1 && (
                  <Typography component="span" color="success.main"> (无限制)</Typography>
                )}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* 套餐详情 */}
        {subscription && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              当前套餐详情
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    套餐名称
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {subscription.planInfo?.name || subscription.plan?.name || '免费试用'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    套餐状态
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {subscription.plan === 'FREE' ?
                     (subscription.status === 'EXPIRED' ? '已过期' : `免费试用 (剩余${subscription.daysLeft}天)`) :
                     subscription.status === 'TRIAL' ? '试用中' :
                     subscription.status === 'ACTIVE' ? '已激活' :
                     subscription.status === 'PENDING' ? '待审核' : '已过期'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    开始时间
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {subscription.startDate ? new Date(subscription.startDate).toLocaleDateString() : '未知'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    到期时间
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {subscription.plan === 'FREE' && subscription.trialEndDate ?
                     new Date(subscription.trialEndDate).toLocaleDateString() :
                     subscription.endDate ? new Date(subscription.endDate).toLocaleDateString() :
                     subscription.status === 'TRIAL' && subscription.trialEndDate ?
                     new Date(subscription.trialEndDate).toLocaleDateString() : '永久'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {/* 用户管理 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">
            团队成员
          </Typography>
          {isAdmin && merchantInfo.type === 'ENTERPRISE' && (
            <Button
              variant="contained"
              startIcon={<PersonAdd />}
              onClick={() => setAddUserDialogOpen(true)}
              size="small"
            >
              添加成员
            </Button>
          )}
          {merchantInfo.type !== 'ENTERPRISE' && (
            <Typography variant="body2" color="text.secondary">
              {merchantInfo.type === 'FREE' ? '升级为企业商户后可添加团队成员' : '个人商户不支持添加团队成员'}
            </Typography>
          )}
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>姓名</TableCell>
                <TableCell>邮箱</TableCell>
                <TableCell>角色</TableCell>
                <TableCell>加入时间</TableCell>
                {isAdmin && <TableCell align="right">操作</TableCell>}
              </TableRow>
            </TableHead>
            <TableBody>
              {merchantInfo.memberships?.map((membership: any) => (
                <TableRow key={membership.user.id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        {membership.user.name?.charAt(0) || membership.user.email?.charAt(0)}
                      </Avatar>
                      {membership.user.name || '未设置'}
                    </Box>
                  </TableCell>
                  <TableCell>{membership.user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={membership.role === 'TENANT_ADMIN' ? '管理员' : '成员'}
                      size="small"
                      color={membership.role === 'TENANT_ADMIN' ? 'primary' : 'default'}
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(membership.user.createdAt).toLocaleDateString()}
                  </TableCell>
                  {isAdmin && (
                    <TableCell align="right">
                      {membership.user.id !== session.user.id && (
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleRemoveUser(membership.user.id)}
                        >
                          <Delete />
                        </IconButton>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* 订单管理 */}
      {isAdmin && (
        <Box sx={{ mb: 3 }}>
          <MerchantOrders tenantId={tenantId} />
        </Box>
      )}

      {/* 升级对话框 */}
      <UpgradeDialog
        open={upgradeDialogOpen}
        onClose={() => setUpgradeDialogOpen(false)}
        currentPlan={merchantInfo.type}
        tenantId={tenantId}
        onSuccess={() => {
          // 刷新页面数据
          window.location.reload();
        }}
      />

      {/* 编辑商户信息对话框 */}
      <EditMerchantDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        merchantInfo={merchantInfo}
        onSuccess={() => {
          refetch();
          setEditDialogOpen(false);
        }}
      />

      {/* 邀请用户对话框 */}
      <InviteUserDialog
        open={addUserDialogOpen}
        onClose={() => setAddUserDialogOpen(false)}
        tenantId={tenantId}
        onSuccess={() => {
          refetch();
          setAddUserDialogOpen(false);
        }}
      />
    </Box>
  );
}

// 编辑商户信息对话框组件
function EditMerchantDialog({ open, onClose, merchantInfo, onSuccess }: any) {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    description: '',
  });

  const updateMutation = api.merchant.updateInfo.useMutation({
    onSuccess: () => {
      onSuccess();
    },
  });

  useEffect(() => {
    if (merchantInfo) {
      setFormData({
        name: merchantInfo.name || '',
        phone: merchantInfo.phone || '',
        email: merchantInfo.email || '',
        address: merchantInfo.address || '',
        description: merchantInfo.description || '',
      });
    }
  }, [merchantInfo]);

  const handleSave = () => {
    updateMutation.mutate({
      tenantId: merchantInfo.id,
      ...formData,
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>编辑商户信息</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              label="商户名称"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              size="small"
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="联系电话"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="邮箱地址"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              fullWidth
              size="small"
              type="email"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="地址"
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="商户描述"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              fullWidth
              size="small"
              multiline
              rows={3}
              placeholder="简单介绍您的商户..."
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small">取消</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={updateMutation.isLoading || !formData.name}
          size="small"
        >
          {updateMutation.isLoading ? '保存中...' : '保存'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// 邀请用户对话框组件
function InviteUserDialog({ open, onClose, tenantId, onSuccess }: any) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'TENANT_MEMBER',
  });

  const inviteUserMutation = api.merchant.inviteUser.useMutation({
    onSuccess: () => {
      onSuccess();
      setFormData({ name: '', email: '', role: 'TENANT_MEMBER' });
    },
  });

  const handleInvite = () => {
    inviteUserMutation.mutate({
      tenantId,
      ...formData,
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>邀请用户</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              label="姓名"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              size="small"
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="邮箱地址"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              fullWidth
              size="small"
              type="email"
              required
              helperText="系统将向此邮箱发送邀请链接"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth size="small">
              <InputLabel>用户角色</InputLabel>
              <Select
                value={formData.role}
                label="用户角色"
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              >
                <MenuItem value="TENANT_MEMBER">普通成员</MenuItem>
                <MenuItem value="TENANT_ADMIN">管理员</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {inviteUserMutation.error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {inviteUserMutation.error.message}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small">取消</Button>
        <Button
          onClick={handleInvite}
          variant="contained"
          disabled={inviteUserMutation.isLoading || !formData.name || !formData.email}
          size="small"
        >
          {inviteUserMutation.isLoading ? '邀请中...' : '发送邀请'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// 商户页面骨架屏组件
function MerchantPageSkeleton() {
  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: 1200, mx: 'auto' }}>
      {/* 商户基本信息骨架 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
          <Box sx={{ flex: 1 }}>
            <StatsCardSkeleton />
          </Box>
        </Box>
      </Paper>

      {/* 统计信息骨架 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <StatsCardSkeleton />
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* 用户管理骨架 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ mb: 3 }}>
          <StatsCardSkeleton />
        </Box>
        {Array.from({ length: 5 }).map((_, index) => (
          <ListItemSkeleton key={index} />
        ))}
      </Paper>
    </Box>
  );
}
