"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Avatar,
} from "@mui/material";
import {
  ChevronLeft,
  ChevronRight,
  Today,
  Warning,
  Error,
  Info,
  CheckCircle,
  FilterList,
  Assessment,
  Schedule,
  EventAvailable,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { CalendarSkeleton } from "~/components/ui/Skeleton";

// 获取当前月份的日期数组
function getCalendarDays(year: number, month: number) {
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - firstDay.getDay());
  
  const days = [];
  const current = new Date(startDate);
  
  for (let i = 0; i < 42; i++) {
    days.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }
  
  return days;
}

// 获取证件到期状态
function getExpiryStatus(expiryDate: Date) {
  const today = new Date();
  const diffTime = expiryDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return { status: "expired", color: "error", days: Math.abs(diffDays) };
  } else if (diffDays <= 7) {
    return { status: "critical", color: "error", days: diffDays };
  } else if (diffDays <= 30) {
    return { status: "warning", color: "warning", days: diffDays };
  } else {
    return { status: "normal", color: "success", days: diffDays };
  }
}

export default function CalendarPage() {
  const { data: session, status } = useSession();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [certTypeFilter, setCertTypeFilter] = useState<string>("all");

  if (status === "loading") {
    return <CalendarPageSkeleton />;
  }

  if (!session) {
    redirect("/auth/signin");
  }

  const tenantId = session.user.currentTenantId;

  if (!tenantId) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error">
          未找到关联的商户，请联系管理员
        </Typography>
      </Box>
    );
  }

  // 获取证件数据 - 日历显示证件记录
  const { data: documentsData, isLoading, error } = api.document.getAll.useQuery({
    tenantId,
    page: 1,
    limit: 100, // API最大限制100条
    search: "",
    certType: certTypeFilter === "all" ? undefined : certTypeFilter,
    status: undefined,
  }, {
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    refetchOnWindowFocus: false,
  });

  // 获取证件类型数据用于筛选
  const { data: documentTypes = [] } = api.documentType.getAll.useQuery(
    { tenantId },
    {
      enabled: !!tenantId,
      staleTime: 10 * 60 * 1000, // 10分钟缓存
    }
  );

  if (isLoading) {
    return <CalendarPageSkeleton />;
  }

  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error">
          加载证件数据失败: {error.message}
        </Typography>
      </Box>
    );
  }

  const documents = documentsData?.documents ?? [];

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const calendarDays = getCalendarDays(year, month);

  // 计算当月统计信息
  const currentMonthStart = new Date(year, month, 1);
  const currentMonthEnd = new Date(year, month + 1, 0);

  const currentMonthDocuments = documents.filter(doc => {
    const expiryDate = new Date(doc.validUntil);
    return expiryDate >= currentMonthStart && expiryDate <= currentMonthEnd;
  });

  const stats = {
    total: currentMonthDocuments.length,
    expired: currentMonthDocuments.filter(doc => {
      const status = getExpiryStatus(new Date(doc.validUntil));
      return status.status === "expired";
    }).length,
    critical: currentMonthDocuments.filter(doc => {
      const status = getExpiryStatus(new Date(doc.validUntil));
      return status.status === "critical";
    }).length,
    warning: currentMonthDocuments.filter(doc => {
      const status = getExpiryStatus(new Date(doc.validUntil));
      return status.status === "warning";
    }).length,
    normal: currentMonthDocuments.filter(doc => {
      const status = getExpiryStatus(new Date(doc.validUntil));
      return status.status === "normal";
    }).length,
  };
  
  const monthNames = [
    "一月", "二月", "三月", "四月", "五月", "六月",
    "七月", "八月", "九月", "十月", "十一月", "十二月"
  ];

  // 按日期分组证件
  const documentsByDate = documents.reduce((acc, doc) => {
    const expiryDate = new Date(doc.validUntil);
    const dateKey = expiryDate.toDateString();
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(doc);
    return acc;
  }, {} as Record<string, typeof documents>);

  const handlePrevMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1));
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const handleDateClick = (date: Date) => {
    const dateKey = date.toDateString();
    const dayDocuments = documentsByDate[dateKey];
    if (dayDocuments && dayDocuments.length > 0) {
      setSelectedDate(date);
      setDialogOpen(true);
    }
  };

  const selectedDateDocuments = selectedDate 
    ? documentsByDate[selectedDate.toDateString()] ?? []
    : [];

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* 统计信息面板 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                <EventAvailable />
              </Avatar>
              <Typography variant="h6" color="primary">
                {stats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                本月到期
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Avatar sx={{ bgcolor: 'error.main', mx: 'auto', mb: 1 }}>
                <Error />
              </Avatar>
              <Typography variant="h6" color="error">
                {stats.expired + stats.critical}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                紧急处理
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 1 }}>
                <Warning />
              </Avatar>
              <Typography variant="h6" color="warning.main">
                {stats.warning}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                需要关注
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                <CheckCircle />
              </Avatar>
              <Typography variant="h6" color="success.main">
                {stats.normal}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                状态正常
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ p: 3 }}>
        {/* 日历头部 */}
        <Box sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-between",
          alignItems: { xs: "stretch", sm: "center" },
          gap: 2,
          mb: 3
        }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <IconButton onClick={handlePrevMonth}>
              <ChevronLeft />
            </IconButton>
            <Typography variant="h5">
              {year}年 {monthNames[month]}
            </Typography>
            <IconButton onClick={handleNextMonth}>
              <ChevronRight />
            </IconButton>
          </Box>

          <Stack direction={{ xs: "column", sm: "row" }} spacing={2}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>证件类型</InputLabel>
              <Select
                value={certTypeFilter}
                label="证件类型"
                onChange={(e) => setCertTypeFilter(e.target.value)}
                startAdornment={<FilterList sx={{ mr: 1, color: 'action.active' }} />}
              >
                <MenuItem value="all">全部类型</MenuItem>
                {documentTypes.map((type) => (
                  <MenuItem key={type.id} value={type.name}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Button
              variant="outlined"
              startIcon={<Today />}
              onClick={handleToday}
              size="small"
            >
              今天
            </Button>
          </Stack>
        </Box>

        {/* 日历网格 */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 1fr)',
          gap: { xs: 0.5, sm: 1 },
          overflow: 'hidden'
        }}>
          {/* 星期标题 */}
          {["日", "一", "二", "三", "四", "五", "六"].map((day) => (
            <Box key={day} sx={{ textAlign: "center", py: 1 }}>
              <Typography
                variant="subtitle2"
                color="text.secondary"
                fontWeight="bold"
                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
              >
                {day}
              </Typography>
            </Box>
          ))}

          {/* 日期格子 */}
          {calendarDays.map((date, index) => {
            const isCurrentMonth = date.getMonth() === month;
            const isToday = date.toDateString() === new Date().toDateString();
            const dateKey = date.toDateString();
            const dayDocuments = documentsByDate[dateKey] ?? [];

            return (
              <Card
                key={index}
                sx={{
                  minHeight: { xs: 60, sm: 80, md: 100 },
                  cursor: dayDocuments.length > 0 ? "pointer" : "default",
                  opacity: isCurrentMonth ? 1 : 0.3,
                  border: isToday ? 2 : 1,
                  borderColor: isToday ? "primary.main" : "divider",
                  backgroundColor: isToday ? "primary.50" : "background.paper",
                  "&:hover": {
                    backgroundColor: dayDocuments.length > 0 ? "action.hover" : (isToday ? "primary.100" : "action.hover"),
                  },
                  transition: "all 0.2s ease-in-out",
                }}
                onClick={() => handleDateClick(date)}
              >
                <CardContent sx={{
                  p: { xs: 0.5, sm: 1 },
                  "&:last-child": { pb: { xs: 0.5, sm: 1 } },
                  height: "100%",
                  display: "flex",
                  flexDirection: "column"
                }}>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: { xs: 0.5, sm: 1 },
                      fontWeight: isToday ? "bold" : "normal",
                      color: isToday ? "primary.main" : "inherit",
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    {date.getDate()}
                  </Typography>

                  {/* 显示证件到期指示器 */}
                  <Box sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: { xs: 0.25, sm: 0.5 },
                    flex: 1,
                    overflow: "hidden"
                  }}>
                    {dayDocuments.slice(0, 3).map((doc, docIndex) => {
                      const status = getExpiryStatus(new Date(doc.validUntil));
                      return (
                        <Chip
                          key={docIndex}
                          size="small"
                          label={doc.customerName}
                          color={status.color as any}
                          sx={{
                            fontSize: { xs: "0.5rem", sm: "0.6rem" },
                            height: { xs: 16, sm: 18 },
                            "& .MuiChip-label": {
                              px: { xs: 0.25, sm: 0.5 },
                            },
                          }}
                        />
                      );
                    })}
                    {dayDocuments.length > 3 && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                          fontSize: { xs: "0.5rem", sm: "0.6rem" },
                          textAlign: "center"
                        }}
                      >
                        +{dayDocuments.length - 3}
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            );
          })}
        </Box>
      </Paper>

      {/* 详情对话框 */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedDate && `${selectedDate.getFullYear()}年${selectedDate.getMonth() + 1}月${selectedDate.getDate()}日 到期证件`}
        </DialogTitle>
        <DialogContent>
          <List>
            {selectedDateDocuments.map((doc, index) => {
              const status = getExpiryStatus(new Date(doc.validUntil));
              const StatusIcon = status.status === "expired" ? Error :
                               status.status === "critical" ? Warning :
                               status.status === "warning" ? Info : CheckCircle;

              return (
                <div key={doc.id}>
                  <ListItem>
                    <ListItemIcon>
                      <StatusIcon color={status.color as any} />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="subtitle1" component="span">
                            {doc.customerName}
                          </Typography>
                          <Chip
                            label={doc.certType}
                            size="small"
                            sx={{ ml: 1 }}
                            color="primary"
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color={status.color}>
                            {status.status === "expired"
                              ? `已过期 ${status.days} 天`
                              : status.status === "critical"
                              ? `${status.days} 天后到期 (紧急)`
                              : status.status === "warning"
                              ? `${status.days} 天后到期 (提醒)`
                              : `${status.days} 天后到期`
                            }
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            到期日期: {new Date(doc.validUntil).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < selectedDateDocuments.length - 1 && <Divider />}
                </div>
              );
            })}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

// 日历页面骨架屏组件
function CalendarPageSkeleton() {
  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <CalendarSkeleton />
    </Box>
  );
}
