"use client";

import { 
  Box, 
  Typography, 
  Card, 
  CardContent, 
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert
} from "@mui/material";
import { 
  Check, 
  Close, 
  AdminPanelSettings,
  SupervisorAccount,
  AccountCircle,
  Security
} from "@mui/icons-material";
import { usePermissions } from "~/hooks/usePermissions";
import { RoleIndicator, PermissionSummary } from "~/app/_components/role-indicator";

export default function PermissionsTestPage() {
  const permissions = usePermissions();

  const permissionTests = [
    {
      name: "系统级权限",
      items: [
        { key: "isSuperAdmin", label: "超级管理员", value: permissions.isSuperAdmin },
        { key: "isTenantAdmin", label: "商户管理员", value: permissions.isTenantAdmin },
        { key: "isTenantMember", label: "商户成员", value: permissions.isTenantMember },
        { key: "canViewAllTenants", label: "查看所有商户", value: permissions.canViewAllTenants },
        { key: "canSwitchTenants", label: "切换商户", value: permissions.canSwitchTenants },
      ]
    },
    {
      name: "商户管理权限",
      items: [
        { key: "canManageTenant", label: "管理商户", value: permissions.canManageTenant },
        { key: "canDeleteTenant", label: "删除商户", value: permissions.canDeleteTenant },
        { key: "canInviteMembers", label: "邀请成员", value: permissions.canInviteMembers },
        { key: "canManageMembers", label: "管理成员", value: permissions.canManageMembers },
        { key: "canUpdateMemberRoles", label: "更新成员角色", value: permissions.canUpdateMemberRoles },
      ]
    },
    {
      name: "功能管理权限",
      items: [
        { key: "canManageDocumentTypes", label: "管理证件类型", value: permissions.canManageDocumentTypes },
        { key: "canManageFields", label: "管理字段", value: permissions.canManageFields },
      ]
    },
    {
      name: "数据操作权限",
      items: [
        { key: "canCreateDocuments", label: "创建证件", value: permissions.canCreateDocuments },
        { key: "canEditDocuments", label: "编辑证件", value: permissions.canEditDocuments },
        { key: "canDeleteDocuments", label: "删除证件", value: permissions.canDeleteDocuments },
        { key: "canViewDocuments", label: "查看证件", value: permissions.canViewDocuments },
      ]
    }
  ];

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* 页面标题 */}
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Typography variant="h4">
          权限测试页面
        </Typography>
        <RoleIndicator />
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        此页面用于测试和展示当前用户的权限设置。在生产环境中应该移除此页面。
      </Alert>

      <Grid container spacing={3}>
        {/* 用户信息卡片 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                当前用户信息
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  系统角色
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                  {permissions.currentUserRole || "未知"}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  商户角色
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                  {permissions.currentTenantRole || "无"}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  当前商户ID
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 'bold', wordBreak: 'break-all' }}>
                  {permissions.currentTenantId || "无"}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />
              <PermissionSummary />
            </CardContent>
          </Card>
        </Grid>

        {/* 权限详情 */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            {permissionTests.map((category) => (
              <Grid item xs={12} sm={6} key={category.name}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {category.name}
                    </Typography>
                    <List dense>
                      {category.items.map((item) => (
                        <ListItem key={item.key} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {item.value ? (
                              <Check color="success" fontSize="small" />
                            ) : (
                              <Close color="error" fontSize="small" />
                            )}
                          </ListItemIcon>
                          <ListItemText 
                            primary={item.label}
                            primaryTypographyProps={{
                              variant: "body2",
                              color: item.value ? "text.primary" : "text.secondary"
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>

      {/* 角色说明 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            角色说明
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Security color="error" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" color="error">
                  超级管理员
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                系统级管理员，可以管理所有商户和用户，拥有最高权限。
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SupervisorAccount color="warning" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" color="warning.main">
                  商户管理员
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                商户内的管理员，可以管理商户内的用户和数据，可以邀请成员。
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AccountCircle color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" color="primary">
                  商户成员
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                普通成员，可以使用基本功能，如查看和编辑证件，但不能管理其他用户。
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}
