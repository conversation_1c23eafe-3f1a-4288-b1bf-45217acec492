"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect, useParams } from "next/navigation";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Alert,
  Divider,
  Switch,
  FormControlLabel,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  Checkbox,
  FormLabel,
} from "@mui/material";
import {
  ArrowBack,
  Edit,
  Delete,
  Add,
  Save,
  Settings,
  People,
  Description,
  Notifications,
  ExpandMore,
  Email,
  Telegram,
  NotificationsActive,
} from "@mui/icons-material";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import { api } from "~/trpc/react";

export default function MerchantSettingsPage() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const tenantId = params.id as string;

  const [basicInfo, setBasicInfo] = useState({
    name: "",
    description: "",
    email: "",
    phone: "",
    address: "",
    isActive: true,
  });
  const [notificationSettings, setNotificationSettings] = useState({
    emailEnabled: true,
    telegramEnabled: false,
    pushEnabled: true,
    documentExpiry: true,
    documentReminder: true,
    reminderDays: [30, 7, 1],
    telegramChatId: "",
  });
  const [isEditing, setIsEditing] = useState(false);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "TENANT_USER" as const,
  });

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取商户详情
  const { data: tenantDetail, refetch, isLoading } = api.simpleTenant.getDetail.useQuery(
    { id: tenantId },
    { enabled: !!tenantId }
  );

  // 更新商户信息
  const updateMutation = api.simpleTenant.update.useMutation({
    onSuccess: () => {
      alert('商户信息更新成功！');
      setIsEditing(false);
      refetch();
    },
    onError: (error) => {
      alert('更新失败: ' + error.message);
    },
  });

  // 删除用户
  const deleteUserMutation = api.user.delete?.useMutation({
    onSuccess: () => {
      alert('用户删除成功！');
      refetch();
    },
    onError: (error) => {
      alert('删除失败: ' + error.message);
    },
  }) || { mutate: () => alert('删除功能开发中'), isPending: false };

  // 添加用户
  const addUserMutation = api.user.create?.useMutation({
    onSuccess: () => {
      alert('用户添加成功！');
      setAddUserDialogOpen(false);
      setNewUser({ name: "", email: "", role: "TENANT_USER" });
      refetch();
    },
    onError: (error) => {
      alert('添加失败: ' + error.message);
    },
  }) || { mutate: () => alert('添加功能开发中'), isPending: false };

  // 初始化表单数据
  useEffect(() => {
    if (tenantDetail) {
      setBasicInfo({
        name: tenantDetail.name,
        description: tenantDetail.description || "",
        email: tenantDetail.email || "",
        phone: tenantDetail.phone || "",
        address: tenantDetail.address || "",
        isActive: tenantDetail.isActive,
      });

      // 初始化通知设置
      const settings = (tenantDetail.notificationSettings as any) || {};
      setNotificationSettings({
        emailEnabled: settings.emailEnabled ?? true,
        telegramEnabled: settings.telegramEnabled ?? false,
        pushEnabled: settings.pushEnabled ?? true,
        documentExpiry: settings.documentExpiry ?? true,
        documentReminder: settings.documentReminder ?? true,
        reminderDays: settings.reminderDays || [30, 7, 1],
        telegramChatId: tenantDetail.telegramChatId || "",
      });
    }
  }, [tenantDetail]);

  const handleSaveBasicInfo = () => {
    updateMutation.mutate({
      id: tenantId,
      ...basicInfo,
    });
  };

  const handleSaveNotificationSettings = () => {
    updateMutation.mutate({
      id: tenantId,
      notificationSettings,
      telegramChatId: notificationSettings.telegramChatId,
    });
  };

  const handleDeleteUser = (userId: string, userName: string) => {
    if (confirm(`确定要删除用户 "${userName}" 吗？`)) {
      deleteUserMutation.mutate({ id: userId });
    }
  };

  const handleAddUser = () => {
    if (!newUser.name || !newUser.email) {
      alert('请填写完整的用户信息');
      return;
    }
    addUserMutation.mutate({
      ...newUser,
      tenantId,
    });
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>加载中...</Typography>
      </Box>
    );
  }

  if (!tenantDetail) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">商户不存在或无权访问</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* 页面标题 */}
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <IconButton onClick={() => router.back()} sx={{ mr: 1 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" component="h1">
          商户设置 - {tenantDetail.name}
        </Typography>
        <Chip 
          label={tenantDetail.isActive ? '活跃' : '禁用'}
          color={tenantDetail.isActive ? 'success' : 'default'}
          size="small"
          sx={{ ml: 2 }}
        />
      </Box>

      <Grid container spacing={3}>
        {/* 基本信息 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">基本信息</Typography>
                <Button
                  startIcon={isEditing ? <Save /> : <Edit />}
                  onClick={isEditing ? handleSaveBasicInfo : () => setIsEditing(true)}
                  disabled={updateMutation.isPending}
                >
                  {isEditing ? '保存' : '编辑'}
                </Button>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="商户名称"
                    value={basicInfo.name}
                    onChange={(e) => setBasicInfo(prev => ({ ...prev, name: e.target.value }))}
                    disabled={!isEditing}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="描述"
                    value={basicInfo.description}
                    onChange={(e) => setBasicInfo(prev => ({ ...prev, description: e.target.value }))}
                    disabled={!isEditing}
                    multiline
                    rows={2}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="邮箱"
                    value={basicInfo.email}
                    onChange={(e) => setBasicInfo(prev => ({ ...prev, email: e.target.value }))}
                    disabled={!isEditing}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="电话"
                    value={basicInfo.phone}
                    onChange={(e) => setBasicInfo(prev => ({ ...prev, phone: e.target.value }))}
                    disabled={!isEditing}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="地址"
                    value={basicInfo.address}
                    onChange={(e) => setBasicInfo(prev => ({ ...prev, address: e.target.value }))}
                    disabled={!isEditing}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={basicInfo.isActive}
                        onChange={(e) => setBasicInfo(prev => ({ ...prev, isActive: e.target.checked }))}
                        disabled={!isEditing}
                      />
                    }
                    label="启用商户"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* 统计信息 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>统计信息</Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {tenantDetail._count?.memberships || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      用户数
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary">
                      {tenantDetail._count?.documents || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      证件数
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {tenantDetail._count?.subscriptions || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      订阅数
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      {tenantDetail._count?.notifications || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      通知数
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="textSecondary">
                创建时间: {dayjs(tenantDetail.createdAt).format("YYYY-MM-DD HH:mm:ss")}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                最后更新: {dayjs(tenantDetail.updatedAt).format("YYYY-MM-DD HH:mm:ss")}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 用户管理 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">用户管理</Typography>
                <Button
                  startIcon={<Add />}
                  onClick={() => setAddUserDialogOpen(true)}
                  variant="outlined"
                  size="small"
                >
                  添加用户
                </Button>
              </Box>

              {tenantDetail.memberships && tenantDetail.memberships.length > 0 ? (
                <List>
                  {tenantDetail.memberships.map((membership: any) => (
                    <ListItem key={membership.id} divider>
                      <ListItemText
                        primary={membership.user.name}
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              邮箱: {membership.user.email}
                            </Typography>
                            <Typography variant="body2">
                              角色: {membership.role} | 
                              加入时间: {dayjs(membership.createdAt).format("YYYY-MM-DD")}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleDeleteUser(membership.user.id, membership.user.name)}
                          disabled={deleteUserMutation.isPending}
                        >
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Alert severity="info">暂无用户</Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 通知设置 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Notifications sx={{ mr: 2, color: "primary.main" }} />
                <Typography variant="h6">通知设置</Typography>
              </Box>

              <Grid container spacing={3}>
                {/* 通知渠道设置 */}
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      <NotificationsActive sx={{ mr: 1, verticalAlign: "middle" }} />
                      通知渠道
                    </Typography>

                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={notificationSettings.emailEnabled}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              emailEnabled: e.target.checked
                            }))}
                          />
                        }
                        label={
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Email sx={{ mr: 1, fontSize: 20 }} />
                            邮件通知
                          </Box>
                        }
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={notificationSettings.telegramEnabled}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              telegramEnabled: e.target.checked
                            }))}
                          />
                        }
                        label={
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Telegram sx={{ mr: 1, fontSize: 20 }} />
                            Telegram通知
                          </Box>
                        }
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={notificationSettings.pushEnabled}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              pushEnabled: e.target.checked
                            }))}
                          />
                        }
                        label="PWA推送通知"
                      />
                    </FormGroup>

                    {notificationSettings.telegramEnabled && (
                      <TextField
                        fullWidth
                        size="small"
                        label="Telegram Chat ID"
                        value={notificationSettings.telegramChatId}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          telegramChatId: e.target.value
                        }))}
                        sx={{ mt: 2 }}
                        helperText="获取方式：发送消息给 @TLNotification_bot"
                      />
                    )}
                  </Paper>
                </Grid>

                {/* 通知类型和提醒周期 */}
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      通知类型与周期
                    </Typography>

                    <FormGroup sx={{ mb: 2 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={notificationSettings.documentExpiry}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              documentExpiry: e.target.checked
                            }))}
                          />
                        }
                        label="证件过期通知"
                      />

                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={notificationSettings.documentReminder}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              documentReminder: e.target.checked
                            }))}
                          />
                        }
                        label="证件提醒通知"
                      />
                    </FormGroup>

                    <FormLabel component="legend" sx={{ mb: 1 }}>
                      统一提醒周期（天）
                    </FormLabel>
                    <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap", mb: 2 }}>
                      {[1, 3, 7, 15, 30, 60, 90].map((day) => (
                        <Chip
                          key={day}
                          label={`${day}天`}
                          clickable
                          color={notificationSettings.reminderDays.includes(day) ? "primary" : "default"}
                          onClick={() => {
                            const newDays = notificationSettings.reminderDays.includes(day)
                              ? notificationSettings.reminderDays.filter(d => d !== day)
                              : [...notificationSettings.reminderDays, day].sort((a, b) => b - a);
                            setNotificationSettings(prev => ({
                              ...prev,
                              reminderDays: newDays
                            }));
                          }}
                        />
                      ))}
                    </Box>

                    <Alert severity="info" sx={{ fontSize: "0.875rem" }}>
                      系统将在证件到期前的这些天数发送提醒通知。
                      另外，创建证件时设置的自定义提醒时间也会生效。
                    </Alert>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSaveNotificationSettings}
                  disabled={updateMutation.isPending}
                >
                  {updateMutation.isPending ? "保存中..." : "保存通知设置"}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 添加用户对话框 */}
      <Dialog 
        open={addUserDialogOpen} 
        onClose={() => setAddUserDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>添加用户</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="用户名称"
                value={newUser.name}
                onChange={(e) => setNewUser(prev => ({ ...prev, name: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="邮箱"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth size="small">
                <InputLabel>角色</InputLabel>
                <Select
                  value={newUser.role}
                  label="角色"
                  onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value as any }))}
                >
                  <MenuItem value="TENANT_ADMIN">商户管理员</MenuItem>
                  <MenuItem value="TENANT_USER">商户用户</MenuItem>
                  <MenuItem value="TENANT_MEMBER">商户成员</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddUserDialogOpen(false)}>取消</Button>
          <Button 
            onClick={handleAddUser}
            variant="contained"
            disabled={addUserMutation.isPending}
          >
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
