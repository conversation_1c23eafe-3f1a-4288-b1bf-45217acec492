"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Paper,
} from "@mui/material";
import { api } from "~/trpc/react";

export default function TestAPIPage() {
  const { data: session } = useSession();
  const [testResults, setTestResults] = useState<any>(null);
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 测试API调用
  const { data: tenantsData, error: tenantsError, isLoading: tenantsLoading } = api.adminTenant.getAll.useQuery({
    page: 1,
    limit: 50,
  });

  const { data: statsData, error: statsError, isLoading: statsLoading } = api.adminTenant.getStats.useQuery();

  const handleTestAPI = async () => {
    setIsTestingAPI(true);
    try {
      // 收集测试结果
      const results = {
        session: {
          user: session?.user,
          isLoggedIn: !!session,
          isSuperAdmin: session?.user?.role === "SUPER_ADMIN",
        },
        tenantsAPI: {
          loading: tenantsLoading,
          error: tenantsError?.message,
          data: tenantsData,
          tenantCount: tenantsData?.tenants?.length || 0,
        },
        statsAPI: {
          loading: statsLoading,
          error: statsError?.message,
          data: statsData,
        },
      };
      
      setTestResults(results);
    } catch (error) {
      console.error('测试失败:', error);
    } finally {
      setIsTestingAPI(false);
    }
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography variant="h4" gutterBottom>
        API测试页面
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }}>
        这个页面用于测试超级管理员商户管理API是否正常工作
      </Typography>

      {/* 测试按钮 */}
      <Button
        variant="contained"
        onClick={handleTestAPI}
        disabled={isTestingAPI}
        sx={{ mb: 3 }}
      >
        {isTestingAPI ? <CircularProgress size={20} /> : '运行API测试'}
      </Button>

      {/* 实时API状态 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            实时API状态
          </Typography>
          
          <Typography variant="subtitle2">商户列表API:</Typography>
          {tenantsLoading && <Typography color="info.main">加载中...</Typography>}
          {tenantsError && <Alert severity="error" sx={{ mt: 1 }}>错误: {tenantsError.message}</Alert>}
          {tenantsData && (
            <Alert severity="success" sx={{ mt: 1 }}>
              成功加载 {tenantsData.tenants?.length || 0} 个商户
            </Alert>
          )}

          <Typography variant="subtitle2" sx={{ mt: 2 }}>统计API:</Typography>
          {statsLoading && <Typography color="info.main">加载中...</Typography>}
          {statsError && <Alert severity="error" sx={{ mt: 1 }}>错误: {statsError.message}</Alert>}
          {statsData && (
            <Alert severity="success" sx={{ mt: 1 }}>
              统计数据加载成功
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {testResults && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            测试结果
          </Typography>
          
          <Typography variant="subtitle2">用户会话:</Typography>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify(testResults.session, null, 2)}
          </pre>

          <Typography variant="subtitle2" sx={{ mt: 2 }}>商户API:</Typography>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify(testResults.tenantsAPI, null, 2)}
          </pre>

          <Typography variant="subtitle2" sx={{ mt: 2 }}>统计API:</Typography>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify(testResults.statsAPI, null, 2)}
          </pre>
        </Paper>
      )}

      {/* 原始数据显示 */}
      {tenantsData && (
        <Paper sx={{ p: 3, mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            商户数据 (原始)
          </Typography>
          <pre style={{ fontSize: '12px', overflow: 'auto', maxHeight: '400px' }}>
            {JSON.stringify(tenantsData, null, 2)}
          </pre>
        </Paper>
      )}

      {/* 调试信息 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            调试信息
          </Typography>
          <Typography variant="body2">
            • 当前用户: {session?.user?.name} ({session?.user?.email})
          </Typography>
          <Typography variant="body2">
            • 用户角色: {session?.user?.role}
          </Typography>
          <Typography variant="body2">
            • 是否超级管理员: {session?.user?.role === "SUPER_ADMIN" ? "是" : "否"}
          </Typography>
          <Typography variant="body2">
            • 商户API加载状态: {tenantsLoading ? "加载中" : "已完成"}
          </Typography>
          <Typography variant="body2">
            • 统计API加载状态: {statsLoading ? "加载中" : "已完成"}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}
