"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  Alert,
  Card,
  CardContent,
  CardActions,
  Divider,
} from "@mui/material";
import {
  AccountBalance,
  Save,
  Visibility,
  VisibilityOff,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

export default function BankConfigPage() {
  const { data: session } = useSession();
  const [formData, setFormData] = useState({
    bankName: "",
    accountName: "",
    accountNumber: "",
    branchName: "",
    swiftCode: "",
    routingNumber: "",
    notes: "",
  });
  const [showAccountNumber, setShowAccountNumber] = useState(false);

  // 获取银行信息配置
  const { data: bankInfo, refetch, isLoading, error } = api.systemConfig.getBankConfig.useQuery(
    undefined,
    {
      enabled: !!session && session.user?.role === "SUPER_ADMIN",
      retry: false,
    }
  );

  // 更新银行信息
  const updateBankInfoMutation = api.systemConfig.updateBankInfo.useMutation({
    onSuccess: () => {
      alert("银行信息更新成功！");
      refetch();
    },
    onError: (error) => {
      alert("更新失败: " + error.message);
    },
  });

  // 加载现有配置
  useEffect(() => {
    if (bankInfo) {
      setFormData(prev => ({
        bankName: bankInfo.bankName || "",
        accountName: bankInfo.accountName || "",
        accountNumber: bankInfo.accountNumber || "",
        branchName: bankInfo.branchName || "",
        swiftCode: bankInfo.swiftCode || "",
        routingNumber: bankInfo.routingNumber || "",
        notes: bankInfo.notes || "",
      }));
    }
  }, [bankInfo]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSave = () => {
    updateBankInfoMutation.mutate(formData);
  };

  const handleReset = () => {
    if (bankInfo) {
      setFormData({
        bankName: bankInfo.bankName || "",
        accountName: bankInfo.accountName || "",
        accountNumber: bankInfo.accountNumber || "",
        branchName: bankInfo.branchName || "",
        swiftCode: bankInfo.swiftCode || "",
        routingNumber: bankInfo.routingNumber || "",
        notes: bankInfo.notes || "",
      });
    } else {
      setFormData({
        bankName: "",
        accountName: "",
        accountNumber: "",
        branchName: "",
        swiftCode: "",
        routingNumber: "",
        notes: "",
      });
    }
  };

  // 权限检查
  if (session?.user?.role !== "SUPER_ADMIN") {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          只有超级管理员可以访问银行配置页面
        </Alert>
      </Box>
    );
  }

  // 加载状态
  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>加载中...</Typography>
      </Box>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          加载配置失败: {error.message}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <AccountBalance sx={{ mr: 2, color: "primary.main" }} />
        <Typography variant="h4" component="h1">
          收款银行信息配置
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          配置的银行信息将显示在商户订阅套餐的支付确认页面，用于指导商户完成转账付款。
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 配置表单 */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              银行账户信息
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="银行名称"
                  value={formData.bankName}
                  onChange={handleInputChange("bankName")}
                  fullWidth
                  size="small"
                  required
                  placeholder="例：中国工商银行"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="账户名称"
                  value={formData.accountName}
                  onChange={handleInputChange("accountName")}
                  fullWidth
                  size="small"
                  required
                  placeholder="例：某某科技有限公司"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="账户号码"
                  value={formData.accountNumber}
                  onChange={handleInputChange("accountNumber")}
                  fullWidth
                  size="small"
                  required
                  type={showAccountNumber ? "text" : "password"}
                  placeholder="银行账户号码"
                  InputProps={{
                    endAdornment: (
                      <Button
                        size="small"
                        onClick={() => setShowAccountNumber(!showAccountNumber)}
                        sx={{ minWidth: "auto", p: 1 }}
                      >
                        {showAccountNumber ? <VisibilityOff /> : <Visibility />}
                      </Button>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="开户行/支行"
                  value={formData.branchName}
                  onChange={handleInputChange("branchName")}
                  fullWidth
                  size="small"
                  placeholder="例：北京朝阳支行"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="SWIFT代码"
                  value={formData.swiftCode}
                  onChange={handleInputChange("swiftCode")}
                  fullWidth
                  size="small"
                  placeholder="国际转账使用"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="路由号码"
                  value={formData.routingNumber}
                  onChange={handleInputChange("routingNumber")}
                  fullWidth
                  size="small"
                  placeholder="美国银行使用"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="备注说明"
                  value={formData.notes}
                  onChange={handleInputChange("notes")}
                  fullWidth
                  size="small"
                  multiline
                  rows={3}
                  placeholder="转账时的特殊说明或注意事项"
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: "flex", gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Save />}
                onClick={handleSave}
                disabled={updateBankInfoMutation.isLoading || !formData.bankName || !formData.accountName || !formData.accountNumber}
              >
                {updateBankInfoMutation.isLoading ? "保存中..." : "保存配置"}
              </Button>
              <Button
                variant="outlined"
                onClick={handleReset}
              >
                重置
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* 预览效果 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                支付页面预览
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              {formData.bankName ? (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    收款银行信息
                  </Typography>
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">银行名称</Typography>
                    <Typography variant="body1">{formData.bankName}</Typography>
                  </Box>
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">账户名称</Typography>
                    <Typography variant="body1">{formData.accountName}</Typography>
                  </Box>
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">账户号码</Typography>
                    <Typography variant="body1" sx={{ fontFamily: "monospace" }}>
                      {formData.accountNumber || "未设置"}
                    </Typography>
                  </Box>
                  {formData.branchName && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">开户行</Typography>
                      <Typography variant="body1">{formData.branchName}</Typography>
                    </Box>
                  )}
                  {formData.notes && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">备注</Typography>
                      <Typography variant="body2">{formData.notes}</Typography>
                    </Box>
                  )}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  请填写银行信息以查看预览效果
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
