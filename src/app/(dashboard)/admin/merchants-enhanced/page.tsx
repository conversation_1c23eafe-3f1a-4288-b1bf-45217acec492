"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Tooltip,
  InputAdornment,
  Pagination,
  Menu,
  MenuItem as MenuItemComponent,
} from "@mui/material";
import {
  Add,
  Search,
  FilterList,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  MoreVert,
  Business,
  People,
  AttachMoney,
  TrendingUp,
  Assessment,
  Settings,
  Notifications,
} from "@mui/icons-material";
import { Checkbox } from "@mui/material";
import dayjs from "dayjs";
import { api } from "~/trpc/react";
import { StatsCardSkeleton, TableRowSkeleton } from "~/components/ui/Skeleton";

const TENANT_TYPE_MAP = {
  FREE: { label: "免费版", color: "default" as const },
  BASIC: { label: "基础版", color: "primary" as const },
  PRO: { label: "专业版", color: "secondary" as const },
  ENTERPRISE: { label: "企业版", color: "success" as const },
  PERSONAL: { label: "个人版", color: "info" as const },
};

interface TenantFormData {
  id?: string;
  name: string;
  description: string;
  type: "FREE" | "BASIC" | "PRO" | "ENTERPRISE" | "PERSONAL";
  isActive: boolean;
  email?: string;
  phone?: string;
  address?: string;
}

const defaultFormData: TenantFormData = {
  name: "",
  description: "",
  type: "FREE",
  isActive: true,
  email: "",
  phone: "",
  address: "",
};

export default function EnhancedMerchantsPage() {
  const { data: session } = useSession();
  const utils = api.useUtils();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    search: "",
    type: "",
    isActive: "",
  });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState<TenantFormData>(defaultFormData);
  const [isEditing, setIsEditing] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTenant, setSelectedTenant] = useState<any>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedTenantIds, setSelectedTenantIds] = useState<string[]>([]);
  const [batchActionDialogOpen, setBatchActionDialogOpen] = useState(false);
  const [notificationDialogOpen, setNotificationDialogOpen] = useState(false);

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取商户列表 - 使用adminTenant API
  const { data: tenantsData, refetch, isLoading } = api.adminTenant.getAll.useQuery({
    page: page,
    limit: 20,
    search: filters.search,
    type: filters.type && ["FREE", "BASIC", "PRO", "ENTERPRISE"].includes(filters.type)
      ? filters.type as "FREE" | "BASIC" | "PRO" | "ENTERPRISE"
      : undefined,
    isActive: filters.isActive ? filters.isActive === "true" : undefined,
  });

  // 获取系统统计 - 使用adminTenant API
  const { data: systemStats } = api.adminTenant.getStats.useQuery();

  // 创建/更新商户 - 使用adminTenant API
  const createMutation = api.adminTenant.create.useMutation({
    onSuccess: (result) => {
      console.log('创建成功:', result);
      alert('商户创建成功！');
      refetch();
      handleCloseDialog();
    },
    onError: (error) => {
      console.error('创建失败:', error);
      alert('创建失败: ' + error.message);
    },
  });

  const updateMutation = api.adminTenant.update.useMutation({
    onSuccess: (result) => {
      console.log('更新成功:', result);
      alert('商户更新成功！');
      refetch();
      handleCloseDialog();
    },
    onError: (error) => {
      console.error('更新失败:', error);
      alert('更新失败: ' + error.message);
    },
  });

  // 删除商户
  const deleteMutation = api.adminTenant.delete.useMutation({
    onSuccess: (result) => {
      console.log('删除成功:', result);
      alert('商户删除成功！');
      refetch();
    },
    onError: (error) => {
      console.error('删除失败:', error);
      alert('删除失败: ' + error.message);
    },
  });

  // 切换商户状态 - 使用adminTenant API
  const toggleStatusMutation = api.adminTenant.toggleStatus.useMutation({
    onSuccess: (result) => {
      console.log('状态切换成功:', result);
      refetch();
    },
    onError: (error) => {
      console.error('状态切换失败:', error);
      alert('状态切换失败: ' + error.message);
    },
  });



  const handleOpenDialog = (tenant?: any) => {
    if (tenant) {
      setFormData({
        id: tenant.id,
        name: tenant.name,
        description: tenant.description || "",
        type: tenant.type,
        isActive: tenant.isActive,
        email: tenant.email || "",
        phone: tenant.phone || "",
        address: tenant.address || "",
      });
      setIsEditing(true);
    } else {
      setFormData(defaultFormData);
      setIsEditing(false);
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setFormData(defaultFormData);
    setIsEditing(false);
  };

  const handleSubmit = () => {
    // 基本验证
    if (!formData.name.trim()) {
      alert('请输入商户名称');
      return;
    }

    console.log('提交表单数据:', formData);

    if (isEditing && formData.id) {
      console.log('更新商户:', formData);
      const updateData = {
        ...formData,
        id: formData.id,
        type: formData.type === "PERSONAL" ? "FREE" : formData.type as "FREE" | "BASIC" | "PRO" | "ENTERPRISE",
      };
      updateMutation.mutate(updateData);
    } else {
      const { id, ...createData } = formData;
      const createDataWithValidType = {
        ...createData,
        type: createData.type === "PERSONAL" ? "FREE" : createData.type as "FREE" | "BASIC" | "PRO" | "ENTERPRISE",
      };
      console.log('创建商户:', createDataWithValidType);
      createMutation.mutate(createDataWithValidType);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, tenant: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedTenant(tenant);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTenant(null);
  };



  const handleDelete = (tenant: any) => {
    setSelectedTenant(tenant);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleConfirmDelete = () => {
    if (selectedTenant) {
      deleteMutation.mutate({ id: selectedTenant.id });
    }
    setDeleteDialogOpen(false);
    setSelectedTenant(null);
  };

  const handleViewDetail = (tenant: any) => {
    // 先关闭菜单
    setAnchorEl(null);

    // 然后设置商户数据并打开详情对话框
    setSelectedTenant(tenant);
    setDetailDialogOpen(true);
  };

  const handleSelectTenant = (tenantId: string, checked: boolean) => {
    if (checked) {
      setSelectedTenantIds(prev => [...prev, tenantId]);
    } else {
      setSelectedTenantIds(prev => prev.filter(id => id !== tenantId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTenantIds(tenants.map(t => t.id));
    } else {
      setSelectedTenantIds([]);
    }
  };

  const handleBatchAction = (action: 'enable' | 'disable' | 'delete') => {
    if (selectedTenantIds.length === 0) {
      alert('请先选择要操作的商户');
      return;
    }

    setBatchActionDialogOpen(true);
  };

  const handleToggleStatus = (tenantId: string, isActive: boolean) => {
    console.log('切换状态:', { tenantId, isActive });
    toggleStatusMutation.mutate({ id: tenantId, isActive });
  };

  const handleNotificationSettings = () => {
    setNotificationDialogOpen(true);
    handleMenuClose();
  };

  // 商户列表（简单API返回格式）
  const tenants = tenantsData?.tenants || [];

  // 添加错误处理
  const hasError = !isLoading && !tenantsData;
  const isEmpty = !isLoading && tenants.length === 0;

  if (isLoading) {
    return (
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Typography variant="h4" gutterBottom>
          商户管理
        </Typography>

        {/* 统计卡片骨架 */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <StatsCardSkeleton />
            </Grid>
          ))}
        </Grid>

        {/* 表格骨架 */}
        <Paper>
          <Box sx={{ p: 2 }}>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRowSkeleton key={index} columns={6} />
            ))}
          </Box>
        </Paper>
      </Box>
    );
  }

  // 添加调试信息
  console.log('商户管理页面调试信息:', {
    isLoading,
    tenantsData,
    systemStats,
    session: session?.user,
    tenants: tenants.length,
    mutations: {
      create: { isPending: createMutation.isPending, error: createMutation.error },
      update: { isPending: updateMutation.isPending, error: updateMutation.error },
      delete: { isPending: deleteMutation.isPending, error: deleteMutation.error },
      toggleStatus: { isPending: toggleStatusMutation.isPending, error: toggleStatusMutation.error },
    }
  });

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* 页面标题 */}
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Typography variant="h4" component="h1">
          商户管理
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {selectedTenantIds.length > 0 && (
            <>
              <Button
                variant="outlined"
                size="small"
                onClick={() => handleBatchAction('enable')}
              >
                批量启用 ({selectedTenantIds.length})
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={() => handleBatchAction('disable')}
              >
                批量禁用 ({selectedTenantIds.length})
              </Button>
              <Button
                variant="outlined"
                color="error"
                size="small"
                onClick={() => handleBatchAction('delete')}
              >
                批量删除 ({selectedTenantIds.length})
              </Button>
            </>
          )}
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            创建商户
          </Button>
        </Box>
      </Box>

      {/* 错误提示 */}
      {(createMutation.error || updateMutation.error || deleteMutation.error) && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {createMutation.error?.message || 
           updateMutation.error?.message || 
           deleteMutation.error?.message}
        </Alert>
      )}

      {/* 统计卡片 */}
      {systemStats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      总商户数
                    </Typography>
                    <Typography variant="h4">
                      {systemStats.totalTenants}
                    </Typography>
                  </Box>
                  <Business color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      活跃商户
                    </Typography>
                    <Typography variant="h4">
                      {systemStats.activeTenants}
                    </Typography>
                  </Box>
                  <TrendingUp color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      总用户数
                    </Typography>
                    <Typography variant="h4">
                      {systemStats.totalUsers}
                    </Typography>
                  </Box>
                  <People color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      系统活跃度
                    </Typography>
                    <Typography variant="h4">
                      {systemStats.activityScore}%
                    </Typography>
                  </Box>
                  <Assessment color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* 筛选器 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="搜索商户名称"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>商户类型</InputLabel>
              <Select
                value={filters.type}
                label="商户类型"
                onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              >
                <MenuItem value="">全部</MenuItem>
                {Object.entries(TENANT_TYPE_MAP).map(([key, value]) => (
                  <MenuItem key={key} value={key}>
                    {value.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>状态</InputLabel>
              <Select
                value={filters.isActive}
                label="状态"
                onChange={(e) => setFilters(prev => ({ ...prev, isActive: e.target.value }))}
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="true">活跃</MenuItem>
                <MenuItem value="false">禁用</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              fullWidth
            >
              筛选
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 错误状态 */}
      {hasError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          加载商户数据失败，请刷新页面重试
        </Alert>
      )}

      {/* 空状态 */}
      {isEmpty && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            暂无商户数据
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            系统中还没有任何商户，点击上方按钮创建第一个商户
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            创建商户
          </Button>
        </Paper>
      )}

      {/* 商户列表 */}
      {!hasError && !isEmpty && (
        <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selectedTenantIds.length === tenants.length && tenants.length > 0}
                  indeterminate={selectedTenantIds.length > 0 && selectedTenantIds.length < tenants.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </TableCell>
              <TableCell>商户名称</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>用户数</TableCell>
              <TableCell>证件数</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell align="right">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tenants.map((tenant) => (
              <TableRow key={tenant.id}>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedTenantIds.includes(tenant.id)}
                    onChange={(e) => handleSelectTenant(tenant.id, e.target.checked)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">{tenant.name}</Typography>
                    {tenant.description && (
                      <Typography variant="body2" color="textSecondary">
                        {tenant.description}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={TENANT_TYPE_MAP[tenant.type as keyof typeof TENANT_TYPE_MAP]?.label || tenant.type}
                    size="small"
                    color={TENANT_TYPE_MAP[tenant.type as keyof typeof TENANT_TYPE_MAP]?.color || 'default'}
                  />
                </TableCell>
                <TableCell>0</TableCell>
                <TableCell>0</TableCell>
                <TableCell>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={tenant.isActive}
                        onChange={(e) => handleToggleStatus(tenant.id, e.target.checked)}
                        size="small"
                      />
                    }
                    label={tenant.isActive ? "活跃" : "禁用"}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {dayjs(tenant.createdAt).format("YYYY-MM-DD")}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, tenant)}
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      )}

      {/* 操作菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >

        <MenuItemComponent onClick={() => handleViewDetail(selectedTenant)}>
          <Visibility sx={{ mr: 1 }} />
          查看详情
        </MenuItemComponent>
        <MenuItemComponent onClick={() => {
          handleOpenDialog(selectedTenant);
          handleMenuClose();
        }}>
          <Edit sx={{ mr: 1 }} />
          编辑商户
        </MenuItemComponent>

        <MenuItemComponent onClick={handleNotificationSettings}>
          <Notifications sx={{ mr: 1 }} />
          通知设置
        </MenuItemComponent>
        <MenuItemComponent
          onClick={() => handleDelete(selectedTenant)}
          sx={{ color: "error.main" }}
        >
          <Delete sx={{ mr: 1 }} />
          删除商户
        </MenuItemComponent>
      </Menu>

      {/* 创建/编辑对话框 */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {isEditing ? '编辑商户' : '创建商户'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="商户名称"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                size="small"
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth size="small">
                <InputLabel>商户类型</InputLabel>
                <Select
                  value={formData.type}
                  label="商户类型"
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                >
                  {Object.entries(TENANT_TYPE_MAP).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {value.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="描述"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                size="small"
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="联系邮箱"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="联系电话"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="地址"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label="启用商户"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button 
            onClick={handleSubmit}
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditing ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>确认删除商户</DialogTitle>
        <DialogContent>
          {selectedTenant && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                您确定要删除商户 <strong>"{selectedTenant.name}"</strong> 吗？
              </Typography>
              <Alert severity="warning" sx={{ mb: 2 }}>
                此操作不可恢复！删除后该商户的所有数据都将丢失。
              </Alert>
              <Typography variant="body2" color="textSecondary">
                商户信息：
              </Typography>
              <Typography variant="body2">• 名称: {selectedTenant.name}</Typography>
              <Typography variant="body2">• 类型: {TENANT_TYPE_MAP[selectedTenant.type as keyof typeof TENANT_TYPE_MAP]?.label}</Typography>
              <Typography variant="body2">• 状态: {selectedTenant.isActive ? '活跃' : '禁用'}</Typography>
              {selectedTenant.email && (
                <Typography variant="body2">• 邮箱: {selectedTenant.email}</Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? '删除中...' : '确认删除'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 商户详情对话框 */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>商户详情</DialogTitle>
        <DialogContent>
          {selectedTenant && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  商户名称
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedTenant.name}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  商户类型
                </Typography>
                <Chip
                  label={TENANT_TYPE_MAP[selectedTenant.type as keyof typeof TENANT_TYPE_MAP]?.label || selectedTenant.type}
                  size="small"
                  color={TENANT_TYPE_MAP[selectedTenant.type as keyof typeof TENANT_TYPE_MAP]?.color || 'default'}
                  sx={{ mb: 2 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  状态
                </Typography>
                <Chip
                  label={selectedTenant.isActive ? '活跃' : '禁用'}
                  size="small"
                  color={selectedTenant.isActive ? 'success' : 'default'}
                  sx={{ mb: 2 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  创建时间
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {dayjs(selectedTenant.createdAt).format("YYYY-MM-DD HH:mm:ss")}
                </Typography>
              </Grid>

              {selectedTenant.description && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    描述
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTenant.description}
                  </Typography>
                </Grid>
              )}

              {selectedTenant.email && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    联系邮箱
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTenant.email}
                  </Typography>
                </Grid>
              )}

              {selectedTenant.phone && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    联系电话
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTenant.phone}
                  </Typography>
                </Grid>
              )}

              {selectedTenant.address && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    地址
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTenant.address}
                  </Typography>
                </Grid>
              )}

              {selectedTenant.website && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    网站
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <a href={selectedTenant.website} target="_blank" rel="noopener noreferrer">
                      {selectedTenant.website}
                    </a>
                  </Typography>
                </Grid>
              )}

              {/* 统计信息 */}
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                  统计信息
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="textSecondary">用户数</Typography>
                      <Typography variant="h6">{selectedTenant._count?.memberships || 0}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="textSecondary">证件数</Typography>
                      <Typography variant="h6">{selectedTenant._count?.documents || 0}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="textSecondary">订阅数</Typography>
                      <Typography variant="h6">{selectedTenant._count?.subscriptions || 0}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="textSecondary">通知数</Typography>
                      <Typography variant="h6">{selectedTenant._count?.notifications || 0}</Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* 用户列表 */}
              {selectedTenant.memberships && selectedTenant.memberships.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                    用户列表
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    {selectedTenant.memberships.map((membership: any, index: number) => (
                      <Box key={membership.id} sx={{ mb: index < selectedTenant.memberships.length - 1 ? 1 : 0 }}>
                        <Typography variant="body2">
                          <strong>{membership.user.name}</strong> ({membership.user.email})
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          角色: {membership.role} | 加入时间: {dayjs(membership.createdAt).format("YYYY-MM-DD")}
                        </Typography>
                      </Box>
                    ))}
                  </Paper>
                </Grid>
              )}

              {/* 订阅信息 */}
              {selectedTenant.subscriptions && selectedTenant.subscriptions.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                    订阅信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    {selectedTenant.subscriptions.map((subscription: any, index: number) => (
                      <Box key={subscription.id} sx={{ mb: index < selectedTenant.subscriptions.length - 1 ? 2 : 0 }}>
                        <Typography variant="body2">
                          <strong>{subscription.plan?.name || '未知套餐'}</strong>
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          状态: {subscription.status} |
                          价格: ¥{subscription.plan?.price || 0}/{subscription.plan?.billingCycle || 'month'} |
                          创建: {dayjs(subscription.createdAt).format("YYYY-MM-DD")}
                        </Typography>
                        {subscription.expiresAt && (
                          <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                            到期: {dayjs(subscription.expiresAt).format("YYYY-MM-DD")}
                          </Typography>
                        )}
                      </Box>
                    ))}
                  </Paper>
                </Grid>
              )}

              {/* 管理员信息 */}
              {selectedTenant.adminUser && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                    管理员信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body2">
                      <strong>{selectedTenant.adminUser.name}</strong> ({selectedTenant.adminUser.email})
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      用户ID: {selectedTenant.adminUser.id} |
                      注册时间: {dayjs(selectedTenant.adminUser.createdAt).format("YYYY-MM-DD")}
                    </Typography>
                  </Paper>
                </Grid>
              )}

              {/* 当前订阅 */}
              {selectedTenant.currentSubscription && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                    当前订阅
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body2">
                      <strong>{selectedTenant.currentSubscription.plan?.name || '未知套餐'}</strong>
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      状态: {selectedTenant.currentSubscription.status} |
                      价格: ¥{selectedTenant.currentSubscription.plan?.price || 0}/{selectedTenant.currentSubscription.plan?.billingCycle || 'month'}
                    </Typography>
                    {selectedTenant.currentSubscription.expiresAt && (
                      <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                        到期: {dayjs(selectedTenant.currentSubscription.expiresAt).format("YYYY-MM-DD")}
                      </Typography>
                    )}
                  </Paper>
                </Grid>
              )}

              {/* 通知设置信息 */}
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                  通知设置
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Telegram ID</Typography>
                      <Typography variant="body1">
                        {selectedTenant.telegramChatId || '未设置'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">提醒周期</Typography>
                      <Typography variant="body1">
                        {selectedTenant.notificationSettings?.reminderDays?.join('、') || '30、7、1'} 天
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>通知渠道</Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {selectedTenant.notificationSettings?.emailEnabled && (
                          <Chip label="邮件" size="small" color="primary" />
                        )}
                        {selectedTenant.notificationSettings?.telegramEnabled && (
                          <Chip label="Telegram" size="small" color="info" />
                        )}
                        {selectedTenant.notificationSettings?.pushEnabled && (
                          <Chip label="推送" size="small" color="success" />
                        )}
                        {!selectedTenant.notificationSettings?.emailEnabled &&
                         !selectedTenant.notificationSettings?.telegramEnabled &&
                         !selectedTenant.notificationSettings?.pushEnabled && (
                          <Chip label="未配置通知渠道" size="small" color="default" />
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* 证件统计 */}
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
                  证件统计
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="body2">
                    总证件数: <strong>{selectedTenant._count?.documents || 0}</strong>
                  </Typography>
                  <Typography variant="body2">
                    总通知数: <strong>{selectedTenant._count?.notifications || 0}</strong>
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>关闭</Button>
          <Button
            variant="contained"
            onClick={() => {
              setDetailDialogOpen(false);
              handleOpenDialog(selectedTenant);
            }}
          >
            编辑商户
          </Button>
        </DialogActions>
      </Dialog>

      {/* 通知设置对话框 */}
      <NotificationSettingsDialog
        open={notificationDialogOpen}
        tenant={selectedTenant}
        onClose={() => setNotificationDialogOpen(false)}
      />
    </Box>
  );
}

// 通知设置对话框组件
function NotificationSettingsDialog({ open, tenant, onClose }: any) {
  const [config, setConfig] = useState<any>({
    telegramChatId: "",
    reminderDays: [30, 7, 1],
    emailEnabled: true,
    telegramEnabled: false,
    pushEnabled: true,
  });

  // 获取商户通知配置
  const { data: tenantConfig, refetch } = api.notificationConfig.getConfig.useQuery(
    { tenantId: tenant?.id },
    { enabled: !!tenant?.id }
  );

  // 更新商户配置mutation
  const updateConfigMutation = api.notificationConfig.updateConfig.useMutation({
    onSuccess: () => {
      alert("通知设置更新成功！");
      refetch();
      onClose();
    },
    onError: (error) => {
      alert("更新失败: " + error.message);
    },
  });

  // 测试Telegram Bot
  const testTelegramMutation = api.notificationConfig.testNotification.useMutation();

  useEffect(() => {
    if (tenantConfig) {
      setConfig({
        telegramChatId: tenantConfig.telegramChatId || "",
        reminderDays: tenantConfig.notificationSettings?.reminderDays || [30, 7, 1],
        emailEnabled: tenantConfig.notificationSettings?.emailEnabled ?? true,
        telegramEnabled: tenantConfig.notificationSettings?.telegramEnabled ?? false,
        pushEnabled: tenantConfig.notificationSettings?.pushEnabled ?? true,
      });
    } else if (tenant) {
      // 如果没有获取到配置，使用商户基本信息中的通知设置
      setConfig({
        telegramChatId: tenant.telegramChatId || "",
        reminderDays: tenant.notificationSettings?.reminderDays || [30, 7, 1],
        emailEnabled: tenant.notificationSettings?.emailEnabled ?? true,
        telegramEnabled: tenant.notificationSettings?.telegramEnabled ?? false,
        pushEnabled: tenant.notificationSettings?.pushEnabled ?? true,
      });
    }
  }, [tenantConfig, tenant]);

  const handleSave = () => {
    updateConfigMutation.mutate({
      tenantId: tenant.id,
      telegramChatId: config.telegramChatId,
      reminderDays: config.reminderDays,
      emailEnabled: config.emailEnabled,
      telegramEnabled: config.telegramEnabled,
      pushEnabled: config.pushEnabled,
    });
  };

  const handleTestTelegram = () => {
    if (config.telegramChatId) {
      testTelegramMutation.mutate({
        tenantId: tenant.id,
        channels: ["TELEGRAM"],
        message: "测试消息",
      });
    }
  };

  if (!tenant) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Notifications />
          通知设置 - {tenant.name}
        </Box>
      </DialogTitle>
      <DialogContent>
        {/* 当前设置概览 */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            当前通知设置概览
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
            {config.emailEnabled && <Chip label="邮件" size="small" color="primary" />}
            {config.telegramEnabled && <Chip label="Telegram" size="small" color="info" />}
            {config.pushEnabled && <Chip label="推送" size="small" color="success" />}
            {!config.emailEnabled && !config.telegramEnabled && !config.pushEnabled && (
              <Chip label="未启用任何通知渠道" size="small" color="default" />
            )}
          </Box>
          <Typography variant="body2">
            Telegram ID: {config.telegramChatId || '未设置'} |
            提醒周期: {config.reminderDays?.join('、') || '30、7、1'} 天
          </Typography>
        </Alert>

        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Telegram配置 */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Telegram通知配置
            </Typography>
            <TextField
              fullWidth
              label="Telegram接收ID"
              value={config.telegramChatId}
              onChange={(e) => setConfig({ ...config, telegramChatId: e.target.value })}
              size="small"
              helperText="群组ID、频道ID或用户ID，获取方式：https://t.me/TLNotification_bot"
              sx={{ mb: 2 }}
            />
            <Button
              variant="outlined"
              size="small"
              onClick={handleTestTelegram}
              disabled={!config.telegramChatId || testTelegramMutation.isPending}
            >
              {testTelegramMutation.isPending ? "测试中..." : "测试Telegram"}
            </Button>
            {testTelegramMutation.data && (
              <Alert
                severity={testTelegramMutation.data.success ? "success" : "error"}
                sx={{ mt: 2 }}
              >
                {testTelegramMutation.data.message || "测试完成"}
              </Alert>
            )}
          </Grid>

          {/* 通知渠道开关 */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              通知渠道
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={config.emailEnabled}
                    onChange={(e) => setConfig({ ...config, emailEnabled: e.target.checked })}
                  />
                }
                label="邮件通知"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={config.telegramEnabled}
                    onChange={(e) => setConfig({ ...config, telegramEnabled: e.target.checked })}
                  />
                }
                label="Telegram通知"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={config.pushEnabled}
                    onChange={(e) => setConfig({ ...config, pushEnabled: e.target.checked })}
                  />
                }
                label="推送通知"
              />
            </Box>
          </Grid>

          {/* 提醒周期 */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              提醒周期设置
            </Typography>
            <TextField
              fullWidth
              label="提醒周期（天）"
              value={config.reminderDays?.join(',') || "30,7,1"}
              onChange={(e) => {
                const days = e.target.value.split(',').map(d => parseInt(d.trim())).filter(d => !isNaN(d));
                setConfig({ ...config, reminderDays: days });
              }}
              size="small"
              helperText="用逗号分隔，例如: 30,7,1 表示在证件到期前30天、7天、1天发送提醒"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={updateConfigMutation.isPending}
        >
          {updateConfigMutation.isPending ? '保存中...' : '保存设置'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
