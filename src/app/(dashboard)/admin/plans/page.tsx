"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  Stack,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  TrendingUp,
  AttachMoney,
  People,
  Assessment,
  AccountBalance,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { StatsCardSkeleton, TableRowSkeleton } from "~/components/ui/Skeleton";

interface PlanFormData {
  id?: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  billingCycle: "MONTHLY" | "YEARLY" | "TRIAL";
  features: string[];
  limits: {
    maxDocuments: number;
    maxUsers: number;
    maxStorage: number;
    maxNotifications: number;
  };
  isActive: boolean;
  sortOrder: number;
}

const defaultFormData: PlanFormData = {
  name: "",
  description: "",
  price: 0,
  currency: "USD",
  billingCycle: "YEARLY",
  features: [],
  limits: {
    maxDocuments: -1,
    maxUsers: -1,
    maxStorage: -1,
    maxNotifications: -1,
  },
  isActive: true,
  sortOrder: 0,
};

export default function AdminPlansPage() {
  const { data: session } = useSession();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState<PlanFormData>(defaultFormData);
  const [isEditing, setIsEditing] = useState(false);
  const [newFeature, setNewFeature] = useState("");

  // 功能列表操作函数
  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature("");
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取套餐列表
  const { data: plans = [], refetch, isLoading } = api.adminPlan.getAll.useQuery();

  // 获取套餐统计
  const { data: stats } = api.adminPlan.getStats.useQuery();

  // 创建/更新套餐
  const createMutation = api.adminPlan.create.useMutation({
    onSuccess: () => {
      refetch();
      handleCloseDialog();
    },
  });

  const updateMutation = api.adminPlan.update.useMutation({
    onSuccess: () => {
      refetch();
      handleCloseDialog();
    },
  });

  // 删除套餐
  const deleteMutation = api.adminPlan.delete.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  // 切换套餐状态
  const toggleStatusMutation = api.adminPlan.toggleStatus.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleOpenDialog = (plan?: any) => {
    if (plan) {
      setFormData({
        id: plan.id,
        name: plan.name,
        description: plan.description || "",
        price: plan.price,
        currency: plan.currency,
        billingCycle: plan.billingCycle,
        features: plan.features || [],
        limits: plan.limits || defaultFormData.limits,
        isActive: plan.isActive,
        sortOrder: plan.sortOrder,
      });
      setIsEditing(true);
    } else {
      setFormData(defaultFormData);
      setIsEditing(false);
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setFormData(defaultFormData);
    setIsEditing(false);
    setNewFeature("");
  };

  const handleSubmit = () => {
    if (isEditing && formData.id) {
      updateMutation.mutate(formData);
    } else {
      const { id, ...createData } = formData;
      createMutation.mutate(createData);
    }
  };

  const handleDelete = (planId: string) => {
    if (confirm("确定要删除这个套餐吗？")) {
      deleteMutation.mutate({ id: planId });
    }
  };

  const handleToggleStatus = (planId: string, isActive: boolean) => {
    toggleStatusMutation.mutate({ id: planId, isActive });
  };

  if (isLoading) {
    return (
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Typography variant="h4" gutterBottom>
          套餐管理
        </Typography>
        
        {/* 统计卡片骨架 */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <StatsCardSkeleton />
            </Grid>
          ))}
        </Grid>

        {/* 表格骨架 */}
        <Paper>
          <Box sx={{ p: 2 }}>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRowSkeleton key={index} columns={7} />
            ))}
          </Box>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* 页面标题 */}
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Typography variant="h4" component="h1">
          套餐管理
        </Typography>
        <Box sx={{ display: "flex", gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<AccountBalance />}
            href="/admin/bank-config"
          >
            银行配置
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            创建套餐
          </Button>
        </Box>
      </Box>

      {/* 错误提示 */}
      {(createMutation.error || updateMutation.error || deleteMutation.error) && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {createMutation.error?.message || 
           updateMutation.error?.message || 
           deleteMutation.error?.message}
        </Alert>
      )}

      {/* 统计卡片 */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      总套餐数
                    </Typography>
                    <Typography variant="h4">
                      {stats.totalPlans}
                    </Typography>
                  </Box>
                  <Assessment color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      活跃套餐
                    </Typography>
                    <Typography variant="h4">
                      {stats.activePlans}
                    </Typography>
                  </Box>
                  <Visibility color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      总订阅数
                    </Typography>
                    <Typography variant="h4">
                      {stats.totalSubscriptions}
                    </Typography>
                  </Box>
                  <People color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                      平均订阅率
                    </Typography>
                    <Typography variant="h4">
                      {stats.totalPlans > 0 ? Math.round((stats.totalSubscriptions / stats.totalPlans) * 100) / 100 : 0}
                    </Typography>
                  </Box>
                  <TrendingUp color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* 套餐列表 */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>套餐名称</TableCell>
              <TableCell>价格</TableCell>
              <TableCell>计费周期</TableCell>
              <TableCell>订阅数</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>排序</TableCell>
              <TableCell align="right">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {plans.map((plan) => (
              <TableRow key={plan.id}>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">{plan.name}</Typography>
                    {plan.description && (
                      <Typography variant="body2" color="textSecondary">
                        {plan.description}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AttachMoney sx={{ fontSize: 16, mr: 0.5 }} />
                    {plan.price} {plan.currency}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={
                      plan.billingCycle === 'MONTHLY' ? '月付' :
                      plan.billingCycle === 'YEARLY' ? '年付' :
                      plan.billingCycle === 'TRIAL' ? '试用' : plan.billingCycle
                    }
                    size="small"
                    color={
                      plan.billingCycle === 'MONTHLY' ? 'primary' :
                      plan.billingCycle === 'YEARLY' ? 'secondary' :
                      plan.billingCycle === 'TRIAL' ? 'info' : 'default'
                    }
                  />
                </TableCell>
                <TableCell>{plan.subscriptionCount}</TableCell>
                <TableCell>
                  <Chip 
                    label={plan.isActive ? '启用' : '禁用'} 
                    size="small"
                    color={plan.isActive ? 'success' : 'default'}
                  />
                </TableCell>
                <TableCell>{plan.sortOrder}</TableCell>
                <TableCell align="right">
                  <Tooltip title={plan.isActive ? '禁用' : '启用'}>
                    <IconButton
                      size="small"
                      onClick={() => handleToggleStatus(plan.id, !plan.isActive)}
                    >
                      {plan.isActive ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="编辑">
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(plan)}
                    >
                      <Edit />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="删除">
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(plan.id)}
                      disabled={plan.subscriptionCount > 0}
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 创建/编辑对话框 */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {isEditing ? '编辑套餐' : '创建套餐'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="套餐名称"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                size="small"
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="价格"
                type="number"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                size="small"
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="描述"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                size="small"
                multiline
                rows={2}
              />
            </Grid>

            {/* 功能列表编辑 */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                功能列表
              </Typography>
              <Stack spacing={2}>
                {formData.features.map((feature, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                    <TextField
                      fullWidth
                      size="small"
                      value={feature}
                      onChange={(e) => {
                        const newFeatures = [...formData.features];
                        newFeatures[index] = e.target.value;
                        setFormData(prev => ({ ...prev, features: newFeatures }));
                      }}
                      placeholder="输入功能描述"
                    />
                    <IconButton
                      size="small"
                      onClick={() => removeFeature(index)}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                ))}
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    placeholder="添加新功能"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addFeature();
                      }
                    }}
                  />
                  <IconButton
                    size="small"
                    onClick={addFeature}
                    color="primary"
                    disabled={!newFeature.trim()}
                  >
                    <Add />
                  </IconButton>
                </Box>
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth size="small">
                <InputLabel>计费周期</InputLabel>
                <Select
                  value={formData.billingCycle}
                  label="计费周期"
                  onChange={(e) => setFormData(prev => ({ ...prev, billingCycle: e.target.value as any }))}
                >
                  <MenuItem value="TRIAL">试用</MenuItem>
                  <MenuItem value="MONTHLY">月付</MenuItem>
                  <MenuItem value="YEARLY">年付</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="排序"
                type="number"
                value={formData.sortOrder}
                onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: Number(e.target.value) }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label="启用套餐"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button 
            onClick={handleSubmit}
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditing ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
