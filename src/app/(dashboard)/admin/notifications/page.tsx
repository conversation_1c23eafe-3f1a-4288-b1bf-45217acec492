"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  Settings,
  Telegram,
  Email,
  PhoneIphone,
  PlayArrow,
  Edit,
  Save,
  Business,
  Notifications,
  ExpandMore,
  Science,
  CheckCircle,
  Error,
  VpnKey,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

export default function AdminNotificationsPage() {
  const { data: session } = useSession();
  const [systemConfig, setSystemConfig] = useState<any>({});
  const [emailConfig, setEmailConfig] = useState({
    emailProvider: "GMAIL" as const,
    emailFromAddress: "",
    emailFromName: "证件提醒系统",
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    smtpSecure: true,
    emailApiKey: "",
  });
  const [testEmail, setTestEmail] = useState("");
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取系统配置
  const { data: systemNotificationConfig, refetch: refetchSystemConfig } =
    api.systemNotification.getSystemConfig.useQuery();

  // 超级管理员的邮件配置从系统配置中获取
  const currentEmailConfig = systemNotificationConfig;



  // 更新系统配置mutation
  const updateSystemConfigMutation = api.systemNotification.updateSystemConfig.useMutation({
    onSuccess: () => {
      refetchSystemConfig();
    },
  });

  // 生成VAPID密钥mutation
  const generateVapidMutation = api.systemNotification.generateVapidKeys.useMutation({
    onSuccess: (result) => {
      // 自动填入生成的密钥
      setSystemConfig((prev: any) => ({
        ...prev,
        vapidPublicKey: result.publicKey,
        vapidPrivateKey: result.privateKey,
      }));
      alert("VAPID密钥生成成功！请记得保存配置。");
    },
    onError: (error) => {
      alert("生成VAPID密钥失败: " + error.message);
    },
  });

  // Telegram Bot 相关
  const { data: telegramBotStatus, refetch: refetchBotStatus } =
    api.systemNotification.getTelegramBotStatus.useQuery();

  const restartTelegramBotMutation = api.systemNotification.restartTelegramBot.useMutation({
    onSuccess: (data) => {
      if (data.success) {
        alert("Telegram Bot重启成功！");
        refetchBotStatus();
      } else {
        alert(`Telegram Bot重启失败: ${data.message}`);
      }
    },
    onError: (error) => {
      alert(`Telegram Bot重启失败: ${error.message}`);
    },
  });

  // Webhook 设置
  const [webhookInfo, setWebhookInfo] = useState<any>(null);

  const setupWebhook = async () => {
    try {
      const response = await fetch('/api/telegram/setup-webhook', {
        method: 'POST',
      });
      const result = await response.json();

      if (result.success) {
        alert('Webhook设置成功！');
        await getWebhookInfo();
      } else {
        alert(`Webhook设置失败: ${result.message}`);
      }
    } catch (error) {
      alert(`Webhook设置失败: ${error}`);
    }
  };

  const getWebhookInfo = async () => {
    try {
      const response = await fetch('/api/telegram/setup-webhook');
      const result = await response.json();

      if (result.success) {
        setWebhookInfo(result.webhookInfo);
      }
    } catch (error) {
      console.error('获取Webhook信息失败:', error);
    }
  };

  // 页面加载时获取Webhook信息
  useEffect(() => {
    if (systemConfig.telegramBotToken) {
      getWebhookInfo();
    }
  }, [systemConfig.telegramBotToken]);

  // 更新邮件配置mutation - 超级管理员直接更新系统配置
  const updateEmailConfigMutation = api.systemNotification.updateSystemConfig.useMutation({
    onSuccess: () => {
      alert("邮件配置更新成功！");
      refetchSystemConfig();
    },
    onError: (error) => {
      alert("更新失败: " + error.message);
    },
  });

  // 测试邮件发送 - 使用系统级别的测试API
  const testEmailMutation = api.systemNotification.testNotificationConfig.useMutation({
    onSuccess: (result) => {
      setTestResult({ success: true, message: "测试邮件发送成功！" });
    },
    onError: (error) => {
      setTestResult({ success: false, message: "测试失败: " + error.message });
    },
  });

  // 测试Telegram Bot
  const testTelegramMutation = api.systemNotification.testTelegramBot.useMutation();

  // 获取通知统计
  const { data: notificationStats } = api.systemNotification.getNotificationStats.useQuery({
    days: 30,
  });

  useEffect(() => {
    if (systemNotificationConfig) {
      setSystemConfig(systemNotificationConfig);
    }
  }, [systemNotificationConfig]);

  // 初始化邮件配置 - 从系统配置中读取
  useEffect(() => {
    if (currentEmailConfig) {
      setEmailConfig({
        emailProvider: (currentEmailConfig.emailProvider as "GMAIL") ?? "GMAIL",
        emailFromAddress: currentEmailConfig.emailFromAddress ?? "",
        emailFromName: currentEmailConfig.emailFromName ?? "证件提醒系统",
        smtpHost: currentEmailConfig.smtpHost ?? "",
        smtpPort: currentEmailConfig.smtpPort ?? 587,
        smtpUser: currentEmailConfig.smtpUser ?? "",
        smtpPassword: currentEmailConfig.smtpPassword ?? "",
        smtpSecure: currentEmailConfig.smtpSecure ?? true,
        emailApiKey: currentEmailConfig.emailApiKey ?? "",
      });
    }
  }, [currentEmailConfig]);

  const handleSystemConfigChange = (field: string, value: string | boolean | number) => {
    setSystemConfig((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleSaveSystemConfig = () => {
    updateSystemConfigMutation.mutate(systemConfig);
  };

  const handleSaveEmailConfig = () => {
    // 将邮件配置合并到系统配置中保存
    const updatedSystemConfig = {
      ...systemConfig,
      ...emailConfig,
    };
    updateEmailConfigMutation.mutate(updatedSystemConfig);
  };

  const handleTestEmail = () => {
    if (!testEmail) {
      alert("请输入测试邮箱地址");
      return;
    }

    testEmailMutation.mutate({
      type: "EMAIL",
      testData: {
        email: testEmail,
      },
    });
  };

  const handleGenerateVapidKeys = () => {
    if (confirm("生成新的VAPID密钥将替换现有密钥，确定要继续吗？")) {
      generateVapidMutation.mutate();
    }
  };

  const getProviderConfig = () => {
    switch (emailConfig.emailProvider) {
      case "GMAIL":
      default:
        return {
          name: "Gmail SMTP",
          description: "使用Gmail的SMTP服务发送邮件",
          defaultHost: "smtp.gmail.com",
          defaultPort: 587,
          instructions: [
            "1. 开启两步验证",
            "2. 生成应用专用密码",
            "3. 使用Gmail地址和应用专用密码",
          ],
        };
    }
    // 移除不支持的provider选项，只保留GMAIL
    /*
      case "OUTLOOK":
        return {
          name: "Outlook SMTP",
          description: "使用Outlook的SMTP服务发送邮件",
          defaultHost: "smtp-mail.outlook.com",
          defaultPort: 587,
          instructions: [
            "1. 使用Outlook邮箱地址",
            "2. 使用邮箱密码",
            "3. 确保账户安全设置允许SMTP",
          ],
        };
    */
  };



  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography variant="h4" gutterBottom>
        系统通知管理
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        配置系统通知机器人和邮件服务
      </Typography>

      <Grid container spacing={3}>
        {/* 系统配置 */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="系统通知配置"
              subheader="配置通知机器人和服务"
              avatar={<Settings />}
            />
            <CardContent>
              <Grid container spacing={2}>
                {/* 通知方式开关 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    通知方式开关
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.emailEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('emailEnabled', e.target.checked)}
                        />
                      }
                      label="邮件通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.telegramEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('telegramEnabled', e.target.checked)}
                        />
                      }
                      label="Telegram通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.pushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('pushEnabled', e.target.checked)}
                        />
                      }
                      label="PWA推送"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.browserNotificationEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('browserNotificationEnabled', e.target.checked)}
                        />
                      }
                      label="浏览器通知"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Divider />
                </Grid>

                {/* Telegram配置 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Telegram配置
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Telegram Bot Token"
                    value={systemConfig.telegramBotToken || ""}
                    onChange={(e) => handleSystemConfigChange('telegramBotToken', e.target.value)}
                    size="small"
                    type="password"
                    helperText="从 @BotFather 获取的Bot Token"
                    disabled={!systemConfig.telegramEnabled}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Bot名称"
                    value={systemConfig.telegramBotName || ""}
                    onChange={(e) => handleSystemConfigChange('telegramBotName', e.target.value)}
                    size="small"
                    disabled={!systemConfig.telegramEnabled}
                  />
                </Grid>

                {/* Telegram Bot 状态 */}
                <Grid item xs={12}>
                  <Box sx={{ p: 2, bgcolor: 'background.paper', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      🤖 Telegram Bot 状态
                    </Typography>

                    {telegramBotStatus ? (
                      <Box>
                        <Typography variant="body2" color={telegramBotStatus.isRunning ? 'success.main' : 'error.main'}>
                          状态: {telegramBotStatus.isRunning ? '✅ 运行中' : '❌ 未运行'}
                        </Typography>

                        {telegramBotStatus.botInfo && (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="caption" display="block">
                              机器人名称: {telegramBotStatus.botInfo.first_name}
                            </Typography>
                            <Typography variant="caption" display="block">
                              用户名: @{telegramBotStatus.botInfo.username}
                            </Typography>
                            <Typography variant="caption" display="block">
                              ID: {telegramBotStatus.botInfo.id}
                            </Typography>
                          </Box>
                        )}

                        <Box sx={{ mt: 2 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => restartTelegramBotMutation.mutate()}
                            disabled={restartTelegramBotMutation.isPending}
                          >
                            {restartTelegramBotMutation.isPending ? '重启中...' : '重启机器人'}
                          </Button>

                          <Button
                            variant="text"
                            size="small"
                            onClick={() => refetchBotStatus()}
                            sx={{ ml: 1 }}
                          >
                            刷新状态
                          </Button>
                        </Box>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        加载状态中...
                      </Typography>
                    )}

                    {/* Webhook 配置 (Vercel 环境) */}
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        🌐 Webhook 配置 (Vercel/无服务器环境)
                      </Typography>

                      {webhookInfo ? (
                        <Box>
                          <Typography variant="body2" color={webhookInfo.url ? 'success.main' : 'warning.main'}>
                            状态: {webhookInfo.url ? '✅ 已配置' : '⚠️ 未配置'}
                          </Typography>

                          {webhookInfo.url && (
                            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                              Webhook URL: {webhookInfo.url}
                            </Typography>
                          )}

                          <Typography variant="caption" display="block">
                            待处理更新: {webhookInfo.pending_update_count || 0}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          未获取到Webhook信息
                        </Typography>
                      )}

                      <Box sx={{ mt: 2 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={setupWebhook}
                          disabled={!systemConfig.telegramBotToken}
                        >
                          设置Webhook
                        </Button>

                        <Button
                          variant="text"
                          size="small"
                          onClick={getWebhookInfo}
                          sx={{ ml: 1 }}
                        >
                          刷新信息
                        </Button>
                      </Box>

                      <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                        💡 在Vercel等无服务器环境中，使用Webhook代替长轮询
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* PWA推送配置 */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">
                      PWA推送配置
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<VpnKey />}
                      onClick={handleGenerateVapidKeys}
                      disabled={!systemConfig.pushEnabled || generateVapidMutation.isPending}
                    >
                      {generateVapidMutation.isPending ? '生成中...' : '生成VAPID密钥'}
                    </Button>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID公钥"
                    value={systemConfig.vapidPublicKey || ""}
                    onChange={(e) => handleSystemConfigChange('vapidPublicKey', e.target.value)}
                    size="small"
                    disabled={!systemConfig.pushEnabled}
                    helperText="用于PWA推送的VAPID公钥"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID私钥"
                    value={systemConfig.vapidPrivateKey || ""}
                    onChange={(e) => handleSystemConfigChange('vapidPrivateKey', e.target.value)}
                    size="small"
                    type="password"
                    disabled={!systemConfig.pushEnabled}
                    helperText="用于PWA推送的VAPID私钥"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID主题"
                    value={systemConfig.vapidSubject || ""}
                    onChange={(e) => handleSystemConfigChange('vapidSubject', e.target.value)}
                    size="small"
                    disabled={!systemConfig.pushEnabled}
                    helperText="邮箱地址或网站URL"
                  />
                </Grid>

                {/* 系统推送开关 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    推送类型配置
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.systemPushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('systemPushEnabled', e.target.checked)}
                          disabled={!systemConfig.pushEnabled}
                        />
                      }
                      label="系统推送通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.expiryPushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('expiryPushEnabled', e.target.checked)}
                          disabled={!systemConfig.pushEnabled}
                        />
                      }
                      label="证件到期推送"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSaveSystemConfig}
                    disabled={updateSystemConfigMutation.isPending}
                    fullWidth
                  >
                    {updateSystemConfigMutation.isPending ? '保存中...' : '保存配置'}
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* 通知统计 */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="通知统计"
              subheader="最近30天的通知发送情况"
              avatar={<Notifications />}
            />
            <CardContent>
              {notificationStats && (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {notificationStats.totalNotifications}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        总通知数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {notificationStats.emailSent + notificationStats.telegramSent + notificationStats.pushSent}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        成功发送数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="error.main">
                        {notificationStats.totalNotifications - (notificationStats.emailSent + notificationStats.telegramSent + notificationStats.pushSent)}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        失败数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {Math.round(((notificationStats.emailSent + notificationStats.telegramSent + notificationStats.pushSent) / notificationStats.totalNotifications) * 100) || 0}%
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        成功率
                      </Typography>
                    </Paper>
                  </Grid>

                  {/* 按渠道统计 */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      按通知渠道统计
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">邮件</Typography>
                      <Typography variant="body2" color="primary">
                        {notificationStats.emailSent}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Telegram</Typography>
                      <Typography variant="body2" color="primary">
                        {notificationStats.telegramSent}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">推送</Typography>
                      <Typography variant="body2" color="primary">
                        {notificationStats.pushSent}
                      </Typography>
                    </Box>
                  </Grid>


                </Grid>
              )}

              {!notificationStats && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="textSecondary">
                    暂无通知统计数据
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 邮件配置 */}
        <Grid item xs={12}>
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Email sx={{ mr: 2, color: "primary.main" }} />
                <Typography variant="h6">邮件服务配置</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                {/* 基本配置 */}
                <Grid item xs={12} md={8}>
                  <Paper sx={{ p: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>邮件服务商</InputLabel>
                          <Select
                            value={emailConfig.emailProvider}
                            label="邮件服务商"
                            onChange={(e) => {
                              const providerConfig = getProviderConfig();
                              setEmailConfig({
                                ...emailConfig,
                                emailProvider: e.target.value as any,
                                smtpHost: providerConfig.defaultHost,
                                smtpPort: providerConfig.defaultPort,
                              });
                            }}
                          >
                            <MenuItem value="GMAIL">Gmail SMTP</MenuItem>
                            <MenuItem value="OUTLOOK">Outlook SMTP</MenuItem>
                            <MenuItem value="SENDGRID">SendGrid API</MenuItem>
                            <MenuItem value="SMTP">自定义SMTP</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label="发送者邮箱"
                          value={emailConfig.emailFromAddress}
                          onChange={(e) => setEmailConfig({ ...emailConfig, emailFromAddress: e.target.value })}
                          type="email"
                          required
                        />
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label="发送者名称"
                          value={emailConfig.emailFromName}
                          onChange={(e) => setEmailConfig({ ...emailConfig, emailFromName: e.target.value })}
                        />
                      </Grid>

                      {/* SMTP配置 - 只支持GMAIL */}
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              size="small"
                              label="SMTP用户名"
                              value={emailConfig.smtpUser}
                              onChange={(e) => setEmailConfig({ ...emailConfig, smtpUser: e.target.value })}
                              placeholder={emailConfig.emailProvider === "GMAIL" ? "Gmail邮箱地址" : ""}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              size="small"
                              label="SMTP密码"
                              value={emailConfig.smtpPassword}
                              onChange={(e) => setEmailConfig({ ...emailConfig, smtpPassword: e.target.value })}
                              type="password"
                              placeholder={emailConfig.emailProvider === "GMAIL" ? "应用专用密码" : ""}
                            />
                          </Grid>

                          {/* SMTP配置字段 */}
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="SMTP主机"
                                  value={emailConfig.smtpHost}
                                  onChange={(e) => setEmailConfig({ ...emailConfig, smtpHost: e.target.value })}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="SMTP端口"
                                  value={emailConfig.smtpPort}
                                  onChange={(e) => setEmailConfig({ ...emailConfig, smtpPort: parseInt(e.target.value) || 587 })}
                                  type="number"
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={emailConfig.smtpSecure}
                                      onChange={(e) => setEmailConfig({ ...emailConfig, smtpSecure: e.target.checked })}
                                    />
                                  }
                                  label="使用SSL/TLS加密"
                                />
                              </Grid>

                    </Grid>

                    <Box sx={{ mt: 3, display: "flex", gap: 2 }}>
                      <Button
                        variant="contained"
                        startIcon={<Save />}
                        onClick={handleSaveEmailConfig}
                        disabled={updateEmailConfigMutation.isPending}
                      >
                        {updateEmailConfigMutation.isPending ? "保存中..." : "保存邮件配置"}
                      </Button>
                    </Box>
                  </Paper>
                </Grid>

                {/* 配置说明和测试 */}
                <Grid item xs={12} md={4}>
                  {/* 配置说明 */}
                  <Card sx={{ mb: 3 }}>
                    <CardHeader
                      title={
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                          <Chip label={getProviderConfig().name} color="primary" size="small" />
                        </Box>
                      }
                      subheader={getProviderConfig().description}
                    />
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>
                        配置步骤：
                      </Typography>
                      {getProviderConfig().instructions.map((instruction, index) => (
                        <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                          {instruction}
                        </Typography>
                      ))}
                    </CardContent>
                  </Card>

                  {/* 邮件测试 */}
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      <Science sx={{ mr: 1, verticalAlign: "middle" }} />
                      测试邮件发送
                    </Typography>

                    <TextField
                      fullWidth
                      size="small"
                      label="测试邮箱地址"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      type="email"
                      sx={{ mb: 2 }}
                    />

                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<Email />}
                      onClick={handleTestEmail}
                      disabled={testEmailMutation.isPending || !systemConfig.emailEnabled}
                    >
                      {testEmailMutation.isPending ? "发送中..." : "发送测试邮件"}
                    </Button>

                    {testResult && (
                      <Alert
                        severity={testResult.success ? "success" : "error"}
                        icon={testResult.success ? <CheckCircle /> : <Error />}
                        sx={{ mt: 2 }}
                      >
                        {testResult.message}
                      </Alert>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

      </Grid>


    </Box>
  );
}


