"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  Settings,
  Telegram,
  Email,
  PhoneIphone,
  PlayArrow,
  Edit,
  Save,
  Business,
  Notifications,
  ExpandMore,
  Science,
  CheckCircle,
  Error,
  VpnKey,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

export default function AdminNotificationsPage() {
  const { data: session } = useSession();
  const [systemConfig, setSystemConfig] = useState<any>({});
  const [emailConfig, setEmailConfig] = useState({
    emailProvider: "GMAIL" as const,
    emailFromAddress: "",
    emailFromName: "证件提醒系统",
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    smtpSecure: true,
    emailApiKey: "",
  });
  const [testEmail, setTestEmail] = useState("");
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取系统配置
  const { data: systemNotificationConfig, refetch: refetchSystemConfig } =
    api.systemNotification.getSystemConfig.useQuery();

  // 超级管理员的邮件配置从系统配置中获取
  const currentEmailConfig = systemNotificationConfig;



  // 更新系统配置mutation
  const updateSystemConfigMutation = api.systemNotification.updateSystemConfig.useMutation({
    onSuccess: () => {
      refetchSystemConfig();
    },
  });

  // 生成VAPID密钥mutation
  const generateVapidMutation = api.systemNotification.generateVapidKeys.useMutation({
    onSuccess: (result) => {
      // 自动填入生成的密钥
      setSystemConfig((prev: any) => ({
        ...prev,
        vapidPublicKey: result.publicKey,
        vapidPrivateKey: result.privateKey,
      }));
      alert("VAPID密钥生成成功！请记得保存配置。");
    },
    onError: (error) => {
      alert("生成VAPID密钥失败: " + error.message);
    },
  });



  // 更新邮件配置mutation - 超级管理员直接更新系统配置
  const updateEmailConfigMutation = api.systemNotification.updateSystemConfig.useMutation({
    onSuccess: () => {
      alert("邮件配置更新成功！");
      refetchSystemConfig();
    },
    onError: (error) => {
      alert("更新失败: " + error.message);
    },
  });

  // 测试邮件发送 - 使用系统级别的测试API
  const testEmailMutation = api.systemNotification.testNotificationConfig.useMutation({
    onSuccess: (result) => {
      setTestResult({ success: true, message: "测试邮件发送成功！" });
    },
    onError: (error) => {
      setTestResult({ success: false, message: "测试失败: " + error.message });
    },
  });

  // 测试Telegram Bot
  const testTelegramMutation = api.systemNotification.testTelegramBot.useMutation();

  // 获取通知统计
  const { data: notificationStats } = api.systemNotification.getNotificationStats.useQuery({
    days: 30,
  });

  useEffect(() => {
    if (systemNotificationConfig) {
      setSystemConfig(systemNotificationConfig);
    }
  }, [systemNotificationConfig]);

  // 初始化邮件配置 - 从系统配置中读取
  useEffect(() => {
    if (currentEmailConfig) {
      setEmailConfig({
        emailProvider: (currentEmailConfig.emailProvider as "GMAIL") ?? "GMAIL",
        emailFromAddress: currentEmailConfig.emailFromAddress ?? "",
        emailFromName: currentEmailConfig.emailFromName ?? "证件提醒系统",
        smtpHost: currentEmailConfig.smtpHost ?? "",
        smtpPort: currentEmailConfig.smtpPort ?? 587,
        smtpUser: currentEmailConfig.smtpUser ?? "",
        smtpPassword: currentEmailConfig.smtpPassword ?? "",
        smtpSecure: currentEmailConfig.smtpSecure ?? true,
        emailApiKey: currentEmailConfig.emailApiKey ?? "",
      });
    }
  }, [currentEmailConfig]);

  const handleSystemConfigChange = (field: string, value: string | boolean | number) => {
    setSystemConfig((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleSaveSystemConfig = () => {
    updateSystemConfigMutation.mutate(systemConfig);
  };

  const handleSaveEmailConfig = () => {
    // 将邮件配置合并到系统配置中保存
    const updatedSystemConfig = {
      ...systemConfig,
      ...emailConfig,
    };
    updateEmailConfigMutation.mutate(updatedSystemConfig);
  };

  const handleTestEmail = () => {
    if (!testEmail) {
      alert("请输入测试邮箱地址");
      return;
    }

    testEmailMutation.mutate({
      type: "EMAIL",
      testData: {
        email: testEmail,
      },
    });
  };

  const handleGenerateVapidKeys = () => {
    if (confirm("生成新的VAPID密钥将替换现有密钥，确定要继续吗？")) {
      generateVapidMutation.mutate();
    }
  };

  const getProviderConfig = () => {
    switch (emailConfig.emailProvider) {
      case "GMAIL":
        return {
          name: "Gmail SMTP",
          description: "使用Gmail的SMTP服务发送邮件",
          defaultHost: "smtp.gmail.com",
          defaultPort: 587,
          instructions: [
            "1. 开启两步验证",
            "2. 生成应用专用密码",
            "3. 使用Gmail地址和应用专用密码",
          ],
        };
      case "OUTLOOK":
        return {
          name: "Outlook SMTP",
          description: "使用Outlook的SMTP服务发送邮件",
          defaultHost: "smtp-mail.outlook.com",
          defaultPort: 587,
          instructions: [
            "1. 使用Outlook邮箱地址",
            "2. 使用邮箱密码",
            "3. 确保账户安全设置允许SMTP",
          ],
        };
      case "SENDGRID":
        return {
          name: "SendGrid API",
          description: "使用SendGrid的API服务发送邮件",
          defaultHost: "",
          defaultPort: 0,
          instructions: [
            "1. 注册SendGrid账户",
            "2. 创建API Key",
            "3. 验证发送域名",
          ],
        };
      default:
        return {
          name: "自定义SMTP",
          description: "使用自定义SMTP服务器发送邮件",
          defaultHost: "",
          defaultPort: 587,
          instructions: [
            "1. 获取SMTP服务器信息",
            "2. 配置用户名和密码",
            "3. 选择正确的端口和加密方式",
          ],
        };
    }
  };



  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography variant="h4" gutterBottom>
        系统通知管理
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        配置系统通知机器人和邮件服务
      </Typography>

      <Grid container spacing={3}>
        {/* 系统配置 */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="系统通知配置"
              subheader="配置通知机器人和服务"
              avatar={<Settings />}
            />
            <CardContent>
              <Grid container spacing={2}>
                {/* 通知方式开关 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    通知方式开关
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.emailEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('emailEnabled', e.target.checked)}
                        />
                      }
                      label="邮件通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.telegramEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('telegramEnabled', e.target.checked)}
                        />
                      }
                      label="Telegram通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.pushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('pushEnabled', e.target.checked)}
                        />
                      }
                      label="PWA推送"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.browserNotificationEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('browserNotificationEnabled', e.target.checked)}
                        />
                      }
                      label="浏览器通知"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Divider />
                </Grid>

                {/* Telegram配置 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Telegram配置
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Telegram Bot Token"
                    value={systemConfig.telegramBotToken || ""}
                    onChange={(e) => handleSystemConfigChange('telegramBotToken', e.target.value)}
                    size="small"
                    type="password"
                    helperText="从 @BotFather 获取的Bot Token"
                    disabled={!systemConfig.telegramEnabled}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Bot名称"
                    value={systemConfig.telegramBotName || ""}
                    onChange={(e) => handleSystemConfigChange('telegramBotName', e.target.value)}
                    size="small"
                    disabled={!systemConfig.telegramEnabled}
                  />
                </Grid>



                {/* PWA推送配置 */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">
                      PWA推送配置
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<VpnKey />}
                      onClick={handleGenerateVapidKeys}
                      disabled={!systemConfig.pushEnabled || generateVapidMutation.isLoading}
                    >
                      {generateVapidMutation.isLoading ? '生成中...' : '生成VAPID密钥'}
                    </Button>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID公钥"
                    value={systemConfig.vapidPublicKey || ""}
                    onChange={(e) => handleSystemConfigChange('vapidPublicKey', e.target.value)}
                    size="small"
                    disabled={!systemConfig.pushEnabled}
                    helperText="用于PWA推送的VAPID公钥"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID私钥"
                    value={systemConfig.vapidPrivateKey || ""}
                    onChange={(e) => handleSystemConfigChange('vapidPrivateKey', e.target.value)}
                    size="small"
                    type="password"
                    disabled={!systemConfig.pushEnabled}
                    helperText="用于PWA推送的VAPID私钥"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="VAPID主题"
                    value={systemConfig.vapidSubject || ""}
                    onChange={(e) => handleSystemConfigChange('vapidSubject', e.target.value)}
                    size="small"
                    disabled={!systemConfig.pushEnabled}
                    helperText="邮箱地址或网站URL"
                  />
                </Grid>

                {/* 系统推送开关 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    推送类型配置
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.systemPushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('systemPushEnabled', e.target.checked)}
                          disabled={!systemConfig.pushEnabled}
                        />
                      }
                      label="系统推送通知"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.expiryPushEnabled ?? true}
                          onChange={(e) => handleSystemConfigChange('expiryPushEnabled', e.target.checked)}
                          disabled={!systemConfig.pushEnabled}
                        />
                      }
                      label="证件到期推送"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSaveSystemConfig}
                    disabled={updateSystemConfigMutation.isLoading}
                    fullWidth
                  >
                    {updateSystemConfigMutation.isLoading ? '保存中...' : '保存配置'}
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* 通知统计 */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="通知统计"
              subheader="最近30天的通知发送情况"
              avatar={<Notifications />}
            />
            <CardContent>
              {notificationStats && (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {notificationStats.totalSent}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        总发送数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {notificationStats.successCount}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        成功数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="error.main">
                        {notificationStats.failureCount}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        失败数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {Math.round((notificationStats.successCount / notificationStats.totalSent) * 100) || 0}%
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        成功率
                      </Typography>
                    </Paper>
                  </Grid>

                  {/* 按类型统计 */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      按通知类型统计
                    </Typography>
                    {notificationStats.byType?.map((stat: any) => (
                      <Box key={stat.type} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{stat.type}</Typography>
                        <Typography variant="body2" color="primary">
                          {stat.count}
                        </Typography>
                      </Box>
                    ))}
                  </Grid>

                  {/* 按渠道统计 */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      按通知渠道统计
                    </Typography>
                    {notificationStats.byChannel?.map((stat: any) => (
                      <Box key={stat.channel} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{stat.channel}</Typography>
                        <Typography variant="body2" color="primary">
                          {stat.count}
                        </Typography>
                      </Box>
                    ))}
                  </Grid>
                </Grid>
              )}

              {!notificationStats && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="textSecondary">
                    暂无通知统计数据
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 邮件配置 */}
        <Grid item xs={12}>
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Email sx={{ mr: 2, color: "primary.main" }} />
                <Typography variant="h6">邮件服务配置</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                {/* 基本配置 */}
                <Grid item xs={12} md={8}>
                  <Paper sx={{ p: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>邮件服务商</InputLabel>
                          <Select
                            value={emailConfig.emailProvider}
                            label="邮件服务商"
                            onChange={(e) => {
                              const providerConfig = getProviderConfig();
                              setEmailConfig({
                                ...emailConfig,
                                emailProvider: e.target.value as any,
                                smtpHost: providerConfig.defaultHost,
                                smtpPort: providerConfig.defaultPort,
                              });
                            }}
                          >
                            <MenuItem value="GMAIL">Gmail SMTP</MenuItem>
                            <MenuItem value="OUTLOOK">Outlook SMTP</MenuItem>
                            <MenuItem value="SENDGRID">SendGrid API</MenuItem>
                            <MenuItem value="SMTP">自定义SMTP</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label="发送者邮箱"
                          value={emailConfig.emailFromAddress}
                          onChange={(e) => setEmailConfig({ ...emailConfig, emailFromAddress: e.target.value })}
                          type="email"
                          required
                        />
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label="发送者名称"
                          value={emailConfig.emailFromName}
                          onChange={(e) => setEmailConfig({ ...emailConfig, emailFromName: e.target.value })}
                        />
                      </Grid>

                      {emailConfig.emailProvider === "SENDGRID" ? (
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            size="small"
                            label="SendGrid API Key"
                            value={emailConfig.emailApiKey}
                            onChange={(e) => setEmailConfig({ ...emailConfig, emailApiKey: e.target.value })}
                            type="password"
                            required
                          />
                        </Grid>
                      ) : (
                        <>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              size="small"
                              label="SMTP用户名"
                              value={emailConfig.smtpUser}
                              onChange={(e) => setEmailConfig({ ...emailConfig, smtpUser: e.target.value })}
                              placeholder={emailConfig.emailProvider === "GMAIL" ? "Gmail邮箱地址" : ""}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              size="small"
                              label="SMTP密码"
                              value={emailConfig.smtpPassword}
                              onChange={(e) => setEmailConfig({ ...emailConfig, smtpPassword: e.target.value })}
                              type="password"
                              placeholder={emailConfig.emailProvider === "GMAIL" ? "应用专用密码" : ""}
                            />
                          </Grid>

                          {(emailConfig.emailProvider === "SMTP") && (
                            <>
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="SMTP主机"
                                  value={emailConfig.smtpHost}
                                  onChange={(e) => setEmailConfig({ ...emailConfig, smtpHost: e.target.value })}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="SMTP端口"
                                  value={emailConfig.smtpPort}
                                  onChange={(e) => setEmailConfig({ ...emailConfig, smtpPort: parseInt(e.target.value) || 587 })}
                                  type="number"
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={emailConfig.smtpSecure}
                                      onChange={(e) => setEmailConfig({ ...emailConfig, smtpSecure: e.target.checked })}
                                    />
                                  }
                                  label="使用SSL/TLS加密"
                                />
                              </Grid>
                            </>
                          )}
                        </>
                      )}
                    </Grid>

                    <Box sx={{ mt: 3, display: "flex", gap: 2 }}>
                      <Button
                        variant="contained"
                        startIcon={<Save />}
                        onClick={handleSaveEmailConfig}
                        disabled={updateEmailConfigMutation.isPending}
                      >
                        {updateEmailConfigMutation.isPending ? "保存中..." : "保存邮件配置"}
                      </Button>
                    </Box>
                  </Paper>
                </Grid>

                {/* 配置说明和测试 */}
                <Grid item xs={12} md={4}>
                  {/* 配置说明 */}
                  <Card sx={{ mb: 3 }}>
                    <CardHeader
                      title={
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                          <Chip label={getProviderConfig().name} color="primary" size="small" />
                        </Box>
                      }
                      subheader={getProviderConfig().description}
                    />
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>
                        配置步骤：
                      </Typography>
                      {getProviderConfig().instructions.map((instruction, index) => (
                        <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                          {instruction}
                        </Typography>
                      ))}
                    </CardContent>
                  </Card>

                  {/* 邮件测试 */}
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      <Science sx={{ mr: 1, verticalAlign: "middle" }} />
                      测试邮件发送
                    </Typography>

                    <TextField
                      fullWidth
                      size="small"
                      label="测试邮箱地址"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      type="email"
                      sx={{ mb: 2 }}
                    />

                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<Email />}
                      onClick={handleTestEmail}
                      disabled={testEmailMutation.isPending || !systemConfig.emailEnabled}
                    >
                      {testEmailMutation.isPending ? "发送中..." : "发送测试邮件"}
                    </Button>

                    {testResult && (
                      <Alert
                        severity={testResult.success ? "success" : "error"}
                        icon={testResult.success ? <CheckCircle /> : <Error />}
                        sx={{ mt: 2 }}
                      >
                        {testResult.message}
                      </Alert>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

      </Grid>


    </Box>
  );
}


