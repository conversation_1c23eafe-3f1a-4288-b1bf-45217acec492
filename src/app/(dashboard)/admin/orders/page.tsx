"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  Paper,
  Tooltip,
  Pagination,
  InputAdornment,
} from "@mui/material";
import {
  Search,
  FilterList,
  Visibility,
  Edit,
  GetApp,
  AttachMoney,
  ShoppingCart,
  TrendingUp,
  Assessment,
  CheckCircle,
  Cancel,
} from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { api } from "~/trpc/react";
import { StatsCardSkeleton, TableRowSkeleton } from "~/components/ui/Skeleton";

const ORDER_STATUS_MAP = {
  PENDING: { label: "待支付", color: "warning" as const },
  PAID: { label: "已支付", color: "success" as const },
  FAILED: { label: "支付失败", color: "error" as const },
  CANCELLED: { label: "已取消", color: "default" as const },
  REFUNDED: { label: "已退款", color: "info" as const },
};

export default function AdminOrdersPage() {
  const { data: session } = useSession();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    status: "",
    tenantId: "",
    startDate: null as any,
    endDate: null as any,
    search: "",
  });
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [statusNote, setStatusNote] = useState("");
  const [reviewNote, setReviewNote] = useState("");

  // 检查超级管理员权限
  if (session?.user?.role !== "SUPER_ADMIN") {
    redirect("/dashboard");
  }

  // 获取订单列表
  const { data: ordersData, refetch, isLoading } = api.adminOrder.getAll.useQuery({
    page,
    limit: 20,
    ...filters,
    startDate: filters.startDate ? dayjs(filters.startDate).toDate() : undefined,
    endDate: filters.endDate ? dayjs(filters.endDate).toDate() : undefined,
  });

  // 获取订单统计
  const { data: stats } = api.adminOrder.getStats.useQuery({
    startDate: filters.startDate ? dayjs(filters.startDate).toDate() : undefined,
    endDate: filters.endDate ? dayjs(filters.endDate).toDate() : undefined,
  });

  // 更新订单状态
  const updateStatusMutation = api.adminOrder.updateStatus.useMutation({
    onSuccess: () => {
      refetch();
      setStatusDialogOpen(false);
      setSelectedOrder(null);
      setNewStatus("");
      setStatusNote("");
    },
  });

  // 审核订单
  const reviewOrderMutation = api.adminOrder.review.useMutation({
    onSuccess: () => {
      refetch();
      setReviewDialogOpen(false);
      setSelectedOrder(null);
      setReviewNote("");
    },
  });

  // 获取 tRPC utils
  const utils = api.useUtils();

  const handleViewDetail = async (orderId: string) => {
    try {
      const orderDetail = await utils.adminOrder.getById.fetch({ id: orderId });
      setSelectedOrder(orderDetail);
      setDetailDialogOpen(true);
    } catch (error) {
      console.error("获取订单详情失败:", error);
      alert("获取订单详情失败: " + (error as Error).message);
    }
  };

  const handleUpdateStatus = (order: any) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setStatusDialogOpen(true);
  };

  const handleSubmitStatusUpdate = () => {
    if (selectedOrder && newStatus) {
      updateStatusMutation.mutate({
        orderId: selectedOrder.id,
        status: newStatus as any,
        note: statusNote,
      });
    }
  };

  const handleReviewOrder = (order: any) => {
    setSelectedOrder(order);
    setReviewDialogOpen(true);
  };

  const handleSubmitReview = (approved: boolean) => {
    if (selectedOrder) {
      reviewOrderMutation.mutate({
        orderId: selectedOrder.id,
        approved,
        note: reviewNote,
      });
    }
  };

  // 导出功能 - 暂时禁用
  const handleExport = async () => {
    alert('导出功能开发中');
    return;

    // TODO: 实现导出功能
    /*
    try {
      const exportData = await api.adminOrder.export.mutate({
        ...filters,
        startDate: filters.startDate ? dayjs(filters.startDate).toDate() : undefined,
        endDate: filters.endDate ? dayjs(filters.endDate).toDate() : undefined,
        page: 1,
        limit: 1000,
      });
    */
      /*
      // 简单的CSV导出
      const csvContent = [
        ["订单号", "商户名称", "套餐名称", "金额", "状态", "创建时间", "支付时间"].join(","),
        ...exportData.map(order => [
          order.orderNumber,
          order.tenantName,
          order.planName,
          order.amount,
          ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.label || order.status,
          dayjs(order.createdAt).format("YYYY-MM-DD HH:mm:ss"),
          order.paidAt ? dayjs(order.paidAt).format("YYYY-MM-DD HH:mm:ss") : "",
        ].join(","))
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `orders_${dayjs().format("YYYY-MM-DD")}.csv`;
      link.click();
    } catch (error) {
      console.error("导出失败:", error);
    }
    */
  };

  if (isLoading) {
    return (
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Typography variant="h4" gutterBottom>
          订单管理
        </Typography>
        
        {/* 统计卡片骨架 */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <StatsCardSkeleton />
            </Grid>
          ))}
        </Grid>

        {/* 表格骨架 */}
        <Paper>
          <Box sx={{ p: 2 }}>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRowSkeleton key={index} columns={7} />
            ))}
          </Box>
        </Paper>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        {/* 页面标题 */}
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
          <Typography variant="h4" component="h1">
            订单管理
          </Typography>
          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={handleExport}
          >
            导出数据
          </Button>
        </Box>

        {/* 错误提示 */}
        {updateStatusMutation.error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {updateStatusMutation.error.message}
          </Alert>
        )}

        {/* 统计卡片 */}
        {stats && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom variant="h6">
                        总订单数
                      </Typography>
                      <Typography variant="h4">
                        {stats.totalOrders}
                      </Typography>
                    </Box>
                    <ShoppingCart color="primary" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom variant="h6">
                        已支付订单
                      </Typography>
                      <Typography variant="h4">
                        {stats.paidOrders}
                      </Typography>
                    </Box>
                    <Assessment color="success" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom variant="h6">
                        总收入
                      </Typography>
                      <Typography variant="h4">
                        ¥{stats.totalRevenue.toLocaleString()}
                      </Typography>
                    </Box>
                    <AttachMoney color="warning" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom variant="h6">
                        转化率
                      </Typography>
                      <Typography variant="h4">
                        {stats.conversionRate.toFixed(1)}%
                      </Typography>
                    </Box>
                    <TrendingUp color="info" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* 筛选器 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="搜索订单号或商户名称"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={2}>
              <FormControl fullWidth size="small">
                <InputLabel>状态</InputLabel>
                <Select
                  value={filters.status}
                  label="状态"
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="">全部</MenuItem>
                  {Object.entries(ORDER_STATUS_MAP).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {value.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <DatePicker
                label="开始日期"
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} sm={2}>
              <DatePicker
                label="结束日期"
                value={filters.endDate}
                onChange={(date) => setFilters(prev => ({ ...prev, endDate: date }))}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => refetch()}
                fullWidth
              >
                应用筛选
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* 订单列表 */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>订单号</TableCell>
                <TableCell>商户</TableCell>
                <TableCell>套餐</TableCell>
                <TableCell>金额</TableCell>
                <TableCell>状态</TableCell>
                <TableCell>创建时间</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {ordersData?.orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {order.orderNumber}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{order.tenant.name}</Typography>
                      <Typography variant="body2" color="textSecondary">
                        {order.tenant.type}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {order.plan ? (
                      <Box>
                        <Typography variant="subtitle2">
                          {order.plan.name}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {order.plan.billingCycle === 'YEARLY' ? '年付' : order.plan.billingCycle === 'MONTHLY' ? '月付' : '试用'}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        无套餐信息
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AttachMoney sx={{ fontSize: 16, mr: 0.5 }} />
                      {order.amount} {order.currency}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.label || order.status}
                      size="small"
                      color={ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.color || 'default'}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {dayjs(order.createdAt).format("YYYY-MM-DD HH:mm")}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="查看详情">
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetail(order.id)}
                      >
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="更新状态">
                      <IconButton
                        size="small"
                        onClick={() => handleUpdateStatus(order)}
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    {order.status === "PENDING" && order.paymentProofUrl && (
                      <Tooltip title="审核订单">
                        <IconButton
                          size="small"
                          onClick={() => handleReviewOrder(order)}
                          color="primary"
                        >
                          <CheckCircle />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* 分页 */}
        {ordersData && ordersData.pagination.totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={ordersData.pagination.totalPages}
              page={page}
              onChange={(_, newPage) => setPage(newPage)}
              color="primary"
            />
          </Box>
        )}

        {/* 订单详情对话框 */}
        <Dialog 
          open={detailDialogOpen} 
          onClose={() => setDetailDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>订单详情</DialogTitle>
          <DialogContent>
            {selectedOrder && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">订单号</Typography>
                  <Typography variant="body2" fontFamily="monospace">
                    {selectedOrder.orderNumber}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">商户名称</Typography>
                  <Typography variant="body2">{selectedOrder.tenant.name}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">金额</Typography>
                  <Typography variant="body2">
                    {selectedOrder.amount} {selectedOrder.currency}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">状态</Typography>
                  <Chip 
                    label={ORDER_STATUS_MAP[selectedOrder.status as keyof typeof ORDER_STATUS_MAP]?.label || selectedOrder.status}
                    size="small"
                    color={ORDER_STATUS_MAP[selectedOrder.status as keyof typeof ORDER_STATUS_MAP]?.color || 'default'}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">创建时间</Typography>
                  <Typography variant="body2">
                    {dayjs(selectedOrder.createdAt).format("YYYY-MM-DD HH:mm:ss")}
                  </Typography>
                </Grid>
                {selectedOrder.paidAt && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">支付时间</Typography>
                    <Typography variant="body2">
                      {dayjs(selectedOrder.paidAt).format("YYYY-MM-DD HH:mm:ss")}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDetailDialogOpen(false)}>关闭</Button>
          </DialogActions>
        </Dialog>

        {/* 状态更新对话框 */}
        <Dialog 
          open={statusDialogOpen} 
          onClose={() => setStatusDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>更新订单状态</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth size="small">
                  <InputLabel>新状态</InputLabel>
                  <Select
                    value={newStatus}
                    label="新状态"
                    onChange={(e) => setNewStatus(e.target.value)}
                  >
                    {Object.entries(ORDER_STATUS_MAP).map(([key, value]) => (
                      <MenuItem key={key} value={key}>
                        {value.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="备注"
                  value={statusNote}
                  onChange={(e) => setStatusNote(e.target.value)}
                  size="small"
                  multiline
                  rows={3}
                  placeholder="请输入状态更新的原因或备注..."
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatusDialogOpen(false)}>取消</Button>
            <Button 
              onClick={handleSubmitStatusUpdate}
              variant="contained"
              disabled={updateStatusMutation.isPending || !newStatus}
            >
              更新
            </Button>
          </DialogActions>
        </Dialog>

        {/* 审核订单对话框 */}
        <Dialog
          open={reviewDialogOpen}
          onClose={() => setReviewDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>审核订单</DialogTitle>
          <DialogContent>
            {selectedOrder && (
              <Stack spacing={3}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      订单号
                    </Typography>
                    <Typography variant="body1" fontFamily="monospace">
                      {selectedOrder.orderNumber}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      商户
                    </Typography>
                    <Typography variant="body1">
                      {selectedOrder.tenant?.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      套餐
                    </Typography>
                    <Typography variant="body1">
                      {selectedOrder.plan?.name || '无套餐信息'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      金额
                    </Typography>
                    <Typography variant="body1">
                      {selectedOrder.currency} {selectedOrder.amount}
                    </Typography>
                  </Grid>
                </Grid>

                {selectedOrder.paymentProofUrl && (
                  <Box>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      付款凭证
                    </Typography>
                    <img
                      src={selectedOrder.paymentProofUrl}
                      alt="付款凭证"
                      style={{ maxWidth: "100%", maxHeight: "300px", objectFit: "contain" }}
                    />
                  </Box>
                )}

                <TextField
                  fullWidth
                  label="审核备注"
                  value={reviewNote}
                  onChange={(e) => setReviewNote(e.target.value)}
                  size="small"
                  multiline
                  rows={3}
                  placeholder="请输入审核意见..."
                />
              </Stack>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReviewDialogOpen(false)}>取消</Button>
            <Button
              onClick={() => handleSubmitReview(false)}
              color="error"
              disabled={reviewOrderMutation.isPending}
            >
              拒绝
            </Button>
            <Button
              onClick={() => handleSubmitReview(true)}
              variant="contained"
              disabled={reviewOrderMutation.isPending}
            >
              通过
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
}
