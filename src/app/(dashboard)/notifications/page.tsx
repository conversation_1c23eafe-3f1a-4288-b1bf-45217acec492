"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Switch,
  Button,
  TextField,
  Chip,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormGroup,
  FormControlLabel,
} from "@mui/material";
import {
  Notifications,
  Email,
  Telegram,
  PhoneIphone,
  Settings,
  PlayArrow,
  Check,
  Error,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { StatsCardSkeleton, ListItemSkeleton } from "~/components/ui/Skeleton";

export default function NotificationsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [config, setConfig] = useState<any>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // 处理超级管理员重定向
  useEffect(() => {
    if (session?.user?.role === "SUPER_ADMIN") {
      setIsRedirecting(true);
      router.push("/admin/notifications");
    }
  }, [session?.user?.role, router]);

  // 超级管理员重定向显示
  if (session?.user?.role === "SUPER_ADMIN") {
    return (
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Alert severity="info">
          正在跳转到系统通知配置页面...
        </Alert>
      </Box>
    );
  }

  // 只有商户管理员可以访问通知配置
  if (session?.user?.role === "TENANT_MEMBER") {
    return (
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Alert severity="warning">
          只有商户管理员可以设置通知配置，请联系您的管理员进行设置。
        </Alert>
      </Box>
    );
  }
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [pushSubscriptionStatus, setPushSubscriptionStatus] = useState<'unknown' | 'supported' | 'denied' | 'subscribed' | 'unsubscribed'>('unknown');
  const [telegramDialogOpen, setTelegramDialogOpen] = useState(false);
  const [telegramChatId, setTelegramChatId] = useState("");
  const [reminderDays, setReminderDays] = useState<number[]>([30, 7, 1]);
  const [newReminderDay, setNewReminderDay] = useState<number>(0);

  // 获取通知配置
  const { data: notificationConfig, refetch } = api.notificationConfig.getConfig.useQuery({});

  // 获取可用的通知方式
  const { data: availableMethods } = api.notificationConfig.getAvailableMethods.useQuery();

  // 更新配置mutation
  const updateConfigMutation = api.notificationConfig.updateConfig.useMutation({
    onSuccess: () => {
      refetch();
      console.log('配置更新成功');
    },
    onError: (error) => {
      console.error('配置更新失败:', error);
      alert('配置更新失败: ' + error.message);
    },
  });

  // 测试通知mutation
  const testNotificationMutation = api.notificationConfig.testNotification.useMutation();

  useEffect(() => {
    if (notificationConfig) {
      setConfig(notificationConfig);
      setReminderDays(notificationConfig.reminderDays || [30, 7, 1]);
      setTelegramChatId(notificationConfig.telegramChatId || "");
    }
  }, [notificationConfig]);

  const handleConfigChange = (field: string, value: any) => {
    const newConfig = { ...config, [field]: value };
    setConfig(newConfig);
    console.log('更新配置字段:', field, '值:', value, '完整配置:', newConfig);

    // 立即保存配置，确保telegramChatId为空字符串而不是null
    const configToSave = {
      ...newConfig,
      telegramChatId: newConfig.telegramChatId || ""
    };
    updateConfigMutation.mutate(configToSave);
  };

  const handleReminderDaysChange = (days: number[]) => {
    setReminderDays(days);
    handleConfigChange('reminderDays', days);
  };

  const addReminderDay = () => {
    if (newReminderDay > 0 && !reminderDays.includes(newReminderDay)) {
      const newDays = [...reminderDays, newReminderDay].sort((a, b) => b - a);
      handleReminderDaysChange(newDays);
      setNewReminderDay(0);
    }
  };

  const removeReminderDay = (day: number) => {
    const newDays = reminderDays.filter(d => d !== day);
    handleReminderDaysChange(newDays);
  };

  const handleTelegramSave = () => {
    // 处理多个ID：去除空格，过滤空值，用逗号连接
    const processedIds = telegramChatId
      .split(',')
      .map(id => id.trim())
      .filter(id => id.length > 0)
      .join(',');

    const newConfig = {
      ...config,
      telegramChatId: processedIds || "",
      telegramEnabled: !!processedIds // 只有当有ID时才启用
    };
    setConfig(newConfig);
    updateConfigMutation.mutate(newConfig);
    setTelegramDialogOpen(false);
  };

  const handleTelegramToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;

    if (isChecked) {
      // 如果要启用但没有配置Chat ID，打开配置对话框
      if (!config.telegramChatId) {
        setTelegramChatId("");
        setTelegramDialogOpen(true);
      } else {
        // 如果已有配置，直接启用
        handleConfigChange('telegramEnabled', true);
      }
    } else {
      // 直接禁用
      handleConfigChange('telegramEnabled', false);
    }
  };

  const handleTelegramConfigClick = () => {
    // 点击已配置的Telegram项目时，打开配置对话框进行修改
    setTelegramChatId(config.telegramChatId || "");
    setTelegramDialogOpen(true);
  };

  const handleTestNotification = (channels: string[]) => {
    // 如果包含浏览器通知，先触发浏览器通知
    if (channels.includes('BROWSER_NOTIFICATION')) {
      handleBrowserNotificationTest();
    }

    testNotificationMutation.mutate({
      channels: channels as any,
      message: "这是一条测试通知，用于验证通知渠道是否正常工作。",
    });
  };

  const handleBrowserNotificationTest = () => {
    // 检查浏览器是否支持通知
    if (!("Notification" in window)) {
      alert("此浏览器不支持桌面通知");
      return;
    }

    // 检查通知权限
    if (Notification.permission === "granted") {
      // 直接显示通知
      new Notification("测试通知", {
        body: "这是一条测试通知，用于验证浏览器通知功能是否正常工作。",
        icon: "/favicon.ico",
        tag: "test-notification"
      });
    } else if (Notification.permission !== "denied") {
      // 请求权限
      Notification.requestPermission().then((permission) => {
        if (permission === "granted") {
          new Notification("测试通知", {
            body: "这是一条测试通知，用于验证浏览器通知功能是否正常工作。",
            icon: "/favicon.ico",
            tag: "test-notification"
          });
        }
      });
    } else {
      alert("浏览器通知权限被拒绝，请在浏览器设置中允许通知权限");
    }
  };

  // 检查PWA推送订阅状态
  const checkPushSubscriptionStatus = async () => {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      setPushSubscriptionStatus('supported');
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      if (permission === 'denied') {
        setPushSubscriptionStatus('denied');
        return;
      }

      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      if (subscription) {
        setPushSubscriptionStatus('subscribed');
      } else {
        setPushSubscriptionStatus('unsubscribed');
      }
    } catch (error) {
      console.error('检查推送订阅状态失败:', error);
      setPushSubscriptionStatus('supported');
    }
  };

  // 获取VAPID公钥
  const { data: vapidData } = api.notificationConfig.getVapidPublicKey.useQuery();

  // 订阅PWA推送
  const subscribeToPush = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;

      if (!vapidData?.success || !vapidData?.vapidPublicKey) {
        alert('PWA推送服务未配置，请联系管理员在系统设置中配置VAPID密钥');
        return;
      }

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: vapidData.vapidPublicKey,
      });

      // 将订阅信息发送到服务器
      // TODO: 实现保存订阅信息的API
      console.log('PWA推送订阅成功:', subscription);
      setPushSubscriptionStatus('subscribed');

    } catch (error) {
      console.error('PWA推送订阅失败:', error);
      alert('PWA推送订阅失败，请检查浏览器设置或联系管理员');
    }
  };

  // 取消PWA推送订阅
  const unsubscribeFromPush = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      if (subscription) {
        await subscription.unsubscribe();
        setPushSubscriptionStatus('unsubscribed');
        console.log('PWA推送订阅已取消');
      }
    } catch (error) {
      console.error('取消PWA推送订阅失败:', error);
    }
  };

  // 组件加载时检查推送订阅状态
  useEffect(() => {
    if (config) {
      checkPushSubscriptionStatus();
    }
  }, [config]);

  if (!config) {
    return <NotificationsPageSkeleton />;
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography variant="h4" gutterBottom>
        通知设置
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        配置证件到期提醒的通知方式和时间
      </Typography>

      <Grid container spacing={3}>
        {/* 通知渠道设置 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="通知渠道"
              subheader="选择接收通知的方式"
              avatar={<Notifications />}
            />
            <CardContent>
              <List>
                {/* 邮件通知 */}
                <ListItem>
                  <ListItemIcon>
                    <Email />
                  </ListItemIcon>
                  <ListItemText
                    primary="邮件通知"
                    secondary={
                      !availableMethods?.email ? "系统已禁用邮件通知" :
                      `接收邮箱: ${config.notificationEmail || session?.user?.email || "未设置邮箱"}`
                    }
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={config.emailEnabled && (availableMethods?.email ?? false)}
                      onChange={(e) => handleConfigChange('emailEnabled', e.target.checked)}
                      disabled={!config.notificationEmail && !session?.user?.email || !(availableMethods?.email ?? false)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>

                {/* 通知邮箱配置 */}
                {availableMethods?.email && (
                  <ListItem>
                    <Box sx={{ width: '100%', pl: 7 }}>
                      <TextField
                        fullWidth
                        size="small"
                        label="通知邮箱"
                        type="email"
                        value={config.notificationEmail || ''}
                        onChange={(e) => handleConfigChange('notificationEmail', e.target.value)}
                        placeholder={session?.user?.email || '请输入邮箱地址'}
                        helperText="留空则使用登录邮箱"
                        disabled={!(availableMethods?.email ?? false)}
                      />
                    </Box>
                  </ListItem>
                )}

                {availableMethods?.email && <Divider />}

                {/* Telegram通知 */}
                {availableMethods?.telegram && (
                  <ListItem>
                    <ListItemIcon>
                      <Telegram />
                    </ListItemIcon>
                    <ListItemText
                      primary="Telegram通知"
                      secondary={
                        !availableMethods.telegram ? "系统已禁用Telegram通知" :
                        config.telegramChatId ?
                          `接收ID: ${config.telegramChatId}` :
                          "点击开关启用并配置Telegram通知"
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {config.telegramChatId && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={handleTelegramConfigClick}
                            disabled={!availableMethods.telegram}
                          >
                            配置
                          </Button>
                        )}
                        <Switch
                          checked={config.telegramEnabled && (availableMethods?.telegram ?? false) && !!config.telegramChatId}
                          onChange={handleTelegramToggle}
                          disabled={!(availableMethods?.telegram ?? false)}
                        />
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                )}

                <Divider />

                {/* PWA推送通知 */}
                <ListItem>
                  <ListItemIcon>
                    <PhoneIphone />
                  </ListItemIcon>
                  <ListItemText
                    primary="PWA推送"
                    secondary={
                      !(availableMethods?.push ?? false) ? "系统已禁用PWA推送" :
                      pushSubscriptionStatus === 'denied' ? "浏览器已拒绝推送权限" :
                      pushSubscriptionStatus === 'subscribed' ? "已订阅推送服务" :
                      pushSubscriptionStatus === 'unsubscribed' ? "未订阅推送服务" :
                      "即使关闭网页也能接收推送通知"
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {availableMethods?.push && pushSubscriptionStatus === 'unsubscribed' && (
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={subscribeToPush}
                        >
                          订阅
                        </Button>
                      )}
                      {availableMethods?.push && pushSubscriptionStatus === 'subscribed' && (
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={unsubscribeFromPush}
                        >
                          取消订阅
                        </Button>
                      )}
                      <Switch
                        checked={config.pushEnabled && (availableMethods?.push ?? false) && pushSubscriptionStatus === 'subscribed'}
                        onChange={(e) => handleConfigChange('pushEnabled', e.target.checked)}
                        disabled={!(availableMethods?.push ?? false) || pushSubscriptionStatus !== 'subscribed'}
                      />
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>

                <Divider />

                {/* 浏览器通知 */}
                <ListItem>
                  <ListItemIcon>
                    <Notifications />
                  </ListItemIcon>
                  <ListItemText
                    primary="浏览器通知"
                    secondary={
                      !(availableMethods?.browserNotification ?? false) ? "系统已禁用浏览器通知" : "在当前页面显示本地通知"
                    }
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={config.browserNotificationEnabled && (availableMethods?.browserNotification ?? false)}
                      onChange={(e) => handleConfigChange('browserNotificationEnabled', e.target.checked)}
                      disabled={!(availableMethods?.browserNotification ?? false)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<PlayArrow />}
                  onClick={() => setTestDialogOpen(true)}
                  fullWidth
                >
                  测试通知
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 通知类型设置 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="通知类型"
              subheader="选择要接收的通知类型"
              avatar={<Settings />}
            />
            <CardContent>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={config.documentExpiry}
                      onChange={(e) => handleConfigChange('documentExpiry', e.target.checked)}
                    />
                  }
                  label="证件过期通知"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={config.documentReminder}
                      onChange={(e) => handleConfigChange('documentReminder', e.target.checked)}
                    />
                  }
                  label="证件到期提醒"
                />
              </FormGroup>
            </CardContent>
          </Card>
        </Grid>

        {/* 提醒时间设置 */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="提醒时间"
              subheader="设置提前多少天发送到期提醒"
            />
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  当前设置：提前 {reminderDays.join('、')} 天提醒
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {reminderDays.map((day) => (
                    <Chip
                      key={day}
                      label={`${day}天`}
                      onDelete={() => removeReminderDay(day)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Box>

              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  label="添加提醒天数"
                  type="number"
                  value={newReminderDay || ''}
                  onChange={(e) => setNewReminderDay(parseInt(e.target.value) || 0)}
                  size="small"
                  inputProps={{ min: 1, max: 365 }}
                />
                <Button
                  variant="outlined"
                  onClick={addReminderDay}
                  disabled={newReminderDay <= 0 || reminderDays.includes(newReminderDay)}
                >
                  添加
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Telegram配置对话框 */}
      <Dialog open={telegramDialogOpen} onClose={() => setTelegramDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>配置Telegram通知</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" gutterBottom>
              要接收Telegram通知，您需要先获取您的Telegram ID：
            </Typography>
            <Typography variant="body2" component="div" sx={{ mt: 1 }}>
              1. 点击下方按钮访问我们的机器人<br/>
              2. 向机器人发送任意消息<br/>
              3. 机器人会回复您的Telegram ID<br/>
              4. 将ID复制到下方输入框中<br/>
              5. 支持多个ID，用英文逗号分隔
            </Typography>
          </Alert>

          <Box sx={{ mb: 2, textAlign: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Telegram />}
              onClick={() => window.open('https://t.me/TLNotification_bot', '_blank')}
              sx={{ mb: 1 }}
            >
              获取我的Telegram ID
            </Button>
            <Typography variant="caption" display="block" color="text.secondary">
              点击按钮将在新窗口中打开Telegram机器人
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          <TextField
            fullWidth
            label="Telegram ID"
            value={telegramChatId}
            onChange={(e) => setTelegramChatId(e.target.value)}
            margin="normal"
            required
            multiline
            rows={3}
            placeholder="例如: 123456789,987654321 或 @username1,@username2"
            helperText="请输入从机器人获取的Telegram ID，多个ID用英文逗号分隔"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTelegramDialogOpen(false)}>取消</Button>
          <Button
            onClick={handleTelegramSave}
            variant="contained"
            disabled={!telegramChatId.trim() || updateConfigMutation.isPending}
          >
            {updateConfigMutation.isPending ? '保存中...' : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 测试通知对话框 */}
      <Dialog open={testDialogOpen} onClose={() => setTestDialogOpen(false)}>
        <DialogTitle>测试通知</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            选择要测试的通知渠道：
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="邮件通知"
              disabled={!config.emailEnabled || !session?.user?.email}
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Telegram通知"
              disabled={!config.telegramEnabled || !config.telegramChatId}
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="PWA推送"
              disabled={!config.pushEnabled}
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="浏览器通知"
              disabled={!config.browserNotificationEnabled}
            />
          </FormGroup>

          {testNotificationMutation.data && (
            <Alert
              severity="info"
              sx={{ mt: 2 }}
            >
              <Typography variant="subtitle2" gutterBottom>
                测试结果：
              </Typography>
              {testNotificationMutation.data.results.map((result: any, index: number) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {result.success ? <Check color="success" /> : <Error color="error" />}
                  <Typography variant="body2">
                    {result.channel}: {result.message}
                  </Typography>
                </Box>
              ))}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialogOpen(false)}>关闭</Button>
          <Button
            onClick={() => handleTestNotification(['EMAIL', 'TELEGRAM', 'PWA_PUSH', 'BROWSER_NOTIFICATION'])}
            variant="contained"
            disabled={testNotificationMutation.isPending}
          >
            {testNotificationMutation.isPending ? '发送中...' : '发送测试'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

// 通知页面骨架屏组件
function NotificationsPageSkeleton() {
  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography variant="h4" gutterBottom>
        通知设置
      </Typography>

      <Grid container spacing={3}>
        {/* 通知渠道配置骨架 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                通知渠道
              </Typography>
              {Array.from({ length: 4 }).map((_, index) => (
                <ListItemSkeleton key={index} />
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* 通知类型配置骨架 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                通知类型
              </Typography>
              {Array.from({ length: 3 }).map((_, index) => (
                <ListItemSkeleton key={index} />
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* 提醒时间配置骨架 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                提醒时间设置
              </Typography>
              <StatsCardSkeleton />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
