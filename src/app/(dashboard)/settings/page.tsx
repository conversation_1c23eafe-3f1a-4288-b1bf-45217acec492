"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Divider,
  Alert,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
} from "@mui/material";
import {
  Person,
  Security,
  Edit,
  Save,
  Visibility,
  VisibilityOff,
  PhotoCamera,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { FormSkeleton, StatsCardSkeleton } from "~/components/ui/Skeleton";

export default function SettingsPage() {
  const { data: session, status } = useSession();
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    avatar: '',
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "success" | "error">("idle");

  // 获取用户信息
  const { data: userInfo, refetch } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session,
  });

  // 更新个人信息
  const updateProfileMutation = api.user.updateProfile.useMutation({
    onSuccess: () => {
      setSaveStatus("success");
      refetch();
      setTimeout(() => setSaveStatus("idle"), 3000);
    },
    onError: () => {
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    },
  });

  // 修改密码
  const changePasswordMutation = api.user.changePassword.useMutation({
    onSuccess: () => {
      setPasswordDialogOpen(false);
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setSaveStatus("success");
      setTimeout(() => setSaveStatus("idle"), 3000);
    },
    onError: () => {
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    },
  });

  useEffect(() => {
    if (userInfo) {
      setProfileData({
        name: userInfo.name || '',
        email: userInfo.email || '',
        phone: userInfo.phone || '',
        avatar: userInfo.image || '',
      });
    }
  }, [userInfo]);

  if (status === "loading") {
    return <SettingsPageSkeleton />;
  }

  if (!session) {
    redirect("/auth/signin");
  }

  const handleProfileChange = (field: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveProfile = () => {
    setSaveStatus("saving");
    updateProfileMutation.mutate({
      name: profileData.name,
      phone: profileData.phone,
    });
  };

  const handleChangePassword = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
      return;
    }

    changePasswordMutation.mutate({
      currentPassword: passwordData.currentPassword,
      newPassword: passwordData.newPassword,
    });
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: 1000, mx: 'auto' }}>
      {saveStatus === "success" && (
        <Alert severity="success" sx={{ mb: 3 }}>
          操作成功！
        </Alert>
      )}

      {saveStatus === "error" && (
        <Alert severity="error" sx={{ mb: 3 }}>
          操作失败，请重试。
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* 个人信息设置 */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              avatar={<Person color="primary" />}
              title="个人信息"
              subheader="管理您的个人资料信息"
            />
            <CardContent>
              <Grid container spacing={3}>
                {/* 头像部分 */}
                <Grid item xs={12} sx={{ textAlign: 'center', mb: 2 }}>
                  <Box sx={{ position: 'relative', display: 'inline-block' }}>
                    <Avatar
                      src={profileData.avatar}
                      sx={{ width: 100, height: 100, mx: 'auto' }}
                    >
                      {profileData.name?.charAt(0) || profileData.email?.charAt(0)}
                    </Avatar>
                    <IconButton
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        backgroundColor: 'primary.main',
                        color: 'white',
                        '&:hover': { backgroundColor: 'primary.dark' },
                        width: 32,
                        height: 32,
                      }}
                      size="small"
                    >
                      <PhotoCamera fontSize="small" />
                    </IconButton>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    点击更换头像
                  </Typography>
                </Grid>

                {/* 基本信息 */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="姓名"
                    value={profileData.name}
                    onChange={(e) => handleProfileChange('name', e.target.value)}
                    size="small"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="邮箱地址"
                    value={profileData.email}
                    disabled
                    size="small"
                    helperText="邮箱地址不可修改"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="联系电话"
                    value={profileData.phone}
                    onChange={(e) => handleProfileChange('phone', e.target.value)}
                    size="small"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      startIcon={<Save />}
                      onClick={handleSaveProfile}
                      disabled={updateProfileMutation.isPending}
                      size="small"
                    >
                      {updateProfileMutation.isPending ? '保存中...' : '保存信息'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* 安全设置 */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              avatar={<Security color="primary" />}
              title="安全设置"
              subheader="管理您的账户安全"
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="subtitle1">修改密码</Typography>
                      <Typography variant="body2" color="text.secondary">
                        定期更换密码以保护账户安全
                      </Typography>
                    </Box>
                    <Button
                      variant="outlined"
                      startIcon={<Edit />}
                      onClick={() => setPasswordDialogOpen(true)}
                      size="small"
                    >
                      修改密码
                    </Button>
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Divider />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="subtitle1">登录信息</Typography>
                      <Typography variant="body2" color="text.secondary">
                        当前登录邮箱: {session?.user?.email}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        上次登录时间: {new Date().toLocaleString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 修改密码对话框 */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>修改密码</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="当前密码"
                type={showPassword.current ? 'text' : 'password'}
                value={passwordData.currentPassword}
                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                size="small"
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => togglePasswordVisibility('current')}
                        edge="end"
                        size="small"
                      >
                        {showPassword.current ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="新密码"
                type={showPassword.new ? 'text' : 'password'}
                value={passwordData.newPassword}
                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                size="small"
                required
                helperText="密码长度至少6位"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => togglePasswordVisibility('new')}
                        edge="end"
                        size="small"
                      >
                        {showPassword.new ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="确认新密码"
                type={showPassword.confirm ? 'text' : 'password'}
                value={passwordData.confirmPassword}
                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                size="small"
                required
                error={passwordData.confirmPassword !== '' && passwordData.newPassword !== passwordData.confirmPassword}
                helperText={
                  passwordData.confirmPassword !== '' && passwordData.newPassword !== passwordData.confirmPassword
                    ? '两次输入的密码不一致'
                    : '请再次输入新密码'
                }
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => togglePasswordVisibility('confirm')}
                        edge="end"
                        size="small"
                      >
                        {showPassword.confirm ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialogOpen(false)} size="small">
            取消
          </Button>
          <Button
            onClick={handleChangePassword}
            variant="contained"
            disabled={
              changePasswordMutation.isPending ||
              !passwordData.currentPassword ||
              !passwordData.newPassword ||
              !passwordData.confirmPassword ||
              passwordData.newPassword !== passwordData.confirmPassword ||
              passwordData.newPassword.length < 6
            }
            size="small"
          >
            {changePasswordMutation.isPending ? '修改中...' : '确认修改'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

// 设置页面骨架屏组件
function SettingsPageSkeleton() {
  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        个人设置
      </Typography>

      {/* 个人信息骨架 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            个人信息
          </Typography>
          <FormSkeleton />
        </CardContent>
      </Card>

      {/* 安全设置骨架 */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            安全设置
          </Typography>
          <StatsCardSkeleton />
        </CardContent>
      </Card>
    </Box>
  );
}
