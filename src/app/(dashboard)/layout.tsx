"use client";

import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import SidebarNavigation from "../_components/sidebar-navigation";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();

  // 避免闪烁，直接返回null而不是显示加载器
  if (status === "loading") {
    return null;
  }

  if (!session) {
    redirect("/auth/signin");
  }

  return <SidebarNavigation>{children}</SidebarNavigation>;
}
