"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {
  Business,
  Person,
  Check,
  Star,
  Security,
  Support,
  Cloud,
  Api,
  AttachMoney,
  Assessment,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

const steps = ["选择类型", "填写信息", "完成升级"];

const features = {
  PERSONAL: [
    { icon: <Check />, text: "1000个证件管理" },
    { icon: <Check />, text: "50个自定义字段" },
    { icon: <Check />, text: "Telegram通知" },
    { icon: <Check />, text: "PWA支持" },
    { icon: <Check />, text: "数据导出" },
    { icon: <Support />, text: "邮件支持" },
    { icon: <AttachMoney />, text: "$118/年" },
  ],
  ENTERPRISE: [
    { icon: <Check />, text: "无限证件管理" },
    { icon: <Check />, text: "无限用户" },
    { icon: <Check />, text: "无限自定义字段" },
    { icon: <Check />, text: "多种通知方式" },
    { icon: <Api />, text: "API接口" },
    { icon: <Security />, text: "自定义品牌" },
    { icon: <Assessment />, text: "高级报表" },
    { icon: <Star />, text: "优先技术支持" },
    { icon: <AttachMoney />, text: "$288/年" },
  ],
};

export default function UpgradePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);

  // 获取当前用户的tenantId
  const tenantId = session?.user?.currentTenantId;
  const [tenantType, setTenantType] = useState<"PERSONAL" | "ENTERPRISE">("PERSONAL");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    phone: "",
    email: session?.user?.email || "",
    address: "",
    website: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const upgradeMutation = api.merchant.upgrade.useMutation({
    onSuccess: (data) => {
      setActiveStep(2);
      // 3秒后跳转到商户管理页面
      setTimeout(() => {
        router.push("/merchant");
      }, 3000);
    },
    onError: (error) => {
      setErrors({ submit: error.message });
    },
  });

  const handleNext = () => {
    if (activeStep === 0) {
      setActiveStep(1);
    } else if (activeStep === 1) {
      if (validateForm()) {
        handleUpgrade();
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "商户名称不能为空";
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "请输入有效的邮箱地址";
    }

    if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
      newErrors.website = "请输入有效的网站地址";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpgrade = () => {
    if (!tenantId) {
      alert('无法获取商户信息，请重新登录');
      return;
    }

    upgradeMutation.mutate({
      tenantId,
      type: tenantType,
      ...formData,
    });
  };

  const handleInputChange = (field: string) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Paper sx={{ p: 4, maxWidth: 800, mx: "auto" }}>
        <Typography variant="h4" align="center" gutterBottom>
          升级为商户
        </Typography>
        <Typography variant="body1" align="center" color="text.secondary" paragraph>
          选择适合您的商户类型，开始专业的证件管理之旅
        </Typography>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {errors.submit && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {errors.submit}
          </Alert>
        )}

        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              选择商户类型
            </Typography>
            <FormControl component="fieldset" fullWidth>
              <RadioGroup
                value={tenantType}
                onChange={(e) => setTenantType(e.target.value as "PERSONAL" | "ENTERPRISE")}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card 
                      variant="outlined"
                      sx={{ 
                        cursor: 'pointer',
                        border: tenantType === 'PERSONAL' ? 2 : 1,
                        borderColor: tenantType === 'PERSONAL' ? 'primary.main' : 'divider',
                      }}
                      onClick={() => setTenantType('PERSONAL')}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <FormControlLabel
                            value="PERSONAL"
                            control={<Radio />}
                            label=""
                            sx={{ mr: 1 }}
                          />
                          <Person sx={{ mr: 1 }} />
                          <Typography variant="h6">个人商户</Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          适合个人用户或小型工作室
                        </Typography>
                        <List dense>
                          {features.PERSONAL.map((feature, index) => (
                            <ListItem key={index} sx={{ px: 0 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                {feature.icon}
                              </ListItemIcon>
                              <ListItemText 
                                primary={feature.text}
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card 
                      variant="outlined"
                      sx={{ 
                        cursor: 'pointer',
                        border: tenantType === 'ENTERPRISE' ? 2 : 1,
                        borderColor: tenantType === 'ENTERPRISE' ? 'primary.main' : 'divider',
                      }}
                      onClick={() => setTenantType('ENTERPRISE')}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <FormControlLabel
                            value="ENTERPRISE"
                            control={<Radio />}
                            label=""
                            sx={{ mr: 1 }}
                          />
                          <Business sx={{ mr: 1 }} />
                          <Typography variant="h6">企业商户</Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          适合企业和团队使用
                        </Typography>
                        <List dense>
                          {features.ENTERPRISE.map((feature, index) => (
                            <ListItem key={index} sx={{ px: 0 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                {feature.icon}
                              </ListItemIcon>
                              <ListItemText 
                                primary={feature.text}
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        )}

        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              填写商户信息
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="商户名称"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="商户描述"
                  value={formData.description}
                  onChange={handleInputChange('description')}
                  multiline
                  rows={3}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="联系电话"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="联系邮箱"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  error={!!errors.email}
                  helperText={errors.email}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="地址"
                  value={formData.address}
                  onChange={handleInputChange('address')}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="网站地址"
                  value={formData.website}
                  onChange={handleInputChange('website')}
                  error={!!errors.website}
                  helperText={errors.website}
                  placeholder="https://example.com"
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        )}

        {activeStep === 2 && (
          <Box sx={{ textAlign: 'center' }}>
            <Check sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              升级成功！
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              恭喜您成功升级为{tenantType === 'ENTERPRISE' ? '企业' : '个人'}商户！
              您已获得30天免费试用期。
            </Typography>
            <Typography variant="body2" color="text.secondary">
              页面将在3秒后自动跳转到商户管理页面...
            </Typography>
          </Box>
        )}

        {activeStep < 2 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              上一步
            </Button>
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={upgradeMutation.isPending}
            >
              {activeStep === 1 ?
                (upgradeMutation.isPending ? '升级中...' : '完成升级') :
                '下一步'
              }
            </Button>
          </Box>
        )}
      </Paper>
    </Box>
  );
}
