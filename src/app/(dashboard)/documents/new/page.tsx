"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  Typography,
  TextField,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Alert,
  IconButton,
  Chip,
} from "@mui/material";
import { Add, Delete, Settings } from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { zhCN } from "@mui/x-date-pickers/locales";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import { api } from "~/trpc/react";


import { FieldManagementDialog } from "~/app/_components/field-management-dialog";
import { DocumentTypeManagementDialog } from "~/app/_components/document-type-management-dialog";

// 类型定义
interface DynamicField {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "textarea" | "select";
  value: any;
  options?: string[];
  placeholder?: string;
  required?: boolean;
}

interface Certificate {
  id: string;
  certType: string;
  issueDate: Date | null;
  expiryDate: Date | null;
  reminderDate: Date | null;
  documentFields: DynamicField[];
}

// 转换函数
const convertDynamicFieldsToCustomFields = (fields: DynamicField[]) => {
  const result: Record<string, any> = {};
  fields.forEach((field) => {
    if (field.value !== null && field.value !== undefined && field.value !== "") {
      result[field.name] = field.value;
    }
  });
  return result;
};

export default function AddDocumentPageOptimized() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const tenantId = session?.user?.currentTenantId;





  // 客户信息（一次性填写）
  const [customerData, setCustomerData] = useState({
    customerName: "",
    phone: "",
  });

  // 证件列表（支持多个证件）
  const [certificates, setCertificates] = useState<Certificate[]>([
    {
      id: Date.now().toString(),
      certType: "",
      issueDate: null,
      expiryDate: null,
      reminderDate: null,
      documentFields: [],
    }
  ]);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [fieldManagementOpen, setFieldManagementOpen] = useState(false);
  const [documentTypeManagementOpen, setDocumentTypeManagementOpen] = useState(false);

  // 客户自定义字段（全局，所有证件共享）
  const [customerFields, setCustomerFields] = useState<DynamicField[]>([]);

  // API 查询 - 优化加载逻辑
  const { data: documentTypes = [], isLoading: isLoadingTypes, refetch: refetchDocumentTypes } = api.documentType.getAll.useQuery(
    { tenantId: tenantId! },
    {
      enabled: !!tenantId,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      refetchOnWindowFocus: false, // 避免不必要的重新获取
    }
  );

  // 查询现有的自定义字段
  const { data: existingFields = [], isLoading: isLoadingFields } = api.customField.getAll.useQuery(
    { tenantId: tenantId! },
    {
      enabled: !!tenantId,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      refetchOnWindowFocus: false, // 避免不必要的重新获取
    }
  );

  const createMutation = api.document.create.useMutation({
    onSuccess: () => {
      console.log("证件创建成功");
    },
    onError: (error) => {
      console.error("创建证件失败：", error.message);
    },
  });

  // 简化加载状态检查，避免闪烁
  if (status === "loading" || !session) {
    return null; // 返回null而不是Loading文本，避免闪烁
  }

  // 数据加载中时显示基本表单结构，避免布局跳动
  const isDataLoading = isLoadingTypes || isLoadingFields;

  // 处理客户信息输入
  const handleCustomerInputChange = (field: string, value: any) => {
    setCustomerData(prev => ({
      ...prev,
      [field]: value,
    }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // 处理证件信息输入
  const handleCertificateInputChange = (certId: string, field: string, value: any) => {
    setCertificates(prev => prev.map(cert => 
      cert.id === certId 
        ? { ...cert, [field]: value }
        : cert
    ));
    // 清除对应字段的错误
    const errorKey = `${certId}_${field}`;
    if (errors[errorKey]) {
      setErrors(prev => ({
        ...prev,
        [errorKey]: "",
      }));
    }
  };

  // 添加新证件
  const addCertificate = () => {
    setCertificates(prev => [...prev, {
      id: Date.now().toString(),
      certType: "",
      issueDate: null,
      expiryDate: null,
      reminderDate: null,
      documentFields: [],
    }]);
  };

  // 删除证件
  const removeCertificate = (certId: string) => {
    if (certificates.length > 1) {
      setCertificates(prev => prev.filter(cert => cert.id !== certId));
    }
  };

  // 更新证件的自定义字段
  const updateCertificateFields = (certId: string, fields: DynamicField[]) => {
    setCertificates(prev => prev.map(cert => 
      cert.id === certId 
        ? { ...cert, documentFields: fields }
        : cert
    ));
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // 验证客户信息
    if (!customerData.customerName.trim()) {
      newErrors.customerName = "客户姓名不能为空";
    }

    // 验证每个证件
    certificates.forEach((cert, index) => {
      const prefix = `${cert.id}_`;
      
      if (!cert.certType) {
        newErrors[`${prefix}certType`] = "请选择证件类型";
      }

      if (!cert.expiryDate) {
        newErrors[`${prefix}expiryDate`] = "请选择到期日期";
      } else if (cert.expiryDate < new Date()) {
        newErrors[`${prefix}expiryDate`] = "到期日期不能早于今天";
      }

      // 验证提醒日期
      if (cert.reminderDate && cert.expiryDate && cert.reminderDate > cert.expiryDate) {
        newErrors[`${prefix}reminderDate`] = "提醒日期不能晚于到期日期";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (!tenantId) {
        throw new Error("商户ID不能为空");
      }

      // 为每个证件创建记录
      for (const cert of certificates) {
        // 合并自定义字段数据
        const customerCustomFields = convertDynamicFieldsToCustomFields(customerFields);
        const documentCustomFields = convertDynamicFieldsToCustomFields(cert.documentFields);
        const allCustomFields = { ...customerCustomFields, ...documentCustomFields };

        // 验证必需的日期字段
        if (!cert.expiryDate) {
          throw new Error("到期日期是必需的");
        }

        console.log('Creating document with data:', {
          tenantId,
          customerName: customerData.customerName,
          phone: customerData.phone || undefined,
          certType: cert.certType,
          issueDate: cert.issueDate,
          expiryDate: cert.expiryDate,
          reminderDate: cert.reminderDate,
          customFields: allCustomFields,
        });

        await createMutation.mutateAsync({
          tenantId,
          customerName: customerData.customerName,
          phone: customerData.phone || undefined,
          certType: cert.certType as any,
          issueDate: cert.issueDate || undefined,
          expiryDate: cert.expiryDate,
          reminderDate: cert.reminderDate || undefined,
          customFields: Object.keys(allCustomFields).length > 0 ? allCustomFields : undefined,
        });
      }

      // 重置表单
      setCustomerData({
        customerName: "",
        phone: "",
      });
      setCertificates([{
        id: Date.now().toString(),
        certType: "",
        issueDate: null,
        expiryDate: null,
        reminderDate: null,
        documentFields: [],
      }]);
      setCustomerFields([]);
      setErrors({});

      // 跳转到证件列表页面
      router.push("/documents");
    } catch (error) {
      console.error("提交失败:", error);
    }
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale="zh-cn"
      localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
    >
      <Box sx={{ p: { xs: 2, sm: 3 } }}>


        {createMutation.error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            创建证件失败：{createMutation.error.message}
          </Alert>
        )}

        <Box sx={{ maxWidth: 700, mx: 'auto', px: 2 }}>
          <form onSubmit={handleSubmit}>
            {/* 客户信息区域 */}
            <Card sx={{ mb: 3 }}>
              <CardHeader
                title="客户信息"
                action={
                  <Button
                    size="small"
                    onClick={() => {
                      if (tenantId) {
                        setFieldManagementOpen(true);
                      }
                    }}
                    disabled={!tenantId || isDataLoading}
                    startIcon={<Settings />}
                  >
                    字段管理
                  </Button>
                }
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="客户姓名"
                      required
                      value={customerData.customerName}
                      onChange={(e) => handleCustomerInputChange("customerName", e.target.value)}
                      error={!!errors.customerName}
                      helperText={errors.customerName}
                      size="small"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="联系电话"
                      placeholder="请输入联系电话（支持国际号码）"
                      value={customerData.phone}
                      onChange={(e) => handleCustomerInputChange("phone", e.target.value)}
                      error={!!errors.phone}
                      helperText={errors.phone}
                      size="small"
                    />
                  </Grid>

                  {/* 客户自定义字段选择 */}
                  {(existingFields.filter((f: any) => f.category === 'customer').length > 0 || tenantId) && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        选择客户字段：
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                        {existingFields
                          .filter((f: any) => f.category === 'customer')
                          .map((field: any) => {
                            const isSelected = customerFields.some(cf => cf.id === field.id);
                            return (
                              <Chip
                                key={field.id}
                                label={field.name}
                                variant={isSelected ? "filled" : "outlined"}
                                color={isSelected ? "primary" : "default"}
                                onClick={() => {
                                  if (isSelected) {
                                    // 取消选择
                                    setCustomerFields(prev => prev.filter(f => f.id !== field.id));
                                  } else {
                                    // 选择字段
                                    const newField: DynamicField = {
                                      id: field.id,
                                      name: field.name,
                                      type: field.type as any,
                                      value: field.defaultValue || "",
                                      options: field.options ? (field.options as string[]) : [],
                                      placeholder: field.placeholder || "",
                                      required: field.required || false,
                                    };
                                    setCustomerFields(prev => [...prev, newField]);
                                  }
                                }}
                                size="small"
                              />
                            );
                          })}

                        {/* +号按钮作为最后一个选项 */}
                        {tenantId && (
                          <Chip
                            label="+ 添加字段"
                            variant="outlined"
                            color="primary"
                            onClick={() => {
                              if (tenantId) {
                                setFieldManagementOpen(true);
                              }
                            }}
                            size="small"
                            sx={{
                              borderStyle: 'dashed',
                              '&:hover': {
                                backgroundColor: 'primary.50',
                              },
                            }}
                          />
                        )}
                      </Box>
                    </Grid>
                  )}

                  {/* 已选择的客户字段输入框 */}
                  {customerFields.map((field) => (
                    <Grid item xs={12} sm={6} key={field.id}>
                      {field.type === 'select' ? (
                        <FormControl fullWidth required={field.required} size="small">
                          <InputLabel>{field.name}</InputLabel>
                          <Select
                            value={field.value || ""}
                            onChange={(e) => {
                              setCustomerFields(prev => prev.map(f =>
                                f.id === field.id ? { ...f, value: e.target.value } : f
                              ));
                            }}
                            label={field.name}
                          >
                            {field.options?.map((option) => (
                              <MenuItem key={option} value={option}>
                                {option}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      ) : field.type === 'date' ? (
                        <DatePicker
                          label={field.name}
                          value={field.value ? dayjs(field.value) : null}
                          onChange={(newValue) => {
                            setCustomerFields(prev => prev.map(f =>
                              f.id === field.id ? { ...f, value: newValue ? newValue.format('YYYY-MM-DD') : '' } : f
                            ));
                          }}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              required: field.required,
                              size: "small",
                            },
                          }}
                        />
                      ) : (
                        <TextField
                          fullWidth
                          label={field.name}
                          placeholder={field.placeholder || `请输入${field.name}`}
                          value={field.value || ""}
                          onChange={(e) => {
                            setCustomerFields(prev => prev.map(f =>
                              f.id === field.id ? { ...f, value: e.target.value } : f
                            ));
                          }}
                          type={field.type === 'number' ? 'number' : 'text'}
                          multiline={field.type === 'textarea'}
                          rows={field.type === 'textarea' ? 3 : 1}
                          required={field.required}
                          size="small"
                        />
                      )}
                    </Grid>
                  ))}


                </Grid>
              </CardContent>
            </Card>

            {/* 证件信息区域 */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">证件信息</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  size="small"
                  onClick={() => setDocumentTypeManagementOpen(true)}
                  disabled={isDataLoading}
                  startIcon={<Settings />}
                >
                  证件类型管理
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={addCertificate}
                  startIcon={<Add />}
                >
                  添加证件
                </Button>
              </Box>
            </Box>

            {certificates.map((cert, index) => (
              <Card key={cert.id} sx={{ mb: 2 }}>
                <CardHeader
                  title={`证件 ${index + 1}`}
                  action={
                    certificates.length > 1 && (
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => removeCertificate(cert.id)}
                      >
                        <Delete />
                      </IconButton>
                    )
                  }
                />
                <CardContent>
                  <Grid container spacing={2}>
                    {/* 第一行：证件类型和签发日期 */}
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth required error={!!errors[`${cert.id}_certType`]} size="small">
                        <InputLabel>证件类型</InputLabel>
                        <Select
                          value={cert.certType}
                          label="证件类型"
                          onChange={(e) => handleCertificateInputChange(cert.id, "certType", e.target.value)}
                          disabled={isDataLoading}
                        >
                          {isDataLoading ? (
                            <MenuItem disabled>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box
                                  sx={{
                                    width: 12,
                                    height: 12,
                                    borderRadius: '50%',
                                    backgroundColor: 'grey.300',
                                  }}
                                />
                                加载中...
                              </Box>
                            </MenuItem>
                          ) : (
                            documentTypes.map((type: any) => (
                              <MenuItem key={type.id} value={type.name}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Box
                                    sx={{
                                      width: 12,
                                      height: 12,
                                      borderRadius: '50%',
                                      backgroundColor: type.color,
                                    }}
                                  />
                                  {type.name}
                                </Box>
                              </MenuItem>
                            ))
                          )}
                        </Select>
                        {errors[`${cert.id}_certType`] && (
                          <FormHelperText>{errors[`${cert.id}_certType`]}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <DatePicker
                        label="签发日期"
                        value={cert.issueDate ? dayjs(cert.issueDate) : null}
                        onChange={(date) => handleCertificateInputChange(cert.id, "issueDate", date ? date.toDate() : null)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            size: "small",
                          },
                        }}
                      />
                    </Grid>

                    {/* 第二行：到期日期和提醒日期 */}
                    <Grid item xs={12} sm={6}>
                      <DatePicker
                        label="到期日期"
                        value={cert.expiryDate ? dayjs(cert.expiryDate) : null}
                        onChange={(date) => handleCertificateInputChange(cert.id, "expiryDate", date ? date.toDate() : null)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            size: "small",
                            required: true,
                            error: !!errors[`${cert.id}_expiryDate`],
                            helperText: errors[`${cert.id}_expiryDate`],
                          },
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <DatePicker
                        label="提醒日期"
                        value={cert.reminderDate ? dayjs(cert.reminderDate) : null}
                        onChange={(date) => handleCertificateInputChange(cert.id, "reminderDate", date ? date.toDate() : null)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            size: "small",
                            error: !!errors[`${cert.id}_reminderDate`],
                            helperText: errors[`${cert.id}_reminderDate`],
                          },
                        }}
                      />
                    </Grid>

                    {/* 证件自定义字段选择 */}
                    {(existingFields.filter((f: any) => f.category === 'document').length > 0 || tenantId) && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                          选择证件字段：
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          {existingFields
                            .filter((f: any) => f.category === 'document')
                            .map((field: any) => {
                              const isSelected = cert.documentFields.some(cf => cf.id === field.id);
                              return (
                                <Chip
                                  key={field.id}
                                  label={field.name}
                                  variant={isSelected ? "filled" : "outlined"}
                                  color={isSelected ? "primary" : "default"}
                                  onClick={() => {
                                    if (isSelected) {
                                      // 取消选择
                                      updateCertificateFields(cert.id, cert.documentFields.filter(f => f.id !== field.id));
                                    } else {
                                      // 选择字段
                                      const newField: DynamicField = {
                                        id: field.id,
                                        name: field.name,
                                        type: field.type as any,
                                        value: field.defaultValue || "",
                                        options: field.options ? (field.options as string[]) : [],
                                        placeholder: field.placeholder || "",
                                        required: field.required || false,
                                      };
                                      updateCertificateFields(cert.id, [...cert.documentFields, newField]);
                                    }
                                  }}
                                  size="small"
                                />
                              );
                            })}

                          {/* +号按钮作为最后一个选项 */}
                          {tenantId && (
                            <Chip
                              label="+ 添加字段"
                              variant="outlined"
                              color="primary"
                              onClick={() => {
                                if (tenantId) {
                                  setFieldManagementOpen(true);
                                }
                              }}
                              size="small"
                              sx={{
                                borderStyle: 'dashed',
                                '&:hover': {
                                  backgroundColor: 'primary.50',
                                },
                              }}
                            />
                          )}
                        </Box>
                      </Grid>
                    )}

                    {/* 已选择的证件字段输入框 */}
                    {cert.documentFields.map((field) => (
                      <Grid item xs={12} sm={6} key={field.id}>
                        {field.type === 'select' ? (
                          <FormControl fullWidth required={field.required} size="small">
                            <InputLabel>{field.name}</InputLabel>
                            <Select
                              value={field.value || ""}
                              onChange={(e) => {
                                updateCertificateFields(cert.id, cert.documentFields.map(f =>
                                  f.id === field.id ? { ...f, value: e.target.value } : f
                                ));
                              }}
                              label={field.name}
                            >
                              {field.options?.map((option) => (
                                <MenuItem key={option} value={option}>
                                  {option}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        ) : field.type === 'date' ? (
                          <DatePicker
                            label={field.name}
                            value={field.value ? dayjs(field.value) : null}
                            onChange={(newValue) => {
                              updateCertificateFields(cert.id, cert.documentFields.map(f =>
                                f.id === field.id ? { ...f, value: newValue ? newValue.format('YYYY-MM-DD') : '' } : f
                              ));
                            }}
                            slotProps={{
                              textField: {
                                fullWidth: true,
                                required: field.required,
                                size: "small",
                              },
                            }}
                          />
                        ) : (
                          <TextField
                            fullWidth
                            label={field.name}
                            placeholder={field.placeholder || `请输入${field.name}`}
                            value={field.value || ""}
                            onChange={(e) => {
                              updateCertificateFields(cert.id, cert.documentFields.map(f =>
                                f.id === field.id ? { ...f, value: e.target.value } : f
                              ));
                            }}
                            type={field.type === 'number' ? 'number' : 'text'}
                            multiline={field.type === 'textarea'}
                            rows={field.type === 'textarea' ? 3 : 1}
                            required={field.required}
                            size="small"
                          />
                        )}
                      </Grid>
                    ))}


                  </Grid>
                </CardContent>
              </Card>
            ))}

            {/* 提交按钮 */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
              <Button
                variant="outlined"
                onClick={() => router.push("/documents")}
              >
                取消
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={createMutation.isPending}
              >
                {createMutation.isPending ? "创建中..." : `创建 ${certificates.length} 个证件`}
              </Button>
            </Box>
          </form>
        </Box>

        {/* 字段管理对话框 */}
        {tenantId && (
          <FieldManagementDialog
            open={fieldManagementOpen}
            onClose={() => setFieldManagementOpen(false)}
            tenantId={tenantId}
            onSave={(savedCustomerFields, savedDocumentFields) => {
              // 将 CustomField 转换为 DynamicField
              const convertToDynamicField = (customField: any): DynamicField => ({
                id: customField.id,
                name: customField.name,
                type: customField.type,
                value: customField.defaultValue || "",
                options: customField.options || [],
                placeholder: customField.placeholder || "",
                required: customField.required || false,
              });

              // 更新客户字段
              setCustomerFields(savedCustomerFields.map(convertToDynamicField));

              // 更新所有证件的字段
              setCertificates(prev => prev.map(cert => ({
                ...cert,
                documentFields: savedDocumentFields.map(convertToDynamicField)
              })));

              console.log("字段管理保存完成");
            }}
          />
        )}

        {/* 证件类型管理对话框 */}
        {tenantId && (
          <DocumentTypeManagementDialog
            open={documentTypeManagementOpen}
            onClose={() => {
              setDocumentTypeManagementOpen(false);
              // 刷新证件类型列表
              refetchDocumentTypes();
            }}
            tenantId={tenantId}
          />
        )}
      </Box>
    </LocalizationProvider>
  );
}
