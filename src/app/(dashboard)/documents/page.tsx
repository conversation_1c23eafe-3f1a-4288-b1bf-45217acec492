"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  MenuItem as MenuItemComponent,
  Alert,
  Pagination,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Grid,
} from "@mui/material";
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Edit,
  Delete,
  Visibility,
  Warning,
  Error,
  CheckCircle,
  Info,
  Settings,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { FieldManagementDialog } from "~/app/_components/field-management-dialog";
import { DocumentTypeManagementDialog } from "~/app/_components/document-type-management-dialog";
import { DocumentEditForm } from "~/app/_components/document-edit-form";
import { PermissionGuard, AdminOnly, usePermissionCheck } from "~/app/_components/permission-guard";
import { RoleIndicator } from "~/app/_components/role-indicator";
import { DocumentCardSkeleton, PageHeaderSkeleton, TableRowSkeleton } from "~/components/ui/Skeleton";


// 证件状态计算
function getExpiryStatus(validUntil: Date) {
  const today = new Date();
  const diffTime = validUntil.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return { status: "expired", color: "error", label: "已过期", days: Math.abs(diffDays) };
  } else if (diffDays <= 7) {
    return { status: "critical", color: "error", label: "即将过期", days: diffDays };
  } else if (diffDays <= 30) {
    return { status: "warning", color: "warning", label: "需要关注", days: diffDays };
  } else {
    return { status: "normal", color: "success", label: "正常", days: diffDays };
  }
}

// 倒计时Hook
function useCountdown(targetDate: Date) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const target = new Date(targetDate).getTime();
      const difference = target - now;

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000),
        });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  return timeLeft;
}

export default function DocumentsPage() {
  const { data: session, status } = useSession();
  const { permissions, canManage } = usePermissionCheck();
  const [searchTerm, setSearchTerm] = useState("");
  const [certTypeFilter, setCertTypeFilter] = useState("ALL");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [page, setPage] = useState(1);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);
  const [fieldManagementOpen, setFieldManagementOpen] = useState(false);
  const [documentTypeManagementOpen, setDocumentTypeManagementOpen] = useState(false);
  const [orderBy, setOrderBy] = useState<'customerName' | 'certType' | 'validUntil' | 'createdAt'>('validUntil');
  const [order, setOrder] = useState<'asc' | 'desc'>('asc');

  if (status === "loading") {
    return <DocumentsPageSkeleton />;
  }

  if (!session) {
    redirect("/auth/login");
  }

  const tenantId = session.user.currentTenantId;
  const limit = 12;
  const isSuperAdmin = permissions.isSuperAdmin;

  // 获取租户列表（仅超级管理员需要）
  const { data: tenants } = api.tenant.getAll.useQuery(undefined, {
    enabled: isSuperAdmin,
  });

  // 设置默认选中的租户
  useEffect(() => {
    if (isSuperAdmin && tenants && tenants.length > 0 && !selectedTenantId) {
      setSelectedTenantId(tenants[0]?.id || null);
    } else if (!isSuperAdmin && tenantId) {
      setSelectedTenantId(tenantId);
    }
  }, [isSuperAdmin, tenants, tenantId, selectedTenantId]);

  // 获取证件数据
  const { data: documentsData, refetch } = api.document.getAll.useQuery({
    tenantId: isSuperAdmin ? selectedTenantId : tenantId,
    page,
    limit,
    search: searchTerm,
    certType: certTypeFilter === "ALL" ? undefined : certTypeFilter,
    status: statusFilter === "ALL" ? undefined : statusFilter,
  }, {
    enabled: Boolean(isSuperAdmin ? selectedTenantId : tenantId),
  });

  // 获取证件类型数据用于筛选器
  const { data: documentTypes = [] } = api.documentType.getAll.useQuery({
    tenantId: isSuperAdmin ? selectedTenantId : tenantId,
  }, {
    enabled: Boolean(isSuperAdmin ? selectedTenantId : tenantId),
  });

  const documents = documentsData?.documents ?? [];
  const totalPages = documentsData?.pagination.totalPages ?? 1;

  // 更新证件
  const updateMutation = api.document.update.useMutation({
    onSuccess: () => {
      refetch();
      setEditDialogOpen(false);
      setSelectedDocument(null);
    },
    onError: (error) => {
      console.error("更新证件失败:", error);
    },
  });

  // 删除证件
  const deleteMutation = api.document.delete.useMutation({
    onSuccess: () => {
      refetch();
      setDeleteDialogOpen(false);
      setSelectedDocument(null);
    },
  });

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, document: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedDocument(document);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleView = () => {
    setViewDialogOpen(true);
    handleMenuClose();
  };

  const handleEdit = () => {
    setEditDialogOpen(true);
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleEditSave = (formData: any) => {
    if (selectedDocument) {
      const currentTenantId = isSuperAdmin ? selectedTenantId : tenantId;
      if (currentTenantId) {
        updateMutation.mutate({
          ...formData,
          tenantId: currentTenantId,
        });
      }
    }
  };

  const confirmDelete = () => {
    if (selectedDocument) {
      const currentTenantId = isSuperAdmin ? selectedTenantId : tenantId;
      if (currentTenantId) {
        deleteMutation.mutate({
          id: selectedDocument.id,
          tenantId: currentTenantId
        });
      }
    }
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleSort = (property: 'customerName' | 'certType' | 'validUntil' | 'createdAt') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // 排序函数
  const sortedDocuments = [...documents].sort((a, b) => {
    let aValue: any = a[orderBy];
    let bValue: any = b[orderBy];

    if (orderBy === 'validUntil' || orderBy === 'createdAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (order === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // 倒计时显示组件
  const CountdownDisplay = ({ validUntil }: { validUntil: Date }) => {
    const expiryStatus = getExpiryStatus(validUntil);

    if (expiryStatus.status === 'expired') {
      return (
        <Chip
          label={`已过期 ${expiryStatus.days} 天`}
          color="error"
          size="small"
          icon={<Error />}
        />
      );
    }

    return (
      <Chip
        label={`${expiryStatus.days} 天`}
        color={expiryStatus.color as any}
        size="small"
        variant={expiryStatus.status === 'critical' ? 'filled' : 'outlined'}
      />
    );
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>


      <Box sx={{
        display: "flex",
        justifyContent: "flex-end",
        alignItems: "center",
        gap: 2,
        mb: 3
      }}>
        {/* 证件类型管理 - 仅管理员可见 */}
        <AdminOnly>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => setDocumentTypeManagementOpen(true)}
            size="medium"
          >
            证件类型
          </Button>
        </AdminOnly>

        {/* 字段管理 - 仅管理员可见 */}
        <AdminOnly>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => setFieldManagementOpen(true)}
            size="medium"
          >
            字段管理
          </Button>
        </AdminOnly>

        {/* 添加证件 - 所有用户可见 */}
        <PermissionGuard requiredPermission="canCreateDocuments">
          <Button
            variant="contained"
            startIcon={<Add />}
            href="/documents/new"
            size="medium"
          >
            添加证件
          </Button>
        </PermissionGuard>
      </Box>

      {/* 搜索和筛选 */}
      <Paper sx={{ p: { xs: 2, sm: 2 }, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          {/* 租户选择器（仅超级管理员） */}
          {isSuperAdmin && (
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>选择租户</InputLabel>
                <Select
                  value={selectedTenantId || ""}
                  onChange={(e) => setSelectedTenantId(e.target.value || null)}
                  label="选择租户"
                >
                  {tenants?.map((tenant) => (
                    <MenuItem key={tenant.id} value={tenant.id}>
                      {tenant.name} ({tenant.type === "PERSONAL" ? "个人" : "企业"})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12} sm={12} md={isSuperAdmin ? 3 : 4}>
            <TextField
              fullWidth
              placeholder="搜索客户姓名、联系电话..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>证件类型</InputLabel>
              <Select
                value={certTypeFilter}
                label="证件类型"
                onChange={(e) => setCertTypeFilter(e.target.value)}
              >
                <MenuItem value="ALL">全部</MenuItem>
                {documentTypes.map((type: any) => (
                  <MenuItem key={type.id} value={type.name}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>到期状态</InputLabel>
              <Select
                value={statusFilter}
                label="到期状态"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="ALL">全部</MenuItem>
                <MenuItem value="EXPIRED">已过期</MenuItem>
                <MenuItem value="EXPIRING_SOON">即将过期</MenuItem>
                <MenuItem value="NORMAL">正常</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterList />}
              size="medium"
              onClick={() => {
                setSearchTerm("");
                setCertTypeFilter("ALL");
                setStatusFilter("ALL");
                setPage(1);
              }}
            >
              重置
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 证件列表 */}
      {documents.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: "center" }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            暂无证件数据
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            开始添加您的第一个证件吧
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            href="/documents/new"
          >
            添加证件
          </Button>
        </Paper>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'customerName'}
                      direction={orderBy === 'customerName' ? order : 'asc'}
                      onClick={() => handleSort('customerName')}
                    >
                      客户信息
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'certType'}
                      direction={orderBy === 'certType' ? order : 'asc'}
                      onClick={() => handleSort('certType')}
                    >
                      证件类型
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'createdAt'}
                      direction={orderBy === 'createdAt' ? order : 'asc'}
                      onClick={() => handleSort('createdAt')}
                    >
                      创建日期
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'validUntil'}
                      direction={orderBy === 'validUntil' ? order : 'asc'}
                      onClick={() => handleSort('validUntil')}
                    >
                      到期日期
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>到期倒计时</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell align="right">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedDocuments.map((document) => {
                  const expiryStatus = getExpiryStatus(new Date(document.validUntil));
                  const StatusIcon = expiryStatus.status === "expired" ? Error :
                                   expiryStatus.status === "critical" ? Warning :
                                   expiryStatus.status === "warning" ? Info : CheckCircle;

                  return (
                    <TableRow key={document.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {document.customerName.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium">
                              {document.customerName}
                            </Typography>
                            {document.phone && (
                              <Typography variant="body2" color="text.secondary">
                                {document.phone}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={document.certType}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(document.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(document.validUntil).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <CountdownDisplay validUntil={new Date(document.validUntil)} />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <StatusIcon color={expiryStatus.color as any} fontSize="small" />
                          <Typography variant="body2" color={`${expiryStatus.color}.main`}>
                            {expiryStatus.label}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuClick(e, document)}
                        >
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 分页 */}
          {totalPages > 1 && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* 操作菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {/* 查看详情 - 所有用户可见 */}
        <PermissionGuard requiredPermission="canViewDocuments">
          <MenuItemComponent onClick={handleView}>
            <Visibility sx={{ mr: 1 }} />
            查看详情
          </MenuItemComponent>
        </PermissionGuard>

        {/* 编辑 - 所有用户可见 */}
        <PermissionGuard requiredPermission="canEditDocuments">
          <MenuItemComponent onClick={handleEdit}>
            <Edit sx={{ mr: 1 }} />
            编辑
          </MenuItemComponent>
        </PermissionGuard>

        {/* 删除 - 仅管理员可见 */}
        <PermissionGuard requiredPermission="canDeleteDocuments">
          <MenuItemComponent onClick={handleDelete} sx={{ color: "error.main" }}>
            <Delete sx={{ mr: 1 }} />
            删除
          </MenuItemComponent>
        </PermissionGuard>
      </Menu>

      {/* 查看详情对话框 */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {selectedDocument?.customerName?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6">证件详情</Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedDocument?.customerName}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedDocument && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    基本信息
                  </Typography>
                  <Stack spacing={2}>
                    <TextField
                      label="客户姓名"
                      value={selectedDocument.customerName}
                      InputProps={{ readOnly: true }}
                      size="small"
                      fullWidth
                    />
                    <TextField
                      label="证件类型"
                      value={selectedDocument.certType}
                      InputProps={{ readOnly: true }}
                      size="small"
                      fullWidth
                    />
                    {selectedDocument.phone && (
                      <TextField
                        label="联系电话"
                        value={selectedDocument.phone}
                        InputProps={{ readOnly: true }}
                        size="small"
                        fullWidth
                      />
                    )}
                  </Stack>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    证件信息
                  </Typography>
                  <Stack spacing={2}>
                    <TextField
                      label="创建日期"
                      value={new Date(selectedDocument.createdAt).toLocaleDateString()}
                      InputProps={{ readOnly: true }}
                      size="small"
                      fullWidth
                    />
                    <TextField
                      label="到期日期"
                      value={new Date(selectedDocument.validUntil).toLocaleDateString()}
                      InputProps={{ readOnly: true }}
                      size="small"
                      fullWidth
                    />
                    {selectedDocument.certNumber && (
                      <TextField
                        label="证件编号"
                        value={selectedDocument.certNumber}
                        InputProps={{ readOnly: true }}
                        size="small"
                        fullWidth
                      />
                    )}
                    {selectedDocument.issueBy && (
                      <TextField
                        label="签发机关"
                        value={selectedDocument.issueBy}
                        InputProps={{ readOnly: true }}
                        size="small"
                        fullWidth
                      />
                    )}
                  </Stack>
                </Grid>

                {/* 状态信息 */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    状态信息
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                    <CountdownDisplay validUntil={new Date(selectedDocument.validUntil)} />
                    {(() => {
                      const status = getExpiryStatus(new Date(selectedDocument.validUntil));
                      const StatusIcon = status.status === "expired" ? Error :
                                       status.status === "critical" ? Warning :
                                       status.status === "warning" ? Info : CheckCircle;
                      return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <StatusIcon color={status.color as any} fontSize="small" />
                          <Typography variant="body2" color={`${status.color}.main`}>
                            {status.label}
                          </Typography>
                        </Box>
                      );
                    })()}
                  </Box>
                </Grid>

                {/* 自定义字段 */}
                {selectedDocument.customFields && Object.keys(selectedDocument.customFields).length > 0 && (
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                      自定义字段
                    </Typography>
                    <Grid container spacing={2}>
                      {Object.entries(selectedDocument.customFields).map(([key, value]) => (
                        <Grid item xs={12} sm={6} key={key}>
                          <TextField
                            label={key}
                            value={value as string}
                            InputProps={{ readOnly: true }}
                            size="small"
                            fullWidth
                          />
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>关闭</Button>
          <Button
            variant="contained"
            onClick={() => {
              setViewDialogOpen(false);
              setEditDialogOpen(true);
            }}
          >
            编辑
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {selectedDocument?.customerName?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6">编辑证件</Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedDocument?.customerName} - {selectedDocument?.certType}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0, overflow: 'auto' }}>
          {selectedDocument && (
            <DocumentEditForm
              document={selectedDocument}
              tenantId={isSuperAdmin ? selectedTenantId! : tenantId!}
              onSave={handleEditSave}
              onCancel={() => setEditDialogOpen(false)}
            />
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            disabled={updateMutation.isLoading}
          >
            取消
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="document-edit-form"
            disabled={updateMutation.isLoading}
          >
            {updateMutation.isLoading ? "保存中..." : "保存"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除 "{selectedDocument?.customerName}" 的证件吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button
            onClick={confirmDelete}
            color="error"
            disabled={deleteMutation.isLoading}
          >
            {deleteMutation.isLoading ? "删除中..." : "删除"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 字段管理对话框 */}
      <FieldManagementDialog
        open={fieldManagementOpen}
        onClose={() => setFieldManagementOpen(false)}
        tenantId={isSuperAdmin ? selectedTenantId || "" : tenantId || ""}
        onSave={(customerFields, documentFields) => {
          console.log("字段管理保存完成:", { customerFields, documentFields });
          // 可以在这里添加刷新证件列表等逻辑
        }}
      />

      {/* 证件类型管理对话框 */}
      {(isSuperAdmin ? selectedTenantId : tenantId) && (
        <DocumentTypeManagementDialog
          open={documentTypeManagementOpen}
          onClose={() => setDocumentTypeManagementOpen(false)}
          tenantId={isSuperAdmin ? selectedTenantId! : tenantId!}
        />
      )}
    </Box>
  );
}

// 证件页面骨架屏组件
function DocumentsPageSkeleton() {
  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <PageHeaderSkeleton />

      {/* 搜索和筛选骨架 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TableRowSkeleton columns={1} />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TableRowSkeleton columns={1} />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TableRowSkeleton columns={1} />
          </Grid>
          <Grid item xs={12} sm={2}>
            <TableRowSkeleton columns={1} />
          </Grid>
        </Grid>
      </Paper>

      {/* 表格骨架 */}
      <Paper>
        <Box sx={{ p: 2 }}>
          {Array.from({ length: 10 }).map((_, index) => (
            <TableRowSkeleton key={index} columns={6} />
          ))}
        </Box>
      </Paper>
    </Box>
  );
}
