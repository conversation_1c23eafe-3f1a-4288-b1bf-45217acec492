import "~/styles/globals.css";

import { type Metadata, type Viewport } from "next";
import { <PERSON>ei<PERSON> } from "next/font/google";
import React from "react";
import { SessionProvider } from "next-auth/react";
import { TRPCReactProvider } from "~/trpc/react";
import { ClientThemeProvider } from "./_components/theme-provider";
import ServiceWorkerRegistration from "./_components/service-worker-registration";

export const metadata: Metadata = {
  title: "证件到期提醒管理系统",
  description: "专业的证件到期提醒与管理工具，支持多租户",
  manifest: "/manifest.json",
  icons: [
    { rel: "icon", url: "/favicon.svg", type: "image/svg+xml" },
    { rel: "icon", url: "/favicon.ico" },
    { rel: "apple-touch-icon", url: "/icon-192x192.svg" },
    { rel: "apple-touch-icon", url: "/icon-512x512.svg", sizes: "512x512" },
  ],
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#1976d2",
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="zh-CN" className={`${geist.variable}`}>
      <body>
        <SessionProvider>
          <ClientThemeProvider>
            <TRPCReactProvider>{children}</TRPCReactProvider>
            <ServiceWorkerRegistration />
          </ClientThemeProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
