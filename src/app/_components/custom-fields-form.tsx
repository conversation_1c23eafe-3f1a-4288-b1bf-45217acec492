"use client";

import { useState } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Divider,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import {
  Add,
  Delete,
} from "@mui/icons-material";
import dayjs from "dayjs";

interface DynamicField {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "textarea" | "select";
  value: any;
  options?: string[];
  placeholder?: string;
  required?: boolean;
}

interface DynamicFieldsFormProps {
  title?: string;
  fields: DynamicField[];
  onChange: (fields: DynamicField[]) => void;
}

export function DynamicFieldsForm({
  title,
  fields,
  onChange,
}: DynamicFieldsFormProps) {
  const [newFieldName, setNewFieldName] = useState("");
  const [newFieldType, setNewFieldType] = useState<"text" | "number" | "date" | "textarea">("text");

  const addField = () => {
    if (!newFieldName.trim()) return;

    const newField: DynamicField = {
      id: Date.now().toString(),
      name: newFieldName.trim(),
      type: newFieldType,
      value: newFieldType === "date" ? null : "",
    };

    onChange([...fields, newField]);
    setNewFieldName("");
    setNewFieldType("text");
  };

  const removeField = (fieldId: string) => {
    onChange(fields.filter(field => field.id !== fieldId));
  };

  const updateFieldValue = (fieldId: string, value: any) => {
    onChange(fields.map(field =>
      field.id === fieldId ? { ...field, value } : field
    ));
  };

  const renderFieldValue = (field: DynamicField) => {
    switch (field.type) {
      case "text":
        return (
          <TextField
            fullWidth
            label={field.name}
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="small"
          />
        );

      case "number":
        return (
          <TextField
            fullWidth
            label={field.name}
            type="number"
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="small"
          />
        );

      case "textarea":
        return (
          <TextField
            fullWidth
            label={field.name}
            multiline
            rows={3}
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="small"
          />
        );

      case "date":
        return (
          <DatePicker
            label={field.name}
            value={field.value ? dayjs(field.value) : null}
            onChange={(date) => updateFieldValue(field.id, date?.toDate() || null)}
            slotProps={{
              textField: {
                fullWidth: true,
                size: "small",
              },
            }}
          />
        );

      case "select":
        return (
          <FormControl fullWidth size="small">
            <InputLabel>{field.name}</InputLabel>
            <Select
              value={field.value || ""}
              onChange={(e) => updateFieldValue(field.id, e.target.value)}
              label={field.name}
            >
              {field.options?.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      {title && (
        <CardHeader
          title={title}
          titleTypographyProps={{
            variant: "h6",
            fontSize: { xs: "1.1rem", sm: "1.25rem" }
          }}
        />
      )}
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        {/* 现有自定义字段 */}
        {fields.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={2}>
              {fields.map((field) => (
                <Grid
                  item
                  xs={12}
                  sm={field.type === "textarea" ? 12 : 6}
                  key={field.id}
                >
                  <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                    <Box sx={{ flex: 1 }}>
                      {renderFieldValue(field)}
                    </Box>
                    <IconButton
                      onClick={() => removeField(field.id)}
                      size="small"
                      color="error"
                      sx={{ mt: 1 }}
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </Grid>
              ))}
            </Grid>
            <Divider sx={{ my: 2 }} />
          </Box>
        )}

        {/* 添加新字段 */}
        <Typography variant="subtitle2" gutterBottom>
          添加自定义字段
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="字段名称"
              value={newFieldName}
              onChange={(e) => setNewFieldName(e.target.value)}
              size="medium"
              placeholder="例如：备注信息"
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="medium">
              <InputLabel>字段类型</InputLabel>
              <Select
                value={newFieldType}
                label="字段类型"
                onChange={(e) => setNewFieldType(e.target.value as any)}
              >
                <MenuItem value="text">文本</MenuItem>
                <MenuItem value="number">数字</MenuItem>
                <MenuItem value="date">日期</MenuItem>
                <MenuItem value="textarea">多行文本</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Button
              variant="contained"
              onClick={addField}
              disabled={!newFieldName.trim()}
              startIcon={<Add />}
              fullWidth
            >
              添加字段
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}

// 将动态字段转换为保存格式
export function convertDynamicFieldsToCustomFields(fields: DynamicField[]): Record<string, any> {
  const result: Record<string, any> = {};
  fields.forEach(field => {
    if (field.value !== null && field.value !== "") {
      result[field.name] = field.value;
    }
  });
  return result;
}
