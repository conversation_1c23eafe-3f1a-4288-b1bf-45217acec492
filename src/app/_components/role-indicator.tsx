"use client";

import { Box, Chip, Tooltip, Typography } from "@mui/material";
import { 
  AdminPanelSettings, 
  SupervisorAccount, 
  AccountCircle,
  Security
} from "@mui/icons-material";
import { usePermissions } from "~/hooks/usePermissions";

interface RoleIndicatorProps {
  showTooltip?: boolean;
  size?: "small" | "medium";
  variant?: "outlined" | "filled";
}

export function RoleIndicator({ 
  showTooltip = true, 
  size = "small",
  variant = "outlined" 
}: RoleIndicatorProps) {
  const permissions = usePermissions();

  // 获取角色信息
  const getRoleInfo = () => {
    if (permissions.isSuperAdmin) {
      return {
        label: "超级管理员",
        color: "error" as const,
        icon: <Security fontSize="small" />,
        description: "系统级管理员，可以管理所有租户和用户"
      };
    }

    if (permissions.currentTenantRole === "ADMIN") {
      return {
        label: "租户管理员",
        color: "warning" as const,
        icon: <SupervisorAccount fontSize="small" />,
        description: "租户管理员，可以管理租户内的用户和数据"
      };
    }

    if (permissions.currentTenantRole === "MEMBER") {
      return {
        label: "租户成员",
        color: "primary" as const,
        icon: <AccountCircle fontSize="small" />,
        description: "普通成员，可以使用基本功能"
      };
    }

    return {
      label: "未知角色",
      color: "default" as const,
      icon: <AccountCircle fontSize="small" />,
      description: "角色信息不明确"
    };
  };

  const roleInfo = getRoleInfo();

  const chipElement = (
    <Chip
      icon={roleInfo.icon}
      label={roleInfo.label}
      color={roleInfo.color}
      variant={variant}
      size={size}
    />
  );

  if (!showTooltip) {
    return chipElement;
  }

  return (
    <Tooltip 
      title={
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {roleInfo.label}
          </Typography>
          <Typography variant="caption">
            {roleInfo.description}
          </Typography>
          {permissions.currentTenantRole && (
            <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
              租户角色: {permissions.currentTenantRole}
            </Typography>
          )}
        </Box>
      }
      arrow
    >
      {chipElement}
    </Tooltip>
  );
}

// 权限摘要组件
export function PermissionSummary() {
  const permissions = usePermissions();

  const getPermissionList = () => {
    const permissionList = [];
    
    if (permissions.canManageTenant) {
      permissionList.push("管理租户");
    }
    if (permissions.canInviteMembers) {
      permissionList.push("邀请成员");
    }
    if (permissions.canManageDocumentTypes) {
      permissionList.push("管理证件类型");
    }
    if (permissions.canManageFields) {
      permissionList.push("管理字段");
    }
    if (permissions.canDeleteDocuments) {
      permissionList.push("删除证件");
    }
    if (permissions.canViewAllTenants) {
      permissionList.push("查看所有租户");
    }

    return permissionList;
  };

  const permissionList = getPermissionList();

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <RoleIndicator />
        <Typography variant="body2" color="text.secondary">
          当前权限
        </Typography>
      </Box>
      
      {permissionList.length > 0 ? (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {permissionList.map((permission) => (
            <Chip
              key={permission}
              label={permission}
              size="small"
              variant="outlined"
              color="primary"
            />
          ))}
        </Box>
      ) : (
        <Typography variant="caption" color="text.secondary">
          基础使用权限
        </Typography>
      )}
    </Box>
  );
}

// 简化的角色徽章
export function RoleBadge() {
  const permissions = usePermissions();

  if (permissions.isSuperAdmin) {
    return (
      <Chip
        icon={<Security fontSize="small" />}
        label="超管"
        color="error"
        size="small"
        variant="filled"
      />
    );
  }

  if (permissions.currentTenantRole === "ADMIN") {
    return (
      <Chip
        icon={<SupervisorAccount fontSize="small" />}
        label="管理员"
        color="warning"
        size="small"
        variant="filled"
      />
    );
  }

  if (permissions.currentTenantRole === "MEMBER") {
    return (
      <Chip
        icon={<AccountCircle fontSize="small" />}
        label="成员"
        color="primary"
        size="small"
        variant="outlined"
      />
    );
  }

  return null;
}
