'use client';

import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Stack,
  Paper,
  Avatar,
  useTheme,
  alpha,
} from '@mui/material';
import {
  NotificationsActive,
  Security,
  Group,
  CalendarMonth,
  CloudSync,
  Telegram,
  CheckCircle,
  TrendingUp,
  Shield,
  Speed,
} from '@mui/icons-material';
import Link from 'next/link';

const features = [
  {
    icon: <Security />,
    title: '智能证件管理',
    description: '统一管理客户信息与证件资料，支持自定义字段和智能分类',
    color: '#2196f3',
  },
  {
    icon: <NotificationsActive />,
    title: '多渠道提醒',
    description: '邮件、Telegram、PWA推送多种提醒方式，确保不错过任何重要日期',
    color: '#ff9800',
  },
  {
    icon: <Group />,
    title: '团队协作',
    description: '支持多用户协作管理，权限分级，适合个人和企业使用',
    color: '#4caf50',
  },
  {
    icon: <CalendarMonth />,
    title: '可视化日历',
    description: '直观的日历视图，快速识别证件到期高峰期，提前做好准备',
    color: '#9c27b0',
  },
  {
    icon: <CloudSync />,
    title: 'PWA应用',
    description: '可安装到桌面，支持离线使用和浏览器推送通知',
    color: '#00bcd4',
  },
  {
    icon: <Telegram />,
    title: 'Telegram集成',
    description: '绑定Telegram Bot，随时随地接收重要证件到期提醒',
    color: '#607d8b',
  },
];

const benefits = [
  {
    icon: <CheckCircle />,
    title: '零遗漏',
    description: '多重提醒机制确保重要证件不会过期',
  },
  {
    icon: <TrendingUp />,
    title: '高效率',
    description: '自动化管理流程，节省90%的人工时间',
  },
  {
    icon: <Shield />,
    title: '安全可靠',
    description: '企业级数据安全保护，信息加密存储',
  },
  {
    icon: <Speed />,
    title: '快速部署',
    description: '5分钟快速上手，无需复杂配置',
  },
];

export function LandingPage() {
  const theme = useTheme();

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 50%, #0d47a1 100%)`,
          color: 'white',
          py: { xs: 10, md: 15 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background decorations */}
        <Box
          sx={{
            position: 'absolute',
            top: -50,
            right: -50,
            width: 200,
            height: 200,
            borderRadius: '50%',
            bgcolor: alpha('#ffffff', 0.05),
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: -100,
            left: -100,
            width: 300,
            height: 300,
            borderRadius: '50%',
            bgcolor: alpha('#ffffff', 0.03),
          }}
        />

        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Stack spacing={4}>
                <Box>
                  <Typography
                    variant="h1"
                    component="h1"
                    sx={{
                      fontSize: { xs: '2.8rem', md: '4rem' },
                      fontWeight: 700,
                      lineHeight: 1.1,
                      mb: 2,
                    }}
                  >
                    智能证件管理
                    <Box component="span" sx={{ color: '#ffd54f' }}>
                      新体验
                    </Box>
                  </Typography>
                  <Typography
                    variant="h5"
                    sx={{
                      opacity: 0.95,
                      fontWeight: 400,
                      lineHeight: 1.6,
                      maxWidth: 500,
                    }}
                  >
                    专业的证件到期提醒与管理工具，让您再也不用担心重要证件过期，
                    支持多种提醒方式，助力个人和企业高效管理。
                  </Typography>
                </Box>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mt: 4 }}>
                  <Button
                    component={Link}
                    href="/auth/signin"
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: 'grey.100',
                        transform: 'translateY(-2px)',
                        boxShadow: 4,
                      },
                      px: 5,
                      py: 2,
                      borderRadius: 3,
                      fontWeight: 600,
                      transition: 'all 0.3s ease',
                    }}
                  >
                    免费开始使用
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': {
                        borderColor: 'white',
                        bgcolor: alpha('#ffffff', 0.1),
                        transform: 'translateY(-2px)',
                      },
                      px: 5,
                      py: 2,
                      borderRadius: 3,
                      fontWeight: 600,
                      transition: 'all 0.3s ease',
                    }}
                  >
                    查看演示
                  </Button>
                </Stack>

                {/* Stats */}
                <Stack direction="row" spacing={4} sx={{ mt: 6 }}>
                  <Box textAlign="center">
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>1000+</Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>活跃用户</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>50K+</Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>证件管理</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>99.9%</Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>提醒准确率</Typography>
                  </Box>
                </Stack>
              </Stack>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                }}
              >
                {/* Main illustration */}
                <Paper
                  elevation={20}
                  sx={{
                    width: { xs: 280, md: 400 },
                    height: { xs: 200, md: 280 },
                    borderRadius: 4,
                    bgcolor: 'white',
                    display: 'flex',
                    flexDirection: 'column',
                    p: 3,
                    position: 'relative',
                    transform: 'rotate(-5deg)',
                  }}
                >
                  {/* Certificate mockup */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <Security />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" color="text.primary">身份证</Typography>
                      <Typography variant="body2" color="text.secondary">张三</Typography>
                    </Box>
                  </Box>

                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ height: 8, bgcolor: 'grey.200', borderRadius: 1 }} />
                    <Box sx={{ height: 8, bgcolor: 'grey.200', borderRadius: 1, width: '80%' }} />
                    <Box sx={{ height: 8, bgcolor: 'grey.200', borderRadius: 1, width: '60%' }} />
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <NotificationsActive sx={{ color: 'warning.main', mr: 1 }} />
                    <Typography variant="body2" color="warning.main" fontWeight={600}>
                      30天后到期
                    </Typography>
                  </Box>
                </Paper>

                {/* Floating notification */}
                <Paper
                  elevation={8}
                  sx={{
                    position: 'absolute',
                    top: { xs: -20, md: -30 },
                    right: { xs: -20, md: -40 },
                    p: 2,
                    borderRadius: 2,
                    bgcolor: 'success.main',
                    color: 'white',
                    minWidth: 120,
                  }}
                >
                  <Typography variant="body2" fontWeight={600}>
                    ✓ 提醒已发送
                  </Typography>
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 10, md: 15 } }}>
        <Stack spacing={8}>
          <Box textAlign="center">
            <Typography
              variant="h2"
              component="h2"
              gutterBottom
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 700,
                mb: 3,
              }}
            >
              为什么选择我们？
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: 700,
                mx: 'auto',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.25rem' },
              }}
            >
              专业的证件管理解决方案，集智能提醒、团队协作、数据安全于一体
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                    },
                    borderRadius: 3,
                  }}
                >
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        bgcolor: feature.color,
                        width: 64,
                        height: 64,
                        mx: 'auto',
                        mb: 3,
                        '& .MuiSvgIcon-root': {
                          fontSize: 32,
                        },
                      }}
                    >
                      {feature.icon}
                    </Avatar>
                    <Typography
                      variant="h5"
                      component="h3"
                      gutterBottom
                      sx={{ fontWeight: 600, mb: 2 }}
                    >
                      {feature.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Stack>
      </Container>

      {/* Benefits Section */}
      <Box sx={{ bgcolor: alpha(theme.palette.primary.main, 0.02), py: { xs: 10, md: 15 } }}>
        <Container maxWidth="lg">
          <Stack spacing={8}>
            <Box textAlign="center">
              <Typography
                variant="h2"
                component="h2"
                gutterBottom
                sx={{
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  fontWeight: 700,
                  mb: 3,
                }}
              >
                核心优势
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  maxWidth: 600,
                  mx: 'auto',
                  lineHeight: 1.6,
                }}
              >
                让证件管理变得简单高效，为您的业务保驾护航
              </Typography>
            </Box>

            <Grid container spacing={4}>
              {benefits.map((benefit, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Box textAlign="center">
                    <Avatar
                      sx={{
                        bgcolor: 'primary.main',
                        width: 80,
                        height: 80,
                        mx: 'auto',
                        mb: 3,
                        '& .MuiSvgIcon-root': {
                          fontSize: 40,
                        },
                      }}
                    >
                      {benefit.icon}
                    </Avatar>
                    <Typography
                      variant="h5"
                      component="h3"
                      gutterBottom
                      sx={{ fontWeight: 600, mb: 2 }}
                    >
                      {benefit.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      {benefit.description}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Stack>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          color: 'white',
          py: { xs: 10, md: 15 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background decorations */}
        <Box
          sx={{
            position: 'absolute',
            top: -100,
            right: -100,
            width: 300,
            height: 300,
            borderRadius: '50%',
            bgcolor: alpha('#ffffff', 0.05),
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: -150,
            left: -150,
            width: 400,
            height: 400,
            borderRadius: '50%',
            bgcolor: alpha('#ffffff', 0.03),
          }}
        />

        <Container maxWidth="md">
          <Stack spacing={5} textAlign="center">
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 700,
                lineHeight: 1.2,
              }}
            >
              开始您的智能证件管理之旅
            </Typography>
            <Typography
              variant="h5"
              sx={{
                opacity: 0.95,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
                fontWeight: 400,
              }}
            >
              立即注册，体验专业的证件管理服务，让重要证件永不过期
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={3}
              justifyContent="center"
              sx={{ mt: 5 }}
            >
              <Button
                component={Link}
                href="/auth/signin"
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'grey.100',
                    transform: 'translateY(-2px)',
                    boxShadow: 6,
                  },
                  px: 6,
                  py: 2.5,
                  borderRadius: 3,
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  transition: 'all 0.3s ease',
                }}
              >
                免费开始使用
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: alpha('#ffffff', 0.1),
                    transform: 'translateY(-2px)',
                  },
                  px: 6,
                  py: 2.5,
                  borderRadius: 3,
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  transition: 'all 0.3s ease',
                }}
              >
                联系我们
              </Button>
            </Stack>

            {/* Trust indicators */}
            <Box sx={{ mt: 6, opacity: 0.8 }}>
              <Typography variant="body2" sx={{ mb: 2 }}>
                已有 1000+ 用户信赖我们的服务
              </Typography>
              <Stack direction="row" spacing={4} justifyContent="center" alignItems="center">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CheckCircle sx={{ fontSize: 20 }} />
                  <Typography variant="body2">免费试用</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Shield sx={{ fontSize: 20 }} />
                  <Typography variant="body2">数据安全</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Speed sx={{ fontSize: 20 }} />
                  <Typography variant="body2">快速上手</Typography>
                </Box>
              </Stack>
            </Box>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
}