'use client';

import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Stack,
  Chip,
} from '@mui/material';
import {
  NotificationsActive,
  Security,
  Group,
  CalendarMonth,
  CloudSync,
  Telegram,
} from '@mui/icons-material';
import Link from 'next/link';

const features = [
  {
    icon: <Security sx={{ fontSize: 40 }} />,
    title: '证件管理',
    description: '统一管理客户信息与证件资料，支持自定义字段和标签分类',
  },
  {
    icon: <NotificationsActive sx={{ fontSize: 40 }} />,
    title: '到期提醒',
    description: '多节点提醒设置，支持PWA推送和Telegram消息通知',
  },
  {
    icon: <Group sx={{ fontSize: 40 }} />,
    title: '多租户架构',
    description: '支持个人和企业用户，企业可添加多成员协作管理',
  },
  {
    icon: <CalendarMonth sx={{ fontSize: 40 }} />,
    title: '日历视图',
    description: '以日历形式直观查看所有证件到期时间，快速识别高峰期',
  },
  {
    icon: <CloudSync sx={{ fontSize: 40 }} />,
    title: 'PWA支持',
    description: '可安装到桌面，支持离线使用和浏览器推送通知',
  },
  {
    icon: <Telegram sx={{ fontSize: 40 }} />,
    title: 'Telegram集成',
    description: '绑定Telegram Bot，随时随地接收重要证件到期提醒',
  },
];

export function LandingPage() {
  return (
    <Box sx={{ minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Stack spacing={3}>
                <Typography variant="h1" component="h1" sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}>
                  证件到期提醒
                  <br />
                  管理系统
                </Typography>
                <Typography variant="h5" sx={{ opacity: 0.9, fontWeight: 400 }}>
                  专业的证件到期提醒与管理工具，支持多租户架构，
                  帮助个人及企业有序管理证件资料，避免重要证件过期。
                </Typography>
                <Stack direction="row" spacing={2} sx={{ mt: 4 }}>
                  <Button
                    component={Link}
                    href="/auth/signin"
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': { bgcolor: 'grey.100' },
                      px: 4,
                      py: 1.5,
                    }}
                  >
                    立即开始
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' },
                      px: 4,
                      py: 1.5,
                    }}
                  >
                    了解更多
                  </Button>
                </Stack>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: { xs: 200, md: 400 },
                }}
              >
                <Box
                  sx={{
                    width: { xs: 200, md: 300 },
                    height: { xs: 200, md: 300 },
                    borderRadius: '50%',
                    bgcolor: 'rgba(255,255,255,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <NotificationsActive sx={{ fontSize: { xs: 80, md: 120 }, color: 'white' }} />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
        <Stack spacing={6}>
          <Box textAlign="center">
            <Typography variant="h2" component="h2" gutterBottom>
              核心功能
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              全面的证件管理解决方案，让您再也不用担心重要证件过期
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Box sx={{ color: 'primary.main', mb: 2 }}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Stack>
      </Container>

      {/* Pricing Section */}
      <Box sx={{ bgcolor: 'grey.50', py: { xs: 8, md: 12 } }}>
        <Container maxWidth="lg">
          <Stack spacing={6}>
            <Box textAlign="center">
              <Typography variant="h2" component="h2" gutterBottom>
                选择适合的套餐
              </Typography>
              <Typography variant="h6" color="text.secondary">
                灵活的订阅方案，满足不同规模的需求
              </Typography>
            </Box>

            <Grid container spacing={4} justifyContent="center">
              <Grid item xs={12} sm={6} md={4}>
                <Card sx={{ height: '100%', position: 'relative' }}>
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="h5" component="h3" gutterBottom>
                      免费试用
                    </Typography>
                    <Typography variant="h3" component="div" sx={{ my: 2 }}>
                      免费
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      30天试用期
                    </Typography>
                    <Stack spacing={2} sx={{ mt: 3, mb: 4 }}>
                      <Typography variant="body2">• 50个证件记录</Typography>
                      <Typography variant="body2">• 5个自定义字段</Typography>
                      <Typography variant="body2">• Telegram通知</Typography>
                      <Typography variant="body2">• PWA推送支持</Typography>
                      <Typography variant="body2">• 社区支持</Typography>
                    </Stack>
                    <Button variant="outlined" fullWidth size="large">
                      开始试用
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card sx={{ height: '100%', position: 'relative', border: 2, borderColor: 'primary.main' }}>
                  <Chip
                    label="推荐"
                    color="primary"
                    sx={{ position: 'absolute', top: 16, right: 16 }}
                  />
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="h5" component="h3" gutterBottom>
                      个人商业版
                    </Typography>
                    <Typography variant="h3" component="div" sx={{ my: 2 }}>
                      $118
                      <Typography variant="body1" component="span" color="text.secondary">
                        /年
                      </Typography>
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      适合个人和小型工作室
                    </Typography>
                    <Stack spacing={2} sx={{ mt: 3, mb: 4 }}>
                      <Typography variant="body2">• 1000个证件记录</Typography>
                      <Typography variant="body2">• 50个自定义字段</Typography>
                      <Typography variant="body2">• Telegram通知</Typography>
                      <Typography variant="body2">• PWA支持</Typography>
                      <Typography variant="body2">• 数据导出</Typography>
                      <Typography variant="body2">• 邮件支持</Typography>
                    </Stack>
                    <Button variant="contained" fullWidth size="large">
                      立即订阅
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card sx={{ height: '100%', position: 'relative' }}>
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="h5" component="h3" gutterBottom>
                      企业商业版
                    </Typography>
                    <Typography variant="h3" component="div" sx={{ my: 2 }}>
                      $288
                      <Typography variant="body1" component="span" color="text.secondary">
                        /年
                      </Typography>
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      适合企业和团队使用
                    </Typography>
                    <Stack spacing={2} sx={{ mt: 3, mb: 4 }}>
                      <Typography variant="body2">• 无限证件记录</Typography>
                      <Typography variant="body2">• 无限用户</Typography>
                      <Typography variant="body2">• 无限自定义字段</Typography>
                      <Typography variant="body2">• 多种通知方式</Typography>
                      <Typography variant="body2">• API接口</Typography>
                      <Typography variant="body2">• 自定义品牌</Typography>
                      <Typography variant="body2">• 高级报表</Typography>
                      <Typography variant="body2">• 优先技术支持</Typography>
                    </Stack>
                    <Button variant="outlined" fullWidth size="large">
                      联系销售
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Stack>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: { xs: 6, md: 8 } }}>
        <Container maxWidth="md">
          <Stack spacing={3} textAlign="center">
            <Typography variant="h3" component="h2">
              准备开始了吗？
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              立即注册，开始管理您的证件，再也不用担心过期问题
            </Typography>
            <Box>
              <Button
                component={Link}
                href="/auth/signin"
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': { bgcolor: 'grey.100' },
                  px: 6,
                  py: 2,
                }}
              >
                免费开始试用
              </Button>
            </Box>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
}