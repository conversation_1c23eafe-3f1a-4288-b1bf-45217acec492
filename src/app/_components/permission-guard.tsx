"use client";

import { ReactNode } from "react";
import { Box, Alert, Typography } from "@mui/material";
import { Lock } from "@mui/icons-material";
import { usePermissions, UserPermissions, hasPermission, hasRole, hasAnyPermission } from "~/hooks/usePermissions";

interface PermissionGuardProps {
  children: ReactNode;
  
  // 权限检查方式（三选一）
  requiredPermission?: keyof UserPermissions;
  requiredRoles?: string[];
  requiredPermissions?: (keyof UserPermissions)[];
  
  // 检查模式
  requireAll?: boolean; // 对于 requiredPermissions，是否需要全部权限（默认为任一权限）
  
  // 自定义权限检查函数
  customCheck?: (permissions: UserPermissions) => boolean;
  
  // 无权限时的显示
  fallback?: ReactNode;
  showFallback?: boolean; // 是否显示无权限提示（默认为隐藏）
  fallbackMessage?: string;
  
  // 样式
  disabled?: boolean; // 是否禁用而不是隐藏
}

export function PermissionGuard({
  children,
  requiredPermission,
  requiredRoles,
  requiredPermissions,
  requireAll = false,
  customCheck,
  fallback,
  showFallback = false,
  fallbackMessage = "您没有权限访问此功能",
  disabled = false,
}: PermissionGuardProps) {
  const permissions = usePermissions();

  // 执行权限检查
  const hasAccess = checkPermissions(
    permissions,
    requiredPermission,
    requiredRoles,
    requiredPermissions,
    requireAll,
    customCheck
  );

  // 如果有权限，直接渲染子组件
  if (hasAccess) {
    return <>{children}</>;
  }

  // 如果设置为禁用模式，渲染禁用状态的子组件
  if (disabled) {
    return (
      <Box sx={{ opacity: 0.5, pointerEvents: "none" }}>
        {children}
      </Box>
    );
  }

  // 如果需要显示无权限提示
  if (showFallback) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <Alert 
        severity="warning" 
        icon={<Lock />}
        sx={{ my: 2 }}
      >
        <Typography variant="body2">
          {fallbackMessage}
        </Typography>
      </Alert>
    );
  }

  // 默认情况下不渲染任何内容
  return null;
}

// 权限检查逻辑
function checkPermissions(
  permissions: UserPermissions,
  requiredPermission?: keyof UserPermissions,
  requiredRoles?: string[],
  requiredPermissions?: (keyof UserPermissions)[],
  requireAll?: boolean,
  customCheck?: (permissions: UserPermissions) => boolean
): boolean {
  // 自定义检查优先级最高
  if (customCheck) {
    return customCheck(permissions);
  }

  // 单个权限检查
  if (requiredPermission) {
    return hasPermission(permissions, requiredPermission);
  }

  // 角色检查
  if (requiredRoles && requiredRoles.length > 0) {
    return hasRole(permissions, requiredRoles);
  }

  // 多个权限检查
  if (requiredPermissions && requiredPermissions.length > 0) {
    if (requireAll) {
      return requiredPermissions.every(permission => 
        hasPermission(permissions, permission)
      );
    } else {
      return hasAnyPermission(permissions, requiredPermissions);
    }
  }

  // 如果没有指定任何权限要求，默认允许访问
  return true;
}

// 便捷的权限检查组件
export function AdminOnly({ children, fallback, showFallback }: {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      requiredRoles={["SUPER_ADMIN", "ADMIN"]}
      fallback={fallback}
      showFallback={showFallback}
      fallbackMessage="此功能仅限管理员使用"
    >
      {children}
    </PermissionGuard>
  );
}

export function SuperAdminOnly({ children, fallback, showFallback }: {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      requiredPermission="isSuperAdmin"
      fallback={fallback}
      showFallback={showFallback}
      fallbackMessage="此功能仅限超级管理员使用"
    >
      {children}
    </PermissionGuard>
  );
}

export function TenantAdminOnly({ children, fallback, showFallback }: {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      requiredPermission="canManageTenant"
      fallback={fallback}
      showFallback={showFallback}
      fallbackMessage="此功能仅限租户管理员使用"
    >
      {children}
    </PermissionGuard>
  );
}

// Hook 形式的权限检查
export function usePermissionCheck() {
  const permissions = usePermissions();
  
  return {
    permissions,
    hasPermission: (permission: keyof UserPermissions) => 
      hasPermission(permissions, permission),
    hasRole: (roles: string[]) => 
      hasRole(permissions, roles),
    hasAnyPermission: (permissionList: (keyof UserPermissions)[]) => 
      hasAnyPermission(permissions, permissionList),
    isAdmin: permissions.canManageTenant,
    isSuperAdmin: permissions.isSuperAdmin,
    canManage: (feature: 'tenant' | 'documents' | 'types' | 'fields') => {
      switch (feature) {
        case 'tenant':
          return permissions.canManageTenant;
        case 'documents':
          return permissions.canEditDocuments;
        case 'types':
          return permissions.canManageDocumentTypes;
        case 'fields':
          return permissions.canManageFields;
        default:
          return false;
      }
    }
  };
}
