"use client";

import { Box, Typography, useTheme } from "@mui/material";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Area,
  AreaChart
} from "recharts";
import { api } from "~/trpc/react";

interface ExpirationTrendChartProps {
  tenantId: string | null;
}

export function ExpirationTrendChart({ tenantId }: ExpirationTrendChartProps) {
  const theme = useTheme();

  // 检查 tenantId 是否有效
  const isValidTenantId = Boolean(tenantId) && tenantId !== "" && tenantId !== "undefined" && tenantId !== null;

  // 获取到期趋势数据 - 只有在 tenantId 有效时才查询
  const { data: trendData, isLoading } = api.document.getExpirationTrend.useQuery(
    { tenantId: tenantId as string, months: 12 },
    {
      enabled: isValidTenantId,
      retry: false, // 不重试失败的查询
    }
  );

  // 如果 tenantId 无效，显示提示
  if (!isValidTenantId) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography color="text.secondary">请选择商户查看数据</Typography>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography color="text.secondary">加载中...</Typography>
      </Box>
    );
  }

  if (!trendData || trendData.length === 0) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography color="text.secondary">暂无数据</Typography>
      </Box>
    );
  }

  // 转换数据格式
  const chartData = trendData.map((item) => ({
    month: item.month,
    expiring: item.expiringCount,
    expired: item.expiredCount,
    total: item.totalCount,
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 1.5,
            boxShadow: 2,
          }}
        >
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography 
              key={index}
              variant="body2" 
              sx={{ color: entry.color }}
            >
              {entry.name}: {entry.value}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ height: 300 }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id="colorExpiring" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={theme.palette.warning.main} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={theme.palette.warning.main} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="colorExpired" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={theme.palette.error.main} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={theme.palette.error.main} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <XAxis 
            dataKey="month" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
          />
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="expiring"
            stackId="1"
            stroke={theme.palette.warning.main}
            fillOpacity={1}
            fill="url(#colorExpiring)"
            name="即将到期"
          />
          <Area
            type="monotone"
            dataKey="expired"
            stackId="1"
            stroke={theme.palette.error.main}
            fillOpacity={1}
            fill="url(#colorExpired)"
            name="已过期"
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  );
}
