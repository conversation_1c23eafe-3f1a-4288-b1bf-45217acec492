"use client";

import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Collapse,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import {
  Menu as MenuIcon,
  Dashboard,
  Description,
  CalendarToday,
  Notifications,
  Settings,
  Business,
  AccountCircle,
  Logout,
  FirstPage,
  LastPage,
  ExpandLess,
  ExpandMore,
  LightMode,
  DarkMode,
  ChevronLeft,
  ChevronRight,
  Assessment,
  ShoppingCart,
  NotificationsActive,
} from "@mui/icons-material";
import { api } from "~/trpc/react";
import { useTheme as useCustomTheme } from "~/contexts/theme-context";
import { usePermissions } from "~/hooks/usePermissions";
import { SidebarSkeleton, NavbarSkeleton } from "~/components/ui/Skeleton";

const drawerWidth = 240;
const miniDrawerWidth = 64;

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: NavigationItem[];
  badge?: number;
}

export default function SidebarNavigation({ children }: { children: React.ReactNode }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const pathname = usePathname();
  const router = useRouter();
  const { data: session } = useSession();
  const permissions = usePermissions();
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [open, setOpen] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const { mode, toggleTheme } = useCustomTheme();

  // 根据当前路径获取页面标题
  const getPageTitle = () => {
    const findNavItem = (items: NavigationItem[], path: string): NavigationItem | null => {
      for (const item of items) {
        if (item.path === path) return item;
        if (item.children) {
          const found = findNavItem(item.children, path);
          if (found) return found;
        }
      }
      return null;
    };

    const currentItem = findNavItem(navigationItems, pathname);
    return currentItem?.label || "证件管理系统";
  };

  // 获取未读通知数量
  const { data: notificationsData } = api.notification.getAll.useQuery(
    { tenantId: session?.user?.currentTenantId || "", page: 1, limit: 100 },
    {
      enabled: !!session?.user?.currentTenantId && session?.user?.currentTenantId !== "",
      retry: false,
    }
  );
  const unreadCount = notificationsData?.notifications?.filter(n => !n.isRead).length || 0;



  // 根据用户权限动态生成导航菜单
  const getNavigationItems = (): NavigationItem[] => {
    const items: NavigationItem[] = [];

    // 仪表板 - 所有角色都可以访问
    items.push({
      id: "dashboard",
      label: "仪表板",
      icon: <Dashboard />,
      path: "/dashboard",
    });

    // 证件管理 - 租户用户可以访问
    if (permissions.canViewDocuments) {
      items.push({
        id: "documents",
        label: "证件管理",
        icon: <Description />,
        path: "/documents",
        children: [
          {
            id: "documents-list",
            label: "证件列表",
            path: "/documents",
          },
          {
            id: "documents-new",
            label: "添加证件",
            path: "/documents/new",
          },
        ],
      });
    }

    // 日历视图 - 租户用户可以访问
    if (permissions.canViewDocuments) {
      items.push({
        id: "calendar",
        label: "日历视图",
        icon: <CalendarToday />,
        path: "/calendar",
      });
    }

    // 通知中心 - 只有商户管理员和超级管理员可以访问
    if (permissions.isSuperAdmin || permissions.canManageTenant) {
      items.push({
        id: "notifications",
        label: "通知中心",
        icon: <Notifications />,
        path: "/notifications",
        badge: unreadCount,
      });
    }

    // 商户管理 - 根据用户角色显示不同页面
    if (permissions.isSuperAdmin) {
      // 超级管理员：显示所有管理页面
      items.push({
        id: "admin-merchants",
        label: "商户管理",
        icon: <Business />,
        path: "/admin/merchants-enhanced",
      });

      items.push({
        id: "admin-plans",
        label: "套餐管理",
        icon: <Assessment />,
        path: "/admin/plans",
      });

      items.push({
        id: "admin-orders",
        label: "订单管理",
        icon: <ShoppingCart />,
        path: "/admin/orders",
      });

      items.push({
        id: "admin-notifications",
        label: "通知配置",
        icon: <NotificationsActive />,
        path: "/admin/notifications",
      });
    } else if (permissions.canManageTenant) {
      // 商户管理员/成员：显示自己的商户资料页面
      items.push({
        id: "merchant",
        label: "商户资料",
        icon: <Business />,
        path: "/merchant",
      });
    }

    // 系统设置 - 所有角色都可以访问（但内容不同）
    items.push({
      id: "settings",
      label: permissions.isSuperAdmin ? "系统设置" : "个人设置",
      icon: <Settings />,
      path: "/settings",
    });

    return items;
  };

  const navigationItems = getNavigationItems();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleThemeToggle = () => {
    toggleTheme();
  };

  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleExpandClick = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    if (path === "/dashboard") {
      return pathname === "/dashboard";
    }
    return pathname.startsWith(path);
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = item.path ? isActive(item.path) : false;

    if (hasChildren) {
      return (
        <Box key={item.id}>
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => handleExpandClick(item.id)}
              sx={{
                pl: 2 + level * 2,
                minHeight: 48,
                backgroundColor: active ? "action.selected" : "inherit",
              }}
            >
              <ListItemIcon sx={{ minWidth: open ? 40 : 0, mr: open ? 3 : 'auto', justifyContent: 'center' }}>
                {item.icon}
              </ListItemIcon>
              {open && (
                <>
                  <ListItemText primary={item.label} />
                  {item.badge && item.badge > 0 && (
                    <Badge badgeContent={item.badge} color="error" sx={{ mr: 1 }} />
                  )}
                  {isExpanded ? <ExpandLess /> : <ExpandMore />}
                </>
              )}
            </ListItemButton>
          </ListItem>
          {open && (
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.children?.map(child => renderNavigationItem(child, level + 1))}
              </List>
            </Collapse>
          )}
        </Box>
      );
    }

    return (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          onClick={() => item.path && handleNavigation(item.path)}
          sx={{
            pl: 2 + level * 2,
            minHeight: 48,
            backgroundColor: active ? "action.selected" : "inherit",
            "&:hover": {
              backgroundColor: "action.hover",
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: open ? 40 : 0, mr: open ? 3 : 'auto', justifyContent: 'center' }}>
            {item.badge && item.badge > 0 ? (
              <Badge badgeContent={item.badge} color="error">
                {item.icon}
              </Badge>
            ) : (
              item.icon
            )}
          </ListItemIcon>
          {open && <ListItemText primary={item.label} />}
        </ListItemButton>
      </ListItem>
    );
  };

  const drawer = (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* 侧边栏头部 */}
      <Box
        sx={{
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: open ? "space-between" : "center",
          px: 2,
          height: 64, // 固定高度，与顶部导航栏一致
          backgroundColor: "primary.main",
          color: "primary.contrastText",
        }}
      >
        {open && (
          <Typography variant="h6" noWrap component="div" sx={{ color: "inherit" }}>
            证件管理系统
          </Typography>
        )}
        {!isMobile && (
          <IconButton
            onClick={open ? handleDrawerClose : handleDrawerOpen}
            sx={{
              color: "primary.contrastText",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
            }}
          >
            {open ? <ChevronLeft /> : <ChevronRight />}
          </IconButton>
        )}
      </Box>
      
      <Divider />
      
      {/* 导航菜单 */}
      <Box sx={{ flexGrow: 1, overflow: "auto" }}>
        <List>
          {navigationItems.map(item => renderNavigationItem(item))}
        </List>
      </Box>
      
      <Divider />
      
      {/* 用户信息 */}
      {session && (
        <Box sx={{ p: 2 }}>
          <ListItem disablePadding>
            <ListItemButton onClick={handleProfileMenuOpen}>
              <ListItemIcon>
                <Avatar sx={{ width: 32, height: 32 }}>
                  <AccountCircle />
                </Avatar>
              </ListItemIcon>
              {open && (
                <ListItemText
                  primary={session.user.name}
                  secondary={session.user.email}
                  primaryTypographyProps={{ variant: "body2" }}
                  secondaryTypographyProps={{ variant: "caption" }}
                />
              )}
            </ListItemButton>
          </ListItem>
        </Box>
      )}
    </Box>
  );

  return (
    <Box sx={{ display: "flex" }}>
      {/* 顶部应用栏 - 仅移动端显示 */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${open ? drawerWidth : miniDrawerWidth}px)` },
          ml: { md: `${open ? drawerWidth : miniDrawerWidth}px` },
          display: { md: "none" },
          zIndex: theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 56, sm: 64 } }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: "none" } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {getPageTitle()}
          </Typography>
          {/* 移动端通知图标 */}
          {unreadCount > 0 && (
            <IconButton
              color="inherit"
              onClick={() => router.push("/notifications")}
              sx={{ display: { md: "none" } }}
            >
              <Badge badgeContent={unreadCount} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          )}
        </Toolbar>
      </AppBar>

      {/* 侧边栏 */}
      <Box
        component="nav"
        sx={{ 
          width: { md: open ? drawerWidth : miniDrawerWidth },
          flexShrink: { md: 0 } 
        }}
      >
        {/* 移动端抽屉 */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // 保持挂载以提高移动端性能
          }}
          sx={{
            display: { xs: "block", md: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              backgroundImage: "none", // 移除默认背景图像
            },
            "& .MuiBackdrop-root": {
              backgroundColor: "rgba(0, 0, 0, 0.3)", // 半透明背景
            },
          }}
        >
          {drawer}
        </Drawer>
        
        {/* 桌面端抽屉 - Mini Variant */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", md: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: open ? drawerWidth : miniDrawerWidth,
              transition: theme.transitions.create("width", {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
              overflowX: "hidden",
            },
          }}
          open={open}
        >
          {drawer}
        </Drawer>
      </Box>

      {/* 主内容区域 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: {
            md: `calc(100% - ${open ? drawerWidth : miniDrawerWidth}px)`
          },
          minHeight: "100vh",
          backgroundColor: "background.default",
          // 移动端优化
          padding: { xs: 0, md: 0 },
          overflow: "hidden", // 防止水平滚动
        }}
      >
        {/* 桌面端顶部导航栏 */}
        <AppBar
          position="static"
          elevation={0}
          sx={{
            display: { xs: "none", md: "block" },
            backgroundColor: "transparent",
            borderBottom: "1px solid",
            borderColor: "divider",
            zIndex: 1200, // 确保z-index低于侧边栏收缩按钮
          }}
        >
          <Toolbar sx={{ justifyContent: "space-between", height: 64 }}>
            {/* 页面标题 */}
            <Typography variant="h5" component="h1" sx={{
              fontWeight: 600,
              color: "text.primary",
              fontSize: { xs: "1.25rem", sm: "1.5rem" }
            }}>
              {getPageTitle()}
            </Typography>

            {/* 右侧按钮组 */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {/* 通知按钮 */}
              <IconButton
                onClick={() => router.push("/notifications")}
                sx={{
                  color: "text.primary",
                  "&:hover": {
                    backgroundColor: "action.hover",
                  },
                }}
              >
                <Badge badgeContent={unreadCount} color="error">
                  <Notifications />
                </Badge>
              </IconButton>

              {/* 主题切换按钮 */}
              <IconButton
                onClick={handleThemeToggle}
                sx={{
                  color: "text.primary",
                  "&:hover": {
                    backgroundColor: "action.hover",
                  },
                }}
              >
                {mode === 'dark' ? <LightMode /> : <DarkMode />}
              </IconButton>
            </Box>
          </Toolbar>
        </AppBar>

        {/* 移动端顶部间距 */}
        <Toolbar sx={{
          display: { md: "none" },
          minHeight: { xs: 56, sm: 64 },
        }} />

        {/* 内容容器 */}
        <Box sx={{
          height: {
            xs: "calc(100vh - 56px)",
            sm: "calc(100vh - 64px)",
            md: "calc(100vh - 64px)" // 减去桌面端顶部导航栏高度
          },
          overflow: "auto",
          // 移动端内边距
          px: { xs: 1, sm: 2, md: 0 },
        }}>
          {children}
        </Box>
      </Box>

      {/* 用户菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
      >
        <MenuItem onClick={() => router.push("/settings")}>
          <Settings sx={{ mr: 1 }} />
          设置
        </MenuItem>
        <MenuItem onClick={() => signOut()}>
          <Logout sx={{ mr: 1 }} />
          退出登录
        </MenuItem>
      </Menu>
    </Box>
  );
}
