'use client';

import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Chip,
  Stack,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { Add, Close } from '@mui/icons-material';
import React, { useState, useEffect } from 'react';
import type { FormEvent, ChangeEvent, KeyboardEvent } from 'react';
import { api } from '~/trpc/react';
import { z } from 'zod';

interface DocumentFormProps {
  open: boolean;
  onClose: () => void;
  tenantId: string;
  documentId?: string; // 如果提供了 documentId，则为编辑模式
  onSuccess?: () => void;
}

const DOCUMENT_TYPES = [
  { value: 'ID_CARD', label: '身份证' },
  { value: 'PASSPORT', label: '护照' },
  { value: 'DRIVER_LICENSE', label: '驾驶证' },
  { value: 'BUSINESS_LICENSE', label: '营业执照' },
  { value: 'WORK_PERMIT', label: '工作许可证' },
  { value: 'VISA', label: '签证' },
  { value: 'OTHER', label: '其他' },
];

interface FormData {
  customerName: string;
  phone: string;
  certType: string;
  certNumber: string;
  validUntil: Date | null;
  issueBy: string;
  customFields: Record<string, any>;
  tags: string[];
}

const initialFormData: FormData = {
  customerName: '',
  phone: '',
  certType: '',
  certNumber: '',
  validUntil: null,
  issueBy: '',
  customFields: {},
  tags: [],
};

export function DocumentForm({ open, onClose, tenantId, documentId, onSuccess }: DocumentFormProps) {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isEditMode = Boolean(documentId);

  // 获取证件详情（编辑模式）
  const { data: documentData, isLoading: isLoadingDocument } = api.document.getById.useQuery(
    { id: documentId!, tenantId },
    { enabled: isEditMode && open }
  );

  // 创建证件
  const createDocumentMutation = api.document.create.useMutation({
    onSuccess: () => {
      onSuccess?.();
      handleClose();
    },
    onError: (error) => {
      console.error('创建证件失败:', error);
    },
  });

  // 更新证件
  const updateDocumentMutation = api.document.update.useMutation({
    onSuccess: () => {
      onSuccess?.();
      handleClose();
    },
    onError: (error) => {
      console.error('更新证件失败:', error);
    },
  });

  // 当打开对话框时重置表单
  useEffect(() => {
    if (open) {
      if (isEditMode && documentData) {
        setFormData({
          customerName: documentData.customerName,
          phone: documentData.phone || '',
          certType: documentData.certType,
          certNumber: documentData.certNumber,
          validUntil: new Date(documentData.validUntil),
          issueBy: documentData.issueBy || '',
          customFields: documentData.customFields || {},
          tags: documentData.tags || [],
        });
      } else if (!isEditMode) {
        setFormData(initialFormData);
      }
      setErrors({});
      setTagInput('');
    }
  }, [open, isEditMode, documentData]);

  const handleClose = () => {
    setFormData(initialFormData);
    setErrors({});
    setTagInput('');
    onClose();
  };

  const handleInputChange = (field: keyof FormData) => (event: ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (field: keyof FormData) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleDateChange = (field: 'validUntil') => (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: date,
    }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleAddTag = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, trimmedTag],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleTagInputKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleAddTag();
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = '客户姓名不能为空';
    }

    if (!formData.certType) {
      newErrors.certType = '请选择证件类型';
    }

    if (!formData.certNumber.trim()) {
      newErrors.certNumber = '证件号码不能为空';
    }

    if (!formData.validUntil) {
      newErrors.validUntil = '请选择到期日期';
    }

    if (formData.email && !z.string().email().safeParse(formData.email).success) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    const submitData = {
      tenantId,
      customerName: formData.customerName.trim(),
      phone: formData.phone.trim() || undefined,
      certType: formData.certType as any,
      certNumber: formData.certNumber.trim(),
      validUntil: formData.validUntil!,
      issueBy: formData.issueBy.trim() || undefined,
      customFields: Object.keys(formData.customFields).length > 0 ? formData.customFields : undefined,
      tags: formData.tags.length > 0 ? formData.tags : undefined,
    };

    if (isEditMode) {
      updateDocumentMutation.mutate({
        id: documentId!,
        ...submitData,
      });
    } else {
      createDocumentMutation.mutate(submitData);
    }
  };

  const isLoading = createDocumentMutation.isPending || updateDocumentMutation.isPending;
  const error = createDocumentMutation.error || updateDocumentMutation.error;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEditMode ? '编辑证件' : '添加证件'}
      </DialogTitle>
      
      <DialogContent>
        {isLoadingDocument ? (
          <Box display="flex" justifyContent="center" py={4}>
            正在加载证件信息...
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error.message}
              </Alert>
            )}
            
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {/* 客户信息 */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="客户姓名"
                  value={formData.customerName}
                  onChange={handleInputChange('customerName')}
                  error={Boolean(errors.customerName)}
                  helperText={errors.customerName}
                  required
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="联系电话"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  error={Boolean(errors.phone)}
                  helperText={errors.phone}
                />
              </Grid>
              


              {/* 证件信息 */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(errors.certType)}>
                  <InputLabel required>证件类型</InputLabel>
                  <Select
                    value={formData.certType}
                    label="证件类型"
                    onChange={handleSelectChange('certType')}
                  >
                    {DOCUMENT_TYPES.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.certType && (
                    <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                      {errors.certType}
                    </Box>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="证件号码"
                  value={formData.certNumber}
                  onChange={handleInputChange('certNumber')}
                  error={Boolean(errors.certNumber)}
                  helperText={errors.certNumber}
                  required
                />
              </Grid>

              {/* 日期信息 */}
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
                  <DatePicker
                    label="到期日期"
                    value={formData.validUntil}
                    onChange={handleDateChange('validUntil')}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        required: true,
                        error: Boolean(errors.validUntil),
                        helperText: errors.validUntil,
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              {/* 签发机构 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="签发机构"
                  value={formData.issueBy}
                  onChange={handleInputChange('issueBy')}
                  error={Boolean(errors.issueBy)}
                  helperText={errors.issueBy}
                />
              </Grid>

              {/* 标签 */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="添加标签"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleTagInputKeyPress}
                  placeholder="输入标签后按回车添加"
                  InputProps={{
                    endAdornment: (
                      <IconButton onClick={handleAddTag} disabled={!tagInput.trim()}>
                        <Add />
                      </IconButton>
                    ),
                  }}
                />
                {formData.tags.length > 0 && (
                  <Stack direction="row" spacing={1} sx={{ mt: 1 }} flexWrap="wrap">
                    {formData.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        onDelete={() => handleRemoveTag(tag)}
                        deleteIcon={<Close />}
                        size="small"
                      />
                    ))}
                  </Stack>
                )}
              </Grid>


            </Grid>
          </>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} disabled={isLoading}>
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={isLoading || isLoadingDocument}
        >
          {isLoading ? '保存中...' : (isEditMode ? '更新' : '添加')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}