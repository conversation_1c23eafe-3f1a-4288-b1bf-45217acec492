"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Alert,
  Divider,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Save,
  Cancel,
  Close,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

interface CustomField {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "select" | "textarea";
  required: boolean;
  options?: string[]; // For select type
  defaultValue?: string;
  placeholder?: string;
}

interface FieldManagementDialogProps {
  open: boolean;
  onClose: () => void;
  tenantId: string;
  onSave?: (customerFields: CustomField[], documentFields: CustomField[]) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`field-tabpanel-${index}`}
      aria-labelledby={`field-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export function FieldManagementDialog({
  open,
  onClose,
  tenantId,
  onSave,
}: FieldManagementDialogProps) {
  const [tabValue, setTabValue] = useState(0);
  const [customerFields, setCustomerFields] = useState<CustomField[]>([]);
  const [documentFields, setDocumentFields] = useState<CustomField[]>([]);
  const [editingField, setEditingField] = useState<CustomField | null>(null);
  const [isAddingField, setIsAddingField] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [newField, setNewField] = useState<Partial<CustomField>>({
    name: "",
    type: "text",
    required: false,
    options: [],
    defaultValue: "",
    placeholder: "",
  });

  // 获取现有字段配置
  const { data: allFieldConfigs = [], refetch: refetchFields } = api.customField.getAll.useQuery(
    { tenantId: tenantId! },
    {
      enabled: !!tenantId && open,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      refetchOnWindowFocus: false, // 避免不必要的重新获取
    }
  );

  // API mutations
  const createFieldMutation = api.customField.create.useMutation();
  const updateFieldMutation = api.customField.update.useMutation();
  const deleteFieldMutation = api.customField.delete.useMutation();



  // 加载字段配置
  useEffect(() => {
    if (!open || !allFieldConfigs.length) {
      return;
    }

    // 在 useEffect 内部重新计算，避免依赖外部计算的数组
    const customerFieldsFromAPI: CustomField[] = [];
    const documentFieldsFromAPI: CustomField[] = [];

    allFieldConfigs.forEach((config) => {
      const customField: CustomField = {
        id: config.id,
        name: config.name,
        type: config.type as "text" | "number" | "date" | "textarea" | "select",
        required: config.required,
        options: config.options ? (config.options as string[]) : [],
        defaultValue: config.defaultValue || "",
        placeholder: config.placeholder || "",
      };

      if (config.category === "customer") {
        customerFieldsFromAPI.push(customField);
      } else if (config.category === "document") {
        documentFieldsFromAPI.push(customField);
      }
    });

    // 使用函数式更新，只在内容真正变化时更新
    setCustomerFields(prev => {
      if (JSON.stringify(prev) !== JSON.stringify(customerFieldsFromAPI)) {
        return customerFieldsFromAPI;
      }
      return prev;
    });

    setDocumentFields(prev => {
      if (JSON.stringify(prev) !== JSON.stringify(documentFieldsFromAPI)) {
        return documentFieldsFromAPI;
      }
      return prev;
    });
  }, [open, allFieldConfigs]); // 只依赖 open 和 allFieldConfigs

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setIsAddingField(false);
    setEditingField(null);
  };

  const getCurrentFields = () => {
    return tabValue === 0 ? customerFields : documentFields;
  };

  const setCurrentFields = (fields: CustomField[]) => {
    if (tabValue === 0) {
      setCustomerFields(fields);
    } else {
      setDocumentFields(fields);
    }
  };

  const handleAddField = () => {
    setIsAddingField(true);
    setEditingField(null);
    setNewField({
      name: "",
      type: "text",
      required: false,
      options: [],
      defaultValue: "",
      placeholder: "",
    });
  };

  const handleEditField = (field: CustomField) => {
    setEditingField(field);
    setIsAddingField(false);
    setNewField({ ...field });
  };

  const handleSaveField = async () => {
    if (!newField.name) {
      return;
    }

    try {
      const category = tabValue === 0 ? "customer" : "document";

      if (editingField) {
        // 更新现有字段
        const updatedField = await updateFieldMutation.mutateAsync({
          id: editingField.id,
          name: newField.name!,
          type: newField.type!,
          required: newField.required!,
          options: newField.options,
          defaultValue: newField.defaultValue,
          placeholder: newField.placeholder,
          category,
          tenantId: tenantId!,
        });

        // 更新本地状态
        const currentFields = getCurrentFields();
        const updatedFields = currentFields.map((f) =>
          f.id === editingField.id ? {
            ...f,
            name: updatedField.name,
            type: updatedField.type as any,
            required: updatedField.required,
            options: updatedField.options as string[] || [],
            defaultValue: updatedField.defaultValue || "",
            placeholder: updatedField.placeholder || "",
          } : f
        );
        setCurrentFields(updatedFields);
      } else {
        // 创建新字段
        const newFieldConfig = await createFieldMutation.mutateAsync({
          tenantId: tenantId!,
          category,
          name: newField.name!,
          type: newField.type!,
          required: newField.required!,
          options: newField.options,
          defaultValue: newField.defaultValue,
          placeholder: newField.placeholder,
        });

        // 添加到本地状态
        const field: CustomField = {
          id: newFieldConfig.id,
          name: newFieldConfig.name,
          type: newFieldConfig.type as any,
          required: newFieldConfig.required,
          options: newFieldConfig.options as string[] || [],
          defaultValue: newFieldConfig.defaultValue || "",
          placeholder: newFieldConfig.placeholder || "",
        };

        const currentFields = getCurrentFields();
        setCurrentFields([...currentFields, field]);
      }

      setIsAddingField(false);
      setEditingField(null);
      // 重置表单
      setNewField({
        name: "",
        type: "text",
        required: false,
        options: [],
        defaultValue: "",
        placeholder: "",
      });

      // 刷新字段配置
      await refetchFields();
    } catch (error) {
      console.error("保存字段失败:", error);
      setSaveError(error instanceof Error ? error.message : "保存字段失败");
    }
  };

  const handleDeleteField = async (fieldId: string) => {
    const field = [...customerFields, ...documentFields].find(f => f.id === fieldId);
    if (!field) return;

    try {
      // 确认删除
      if (!confirm(`确定要删除字段"${field.name}"吗？此操作不可撤销。`)) {
        return;
      }

      // 删除字段 - 后端会自动检查使用情况
      await deleteFieldMutation.mutateAsync({ id: fieldId });

      // 从本地状态中移除
      const currentFields = getCurrentFields();
      const updatedFields = currentFields.filter((f) => f.id !== fieldId);
      setCurrentFields(updatedFields);

      // 刷新字段配置
      await refetchFields();

      console.log(`字段"${field.name}"已删除`);
    } catch (error) {
      console.error("删除字段失败:", error);
      // 如果是使用冲突错误，显示更友好的错误信息
      const errorMessage = error instanceof Error ? error.message : "删除字段失败";
      setSaveError(errorMessage);

      // 如果错误信息包含使用情况，显示alert
      if (errorMessage.includes("正在被") && errorMessage.includes("条记录使用")) {
        alert(errorMessage);
      }
    }
  };

  const handleCancelEdit = () => {
    setIsAddingField(false);
    setEditingField(null);
  };

  const handleClose = () => {
    setIsAddingField(false);
    setEditingField(null);
    onClose();
  };

  const handleSaveAll = async () => {
    // 由于字段现在是实时保存的，这里只需要关闭对话框
    onSave?.(customerFields, documentFields);
    console.log("✅ 所有字段配置已保存");
    handleClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: "80vh" },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography component="div" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
              自定义字段管理
            </Typography>
            <Typography variant="body2" color="text.secondary">
              管理客户信息和证件信息的自定义字段
            </Typography>
            {allFieldConfigs.length > 0 && (
              <Typography variant="caption" color="primary" sx={{ mt: 1, display: 'block' }}>
                已加载 {allFieldConfigs.length} 个字段配置
              </Typography>
            )}
            <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
              💡 提示：字段修改会立即保存。如果字段正在被使用，删除时会有提示。
            </Typography>
          </Box>
          <IconButton
            onClick={handleSaveAll}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {saveError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {saveError}
          </Alert>
        )}

        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="客户字段" />
            <Tab label="证件字段" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <FieldPanel
            fields={customerFields}
            onAddField={handleAddField}
            onEditField={handleEditField}
            onDeleteField={handleDeleteField}
            isAddingField={isAddingField}
            editingField={editingField}
            newField={newField}
            setNewField={setNewField}
            onSaveField={handleSaveField}
            onCancelEdit={handleCancelEdit}
            fieldType="客户"
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <FieldPanel
            fields={documentFields}
            onAddField={handleAddField}
            onEditField={handleEditField}
            onDeleteField={handleDeleteField}
            isAddingField={isAddingField}
            editingField={editingField}
            newField={newField}
            setNewField={setNewField}
            onSaveField={handleSaveField}
            onCancelEdit={handleCancelEdit}
            fieldType="证件"
          />
        </TabPanel>
      </DialogContent>
    </Dialog>
  );
}

interface FieldPanelProps {
  fields: CustomField[];
  onAddField: () => void;
  onEditField: (field: CustomField) => void;
  onDeleteField: (fieldId: string) => void;
  isAddingField: boolean;
  editingField: CustomField | null;
  newField: Partial<CustomField>;
  setNewField: (field: Partial<CustomField>) => void;
  onSaveField: () => void;
  onCancelEdit: () => void;
  fieldType: string;
}

function FieldPanel({
  fields,
  onAddField,
  onEditField,
  onDeleteField,
  isAddingField,
  editingField,
  newField,
  setNewField,
  onSaveField,
  onCancelEdit,
  fieldType,
}: FieldPanelProps) {
  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
        <Typography variant="h6">{fieldType}自定义字段</Typography>
        <Button
          startIcon={<Add />}
          onClick={onAddField}
          variant="outlined"
          size="small"
        >
          添加字段
        </Button>
      </Box>

      {fields.length === 0 && !isAddingField && (
        <Alert severity="info">
          暂无{fieldType}自定义字段，点击"添加字段"创建第一个字段
        </Alert>
      )}

      <List>
        {fields.map((field) => (
          <ListItem key={field.id} divider>
            <ListItemText
              primary={
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="subtitle1">{field.name}</Typography>
                  <Chip
                    label={field.type}
                    size="small"
                    variant="outlined"
                  />
                  {field.required && (
                    <Chip
                      label="必填"
                      size="small"
                      color="error"
                      variant="outlined"
                    />
                  )}
                </Box>
              }
              secondary={
                <Box component="div">
                  <Typography variant="body2" color="text.secondary" component="span">
                    字段名: {field.name}
                  </Typography>
                  {field.placeholder && (
                    <>
                      <br />
                      <Typography variant="body2" color="text.secondary" component="span">
                        占位符: {field.placeholder}
                      </Typography>
                    </>
                  )}
                  {field.options && field.options.length > 0 && (
                    <>
                      <br />
                      <Typography variant="body2" color="text.secondary" component="span">
                        选项: {field.options.join(", ")}
                      </Typography>
                    </>
                  )}
                </Box>
              }
              secondaryTypographyProps={{
                component: 'div'
              }}
            />
            <ListItemSecondaryAction>
              <IconButton
                edge="end"
                onClick={() => onEditField(field)}
                size="small"
                title="编辑字段"
              >
                <Edit />
              </IconButton>
              <IconButton
                edge="end"
                onClick={() => onDeleteField(field.id)}
                size="small"
                color="error"
                title="删除字段"
              >
                <Delete />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>

      {(isAddingField || editingField) && (
        <Box sx={{ mt: 2, p: 2, border: 1, borderColor: "divider", borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>
            {editingField ? "编辑字段" : "添加新字段"}
          </Typography>
          
          <FieldEditor
            field={newField}
            onChange={(updatedField) => setNewField((prev: any) => ({ ...prev, ...updatedField }))}
            onSave={onSaveField}
            onCancel={onCancelEdit}
          />
        </Box>
      )}
    </Box>
  );
}

interface FieldEditorProps {
  field: Partial<CustomField>;
  onChange: (field: Partial<CustomField>) => void;
  onSave: () => void;
  onCancel: () => void;
}

function FieldEditor({ field, onChange, onSave, onCancel }: FieldEditorProps) {
  const handleOptionsChange = (value: string) => {
    const options = value ? value.split(",").map((opt) => opt.trim()).filter(Boolean) : [];
    onChange({ ...field, options });
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <TextField
        label="字段名称"
        value={field.name || ""}
        onChange={(e) => onChange({ ...field, name: e.target.value })}
        size="small"
        required
        helperText="字段名称，将作为显示标签使用"
        fullWidth
      />

      <Box sx={{ display: "flex", gap: 2 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>字段类型</InputLabel>
          <Select
            value={field.type || "text"}
            onChange={(e) => onChange({ ...field, type: e.target.value as any })}
            label="字段类型"
          >
            <MenuItem value="text">文本</MenuItem>
            <MenuItem value="number">数字</MenuItem>
            <MenuItem value="date">日期</MenuItem>
            <MenuItem value="select">下拉选择</MenuItem>
            <MenuItem value="textarea">多行文本</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>是否必填</InputLabel>
          <Select
            value={field.required ? "true" : "false"}
            onChange={(e) => onChange({ ...field, required: e.target.value === "true" })}
            label="是否必填"
          >
            <MenuItem value="false">否</MenuItem>
            <MenuItem value="true">是</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <TextField
        label="占位符文本"
        value={field.placeholder || ""}
        onChange={(e) => onChange({ ...field, placeholder: e.target.value })}
        size="small"
        helperText="输入框的提示文本"
      />

      {field.type === "select" && (
        <TextField
          label="选项列表"
          value={field.options?.join(", ") || ""}
          onChange={(e) => handleOptionsChange(e.target.value)}
          size="small"
          multiline
          rows={2}
          helperText="用逗号分隔多个选项，如：选项1, 选项2, 选项3"
        />
      )}

      <TextField
        label="默认值"
        value={field.defaultValue || ""}
        onChange={(e) => onChange({ ...field, defaultValue: e.target.value })}
        size="small"
        helperText="字段的默认值（可选）"
      />

      <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
        <Button onClick={onCancel} startIcon={<Cancel />}>
          取消
        </Button>
        <Button
          onClick={onSave}
          variant="contained"
          startIcon={<Save />}
          disabled={!field.name}
        >
          保存
        </Button>
      </Box>
    </Box>
  );
}
