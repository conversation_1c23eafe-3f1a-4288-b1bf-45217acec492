"use client";

import { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  IconButton,
  Alert,
  Stack,
  Divider,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { zhCN } from "@mui/x-date-pickers/locales";
import { Add, Delete } from "@mui/icons-material";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { api } from "~/trpc/react";

interface DynamicField {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "textarea" | "select";
  value: any;
  options?: string[];
  placeholder?: string;
  required?: boolean;
}

interface DocumentEditFormProps {
  document: any;
  tenantId: string;
  onSave: (data: any) => void;
  onCancel: () => void;
}

export function DocumentEditForm({ document, tenantId, onSave, onCancel }: DocumentEditFormProps) {
  // 基本字段状态 - 只保留默认的客户信息字段
  const [customerName, setCustomerName] = useState(document.customerName || "");
  const [phone, setPhone] = useState(document.phone || "");
  const [certType, setCertType] = useState(document.certType || "");
  const [certNumber, setCertNumber] = useState(document.certNumber || "");
  const [issueBy, setIssueBy] = useState(document.issueBy || "");
  const [validUntil, setValidUntil] = useState(
    document.validUntil ? dayjs(document.validUntil) : null
  );

  // 自定义字段状态
  const [customerFields, setCustomerFields] = useState<DynamicField[]>([]);
  const [documentFields, setDocumentFields] = useState<DynamicField[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 查询证件类型
  const { data: documentTypes = [], refetch: refetchDocumentTypes } = api.documentType.getAll.useQuery(
    { tenantId },
    { enabled: !!tenantId }
  );

  // 查询自定义字段配置
  const { data: fieldConfigs = [] } = api.customField.getAll.useQuery(
    { tenantId },
    { enabled: !!tenantId }
  );



  // 初始化自定义字段
  useEffect(() => {
    if (fieldConfigs.length > 0) {
      const customerFieldConfigs = fieldConfigs.filter(f => f.category === 'customer');
      const documentFieldConfigs = fieldConfigs.filter(f => f.category === 'document');

      // 初始化客户字段 - 包含已有值的字段和从基本字段迁移的数据
      const initCustomerFields: DynamicField[] = [];

      // 处理自定义字段配置中的字段
      customerFieldConfigs.forEach(config => {
        const fieldValue = document.customFields?.[config.name];
        if (fieldValue !== undefined && fieldValue !== null && fieldValue !== "" && fieldValue !== 0) {
          initCustomerFields.push({
            id: config.id,
            name: config.name,
            type: config.type as any,
            value: fieldValue,
            options: config.options ? (config.options as string[]) : [],
            placeholder: config.placeholder || "",
            required: config.required || false,
          });
        }
      });

      // 处理从基本字段迁移的数据（邮箱、地址、公司）
      const legacyFields = [
        { name: '邮箱地址', value: document.email, type: 'text' },
        { name: '地址', value: document.address, type: 'text' },
        { name: '公司名称', value: document.company, type: 'text' }
      ];

      legacyFields.forEach(legacy => {
        if (legacy.value && legacy.value.trim()) {
          // 检查是否已经有对应的自定义字段配置
          const existingConfig = customerFieldConfigs.find(config =>
            config.name === legacy.name ||
            config.name.toLowerCase().includes(legacy.name.toLowerCase())
          );

          if (existingConfig) {
            // 如果有配置但还没添加到字段列表中，添加它
            const alreadyAdded = initCustomerFields.some(field => field.id === existingConfig.id);
            if (!alreadyAdded) {
              initCustomerFields.push({
                id: existingConfig.id,
                name: existingConfig.name,
                type: existingConfig.type as any,
                value: legacy.value,
                options: existingConfig.options ? (existingConfig.options as string[]) : [],
                placeholder: existingConfig.placeholder || "",
                required: existingConfig.required || false,
              });
            }
          } else {
            // 如果没有配置，创建临时字段显示现有数据
            initCustomerFields.push({
              id: `legacy-${legacy.name}`,
              name: legacy.name,
              type: legacy.type as any,
              value: legacy.value,
              options: [],
              placeholder: "",
              required: false,
            });
          }
        }
      });

      // 初始化证件字段 - 只包含已有值的字段（非空值）
      const initDocumentFields: DynamicField[] = documentFieldConfigs
        .filter(config => {
          const fieldValue = document.customFields?.[config.name];
          return fieldValue !== undefined &&
                 fieldValue !== null &&
                 fieldValue !== "" &&
                 fieldValue !== 0; // 排除数字0作为空值
        })
        .map(config => ({
          id: config.id,
          name: config.name,
          type: config.type as any,
          value: document.customFields[config.name] || config.defaultValue || "",
          options: config.options ? (config.options as string[]) : [],
          placeholder: config.placeholder || "",
          required: config.required || false,
        }));

      setCustomerFields(initCustomerFields);
      setDocumentFields(initDocumentFields);
    }
  }, [fieldConfigs, document.customFields, document.email, document.address, document.company]);

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!customerName.trim()) {
      newErrors.customerName = "客户姓名不能为空";
    }

    if (!certType) {
      newErrors.certType = "请选择证件类型";
    }

    if (!validUntil) {
      newErrors.validUntil = "请选择到期日期";
    }

    // 验证自定义字段
    [...customerFields, ...documentFields].forEach(field => {
      if (field.required && !field.value) {
        newErrors[`custom_${field.id}`] = `${field.name}不能为空`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理保存
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    // 收集自定义字段数据
    const customFieldsData: Record<string, any> = {};
    [...customerFields, ...documentFields].forEach(field => {
      if (field.value !== undefined && field.value !== "") {
        customFieldsData[field.name] = field.value;
      }
    });

    const formData = {
      id: document.id,
      customerName: customerName.trim(),
      phone: phone.trim() || null,
      // 移除直接的邮箱、地址、公司字段，这些现在通过自定义字段处理
      certType,
      certNumber: certNumber.trim() || null,
      issueBy: issueBy.trim() || null,
      validUntil: validUntil?.toDate(),
      customFields: Object.keys(customFieldsData).length > 0 ? customFieldsData : null,
    };

    onSave(formData);
  };

  // 添加自定义字段
  const addCustomField = (category: 'customer' | 'document', fieldConfig: any) => {
    const newField: DynamicField = {
      id: fieldConfig.id,
      name: fieldConfig.name,
      type: fieldConfig.type,
      value: fieldConfig.defaultValue || "",
      options: fieldConfig.options ? (fieldConfig.options as string[]) : [],
      placeholder: fieldConfig.placeholder || "",
      required: fieldConfig.required || false,
    };

    if (category === 'customer') {
      setCustomerFields(prev => [...prev, newField]);
    } else {
      setDocumentFields(prev => [...prev, newField]);
    }
  };

  // 删除自定义字段
  const removeCustomField = (category: 'customer' | 'document', fieldId: string) => {
    if (category === 'customer') {
      setCustomerFields(prev => prev.filter(f => f.id !== fieldId));
    } else {
      setDocumentFields(prev => prev.filter(f => f.id !== fieldId));
    }
  };

  // 更新自定义字段值
  const updateCustomFieldValue = (category: 'customer' | 'document', fieldId: string, value: any) => {
    if (category === 'customer') {
      setCustomerFields(prev => prev.map(f => 
        f.id === fieldId ? { ...f, value } : f
      ));
    } else {
      setDocumentFields(prev => prev.map(f => 
        f.id === fieldId ? { ...f, value } : f
      ));
    }
  };

  // 渲染自定义字段输入组件
  const renderCustomField = (field: DynamicField, category: 'customer' | 'document') => {
    const error = errors[`custom_${field.id}`];

    return (
      <Grid item xs={12} sm={6} key={field.id}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {field.type === 'select' ? (
            <FormControl fullWidth required={field.required} size="small" error={!!error}>
              <InputLabel>{field.name}</InputLabel>
              <Select
                value={field.value || ""}
                onChange={(e) => updateCustomFieldValue(category, field.id, e.target.value)}
                label={field.name}
              >
                {field.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : field.type === 'date' ? (
            <DatePicker
              label={field.name}
              value={field.value ? dayjs(field.value) : null}
              onChange={(newValue) => {
                updateCustomFieldValue(category, field.id, newValue ? newValue.format('YYYY-MM-DD') : '');
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: field.required,
                  size: "small",
                  error: !!error,
                  helperText: error,
                },
              }}
            />
          ) : (
            <TextField
              fullWidth
              label={field.name}
              placeholder={field.placeholder || `请输入${field.name}`}
              value={field.value || ""}
              onChange={(e) => updateCustomFieldValue(category, field.id, e.target.value)}
              type={field.type === 'number' ? 'number' : 'text'}
              multiline={field.type === 'textarea'}
              rows={field.type === 'textarea' ? 3 : 1}
              required={field.required}
              size="small"
              error={!!error}
              helperText={error}
            />
          )}
          <IconButton
            size="small"
            onClick={() => removeCustomField(category, field.id)}
            title="删除字段"
            sx={{ color: 'error.main' }}
          >
            <Delete />
          </IconButton>
        </Box>
      </Grid>
    );
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale="zh-cn"
      localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
    >
      <Box
        component="form"
        id="document-edit-form"
        sx={{ p: 2 }}
        onSubmit={(e) => {
          e.preventDefault();
          handleSave();
        }}
      >
      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mb: 2 }}>
          请检查并修正表单中的错误
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* 基本信息 */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            基本信息
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="客户姓名"
            value={customerName}
            onChange={(e) => setCustomerName(e.target.value)}
            required
            size="small"
            error={!!errors.customerName}
            helperText={errors.customerName}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="联系电话"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            size="small"
          />
        </Grid>



        {/* 客户自定义字段 */}
        {(customerFields.length > 0 || fieldConfigs.filter(f => f.category === 'customer').length > 0) && (
          <>
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="h6" gutterBottom>
                客户自定义字段
              </Typography>
            </Grid>

            {/* 字段选择 */}
            {fieldConfigs.filter(f => f.category === 'customer').length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  选择客户字段：
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {fieldConfigs
                    .filter(f => f.category === 'customer')
                    .map((fieldConfig) => {
                      const isSelected = customerFields.some(cf => cf.id === fieldConfig.id);
                      return (
                        <Chip
                          key={fieldConfig.id}
                          label={fieldConfig.name}
                          variant={isSelected ? "filled" : "outlined"}
                          color={isSelected ? "primary" : "default"}
                          onClick={() => {
                            if (isSelected) {
                              removeCustomField('customer', fieldConfig.id);
                            } else {
                              addCustomField('customer', fieldConfig);
                            }
                          }}
                          size="small"
                        />
                      );
                    })}
                </Box>
              </Grid>
            )}

            {/* 客户字段输入 */}
            {customerFields.map((field) => renderCustomField(field, 'customer'))}
          </>
        )}

        {/* 证件信息 */}
        <Grid item xs={12}>
          <Divider sx={{ my: 1 }} />
          <Typography variant="h6" gutterBottom>
            证件信息
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required size="small" error={!!errors.certType}>
            <InputLabel>证件类型</InputLabel>
            <Select
              value={certType}
              onChange={(e) => setCertType(e.target.value)}
              label="证件类型"
            >
              {documentTypes.map((type) => (
                <MenuItem key={type.id} value={type.name}>
                  {type.icon} {type.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="证件编号"
            value={certNumber}
            onChange={(e) => setCertNumber(e.target.value)}
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="签发机关"
            value={issueBy}
            onChange={(e) => setIssueBy(e.target.value)}
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <DatePicker
            label="到期日期"
            value={validUntil}
            onChange={(newValue) => setValidUntil(newValue)}
            slotProps={{
              textField: {
                fullWidth: true,
                required: true,
                size: "small",
                error: !!errors.validUntil,
                helperText: errors.validUntil,
              },
            }}
          />
        </Grid>

        {/* 证件自定义字段 */}
        {(documentFields.length > 0 || fieldConfigs.filter(f => f.category === 'document').length > 0) && (
          <>
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="h6" gutterBottom>
                证件自定义字段
              </Typography>
            </Grid>

            {/* 字段选择 */}
            {fieldConfigs.filter(f => f.category === 'document').length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  选择证件字段：
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {fieldConfigs
                    .filter(f => f.category === 'document')
                    .map((fieldConfig) => {
                      const isSelected = documentFields.some(cf => cf.id === fieldConfig.id);
                      return (
                        <Chip
                          key={fieldConfig.id}
                          label={fieldConfig.name}
                          variant={isSelected ? "filled" : "outlined"}
                          color={isSelected ? "primary" : "default"}
                          onClick={() => {
                            if (isSelected) {
                              removeCustomField('document', fieldConfig.id);
                            } else {
                              addCustomField('document', fieldConfig);
                            }
                          }}
                          size="small"
                        />
                      );
                    })}
                </Box>
              </Grid>
            )}

            {/* 证件字段输入 */}
            {documentFields.map((field) => renderCustomField(field, 'document'))}
          </>
        )}
      </Grid>
    </Box>
    </LocalizationProvider>
  );
}
