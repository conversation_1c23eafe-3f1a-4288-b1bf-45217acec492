"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Grid,
  IconButton,
  Divider,
  Chip,
  Alert,
  Autocomplete,
  Paper,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import {
  Add,
  Delete,
  History,
} from "@mui/icons-material";
import dayjs from "dayjs";
import { api } from "~/trpc/react";

interface DynamicField {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "textarea" | "select";
  value: any;
  options?: string[];
  placeholder?: string;
  required?: boolean;
}

interface FieldConfig {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "textarea" | "select";
  category: "customer" | "document";
  required: boolean;
  options?: string[];
  placeholder?: string;
  defaultValue?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: {
    id: string;
    name: string | null;
    email: string | null;
  };
}

interface SmartFieldSelectorProps {
  title: string;
  category: "customer" | "document";
  fields: DynamicField[];
  onChange: (fields: DynamicField[]) => void;
  tenantId: string;
}

export function SmartFieldSelector({
  title,
  category,
  fields,
  onChange,
  tenantId,
}: SmartFieldSelectorProps) {
  const [newFieldName, setNewFieldName] = useState("");
  const [newFieldType, setNewFieldType] = useState<"text" | "number" | "date" | "textarea" | "select">("text");
  const [showHistoryFields, setShowHistoryFields] = useState(false);

  // 获取字段配置
  const { data: fieldConfigs = [] } = api.customField.getAll.useQuery({
    tenantId,
    category,
  }, {
    enabled: !!tenantId, // 只有当 tenantId 存在时才执行查询
  });

  // 检查字段是否已存在
  const isFieldExists = (fieldName: string) => {
    return fields.some(field => field.name.toLowerCase() === fieldName.toLowerCase());
  };

  // 获取可用的历史字段（未添加的）
  const availableHistoryFields = fieldConfigs.filter(
    config => !isFieldExists(config.name)
  );

  const addField = (fieldName?: string, fieldType?: string) => {
    const name = fieldName || newFieldName.trim();
    const type = fieldType || newFieldType;
    
    if (!name) return;

    // 检查重复
    if (isFieldExists(name)) {
      alert(`字段"${name}"已存在，请使用不同的名称`);
      return;
    }

    const newField: DynamicField = {
      id: Date.now().toString(),
      name,
      type: type as any,
      value: type === "date" ? null : "",
    };

    onChange([...fields, newField]);
    setNewFieldName("");
    setNewFieldType("text");
  };

  const addHistoryField = (config: FieldConfig) => {
    // 检查重复
    if (isFieldExists(config.name)) {
      alert(`字段"${config.name}"已存在，请使用不同的名称`);
      return;
    }

    const newField: DynamicField = {
      id: Date.now().toString(),
      name: config.name,
      type: config.type,
      value: config.type === "date" ? null : (config.defaultValue || ""),
      options: config.options,
      placeholder: config.placeholder,
      required: config.required,
    };

    onChange([...fields, newField]);
  };

  const removeField = (fieldId: string) => {
    onChange(fields.filter(field => field.id !== fieldId));
  };

  const updateFieldValue = (fieldId: string, value: any) => {
    onChange(fields.map(field => 
      field.id === fieldId ? { ...field, value } : field
    ));
  };

  const renderFieldValue = (field: DynamicField) => {
    switch (field.type) {
      case "text":
        return (
          <TextField
            fullWidth
            label={field.name}
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="medium"
          />
        );

      case "number":
        return (
          <TextField
            fullWidth
            label={field.name}
            type="number"
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="medium"
          />
        );

      case "textarea":
        return (
          <TextField
            fullWidth
            label={field.name}
            multiline
            rows={3}
            value={field.value || ""}
            onChange={(e) => updateFieldValue(field.id, e.target.value)}
            size="medium"
          />
        );

      case "date":
        return (
          <DatePicker
            label={field.name}
            value={field.value ? dayjs(field.value) : null}
            onChange={(date) => updateFieldValue(field.id, date?.toDate() || null)}
            slotProps={{
              textField: {
                fullWidth: true,
                size: "medium",
              },
            }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ mt: 3 }}>
      {/* 现有自定义字段 */}
      {fields.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="subtitle2" gutterBottom>
            {title}
          </Typography>
          <Grid container spacing={2}>
            {fields.map((field) => (
              <Grid 
                item 
                xs={12} 
                sm={field.type === "textarea" ? 12 : 6}
                key={field.id}
              >
                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                  <Box sx={{ flex: 1 }}>
                    {renderFieldValue(field)}
                  </Box>
                  <IconButton
                    onClick={() => removeField(field.id)}
                    size="small"
                    color="error"
                    sx={{ mt: 1 }}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* 历史字段快速选择 */}
      {availableHistoryFields.length > 0 && (
        <Box sx={{ mb: 3 }}>
          {fields.length === 0 && <Divider sx={{ mb: 2 }} />}
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
            <History fontSize="small" color="action" />
            <Typography variant="subtitle2">
              常用字段快速添加
            </Typography>
            <Typography variant="caption" color="text.secondary">
              (点击添加)
            </Typography>
          </Box>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
            {availableHistoryFields.slice(0, 8).map((config) => (
              <Chip
                key={config.name}
                label={`${config.name} (${config.type})`}
                variant="outlined"
                clickable
                onClick={() => addHistoryField(config as any)}
                size="small"
                sx={{ 
                  '&:hover': { 
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText'
                  }
                }}
              />
            ))}
          </Box>
        </Box>
      )}

      {/* 添加新字段 */}
      <Box>
        {fields.length === 0 && availableHistoryFields.length === 0 && <Divider sx={{ mb: 2 }} />}
        <Typography variant="subtitle2" gutterBottom>
          添加新{category === "customer" ? "客户" : "证件"}字段
        </Typography>
        
        {/* 智能字段名称输入 */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <Autocomplete
              freeSolo
              options={fieldConfigs.map(config => config.name)}
              value={newFieldName}
              onInputChange={(event, newValue) => {
                setNewFieldName(newValue || "");
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="字段名称"
                  size="small"
                  placeholder={`例如：${category === "customer" ? "客户等级" : "认证机构"}`}
                  error={newFieldName.trim() !== "" && isFieldExists(newFieldName)}
                  helperText={
                    newFieldName.trim() !== "" && isFieldExists(newFieldName)
                      ? "该字段已存在"
                      : ""
                  }
                />
              )}
              renderOption={(props, option) => {
                const config = fieldConfigs.find(c => c.name === option);
                return (
                  <li {...props}>
                    <Box>
                      <Typography variant="body2">{option}</Typography>
                      {config && (
                        <Typography variant="caption" color="text.secondary">
                          {config.type} • {config.required ? "必填" : "可选"}
                        </Typography>
                      )}
                    </Box>
                  </li>
                );
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>字段类型</InputLabel>
              <Select
                value={newFieldType}
                label="字段类型"
                onChange={(e) => setNewFieldType(e.target.value as any)}
              >
                <MenuItem value="text">文本</MenuItem>
                <MenuItem value="number">数字</MenuItem>
                <MenuItem value="date">日期</MenuItem>
                <MenuItem value="textarea">多行文本</MenuItem>
                <MenuItem value="select">下拉选择</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Button
              variant="outlined"
              onClick={() => addField()}
              disabled={!newFieldName.trim() || isFieldExists(newFieldName)}
              startIcon={<Add />}
              fullWidth
            >
              添加字段
            </Button>
          </Grid>
        </Grid>

        {/* 重复提示 */}
        {newFieldName.trim() !== "" && isFieldExists(newFieldName) && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            字段"{newFieldName}"已存在，请使用不同的名称或删除现有字段后重新添加。
          </Alert>
        )}
      </Box>
    </Box>
  );
}
