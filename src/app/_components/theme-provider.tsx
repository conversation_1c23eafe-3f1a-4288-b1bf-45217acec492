'use client';

import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
import { lightTheme, darkTheme } from '~/lib/theme';
import { ThemeContextProvider, useTheme } from '~/contexts/theme-context';

function MUIThemeProvider({ children }: { children: React.ReactNode }) {
  const { mode } = useTheme();
  const theme = mode === 'dark' ? darkTheme : lightTheme;

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}

export function ClientThemeProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <ThemeContextProvider>
        <MUIThemeProvider>
          {children}
        </MUIThemeProvider>
      </ThemeContextProvider>
    </AppRouterCacheProvider>
  );
}