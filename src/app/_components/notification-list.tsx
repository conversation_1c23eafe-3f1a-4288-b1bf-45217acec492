'use client';

import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  Stack,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
} from '@mui/material';
import {
  Notifications,
  NotificationsActive,
  Warning,
  Info,
  Error,
  CheckCircle,
  MoreVert,
  Delete,
  Edit,
  Add,
  Refresh,
} from '@mui/icons-material';
import React, { useState } from 'react';
import type { MouseEvent, ChangeEvent } from 'react';
import { api } from '~/trpc/react';
import { NotificationListSkeleton } from "~/components/ui/Skeleton";
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface NotificationListProps {
  tenantId: string;
}

const NOTIFICATION_TYPES = [
  { value: 'EXPIRY_WARNING', label: '到期提醒', icon: Warning, color: 'warning' as const },
  { value: 'EXPIRY_ALERT', label: '到期警告', icon: Error, color: 'error' as const },
  { value: 'DOCUMENT_ADDED', label: '证件添加', icon: CheckCircle, color: 'success' as const },
  { value: 'DOCUMENT_UPDATED', label: '证件更新', icon: Info, color: 'info' as const },
  { value: 'SYSTEM', label: '系统通知', icon: Notifications, color: 'inherit' as const },
];

const PRIORITY_LABELS = {
  LOW: '低',
  MEDIUM: '中',
  HIGH: '高',
  URGENT: '紧急',
};

const PRIORITY_COLORS = {
  LOW: 'default' as const,
  MEDIUM: 'primary' as const,
  HIGH: 'warning' as const,
  URGENT: 'error' as const,
};

interface CreateNotificationFormData {
  title: string;
  message: string;
  type: string;
  priority: string;
  documentId?: string;
}

const initialFormData: CreateNotificationFormData = {
  title: '',
  message: '',
  type: 'SYSTEM',
  priority: 'MEDIUM',
  documentId: undefined,
};

export function NotificationList({ tenantId }: NotificationListProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotificationId, setSelectedNotificationId] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState<CreateNotificationFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // 获取通知列表
  const {
    data: notificationData,
    isLoading,
    error,
    refetch,
  } = api.notification.getAll.useQuery({ tenantId });

  const notifications = notificationData?.notifications ?? [];

  // 获取证件列表（用于创建通知时选择关联证件）
  const { data: documents } = api.document.getAll.useQuery({
    tenantId,
    page: 1,
    limit: 100,
  });

  // 标记为已读
  const markAsReadMutation = api.notification.markAsRead.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  // 删除通知
  const deleteNotificationMutation = api.notification.delete.useMutation({
    onSuccess: () => {
      refetch();
      handleMenuClose();
    },
  });

  // 创建通知
  const createNotificationMutation = api.notification.create.useMutation({
    onSuccess: () => {
      refetch();
      handleCreateDialogClose();
    },
    onError: (error) => {
      console.error('创建通知失败:', error);
    },
  });

  const handleMenuOpen = (event: MouseEvent<HTMLElement>, notificationId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedNotificationId(notificationId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedNotificationId(null);
  };

  const handleMarkAsRead = (notificationId: string) => {
    markAsReadMutation.mutate({ id: notificationId, tenantId });
  };

  const handleDelete = () => {
    if (selectedNotificationId) {
      deleteNotificationMutation.mutate({ id: selectedNotificationId, tenantId });
    }
  };

  const handleCreateDialogOpen = () => {
    setCreateDialogOpen(true);
    setFormData(initialFormData);
    setFormErrors({});
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
    setFormData(initialFormData);
    setFormErrors({});
  };

  const handleInputChange = (field: keyof CreateNotificationFormData) => (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除错误
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (field: keyof CreateNotificationFormData) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除错误
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    }

    if (!formData.message.trim()) {
      newErrors.message = '消息内容不能为空';
    }

    if (!formData.type) {
      newErrors.type = '请选择通知类型';
    }

    if (!formData.priority) {
      newErrors.priority = '请选择优先级';
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateSubmit = () => {
    if (!validateForm()) {
      return;
    }

    createNotificationMutation.mutate({
      tenantId,
      title: formData.title.trim(),
      message: formData.message.trim(),
      type: formData.type as any,
      priority: formData.priority as any,
      documentId: formData.documentId || undefined,
    });
  };

  const getNotificationIcon = (type: string) => {
    const notificationType = NOTIFICATION_TYPES.find(t => t.value === type);
    if (!notificationType) return Notifications;
    return notificationType.icon;
  };

  const getNotificationColor = (type: string) => {
    const notificationType = NOTIFICATION_TYPES.find(t => t.value === type);
    if (!notificationType) return 'inherit';
    return notificationType.color;
  };

  if (isLoading) {
    return <NotificationListSkeleton />;
  }

  if (error) {
    return (
      <Alert severity="error">
        加载通知失败: {error.message}
      </Alert>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h2">
            通知中心
          </Typography>
          <Stack direction="row" spacing={1}>
            <Button
              startIcon={<Refresh />}
              onClick={() => refetch()}
              size="small"
            >
              刷新
            </Button>
            <Button
              startIcon={<Add />}
              variant="contained"
              onClick={handleCreateDialogOpen}
              size="small"
            >
              创建通知
            </Button>
          </Stack>
        </Box>

        {!notifications || notifications.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Notifications sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body1" color="text.secondary">
              暂无通知
            </Typography>
          </Box>
        ) : (
          <List>
            {notifications.map((notification, index) => {
              const IconComponent = getNotificationIcon(notification.type);
              const iconColor = getNotificationColor(notification.type);
              
              return (
                <Box key={notification.id}>
                  <ListItem
                    sx={{
                      bgcolor: notification.isRead ? 'transparent' : 'action.hover',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      <IconComponent color={iconColor} />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box component="div" display="flex" alignItems="center" gap={1}>
                          <Box
                            component="span"
                            sx={{
                              fontSize: '0.875rem',
                              fontWeight: notification.isRead ? 'normal' : 'bold',
                            }}
                          >
                            {notification.title}
                          </Box>
                          <Chip
                            label={PRIORITY_LABELS[notification.priority as keyof typeof PRIORITY_LABELS]}
                            size="small"
                            color={PRIORITY_COLORS[notification.priority as keyof typeof PRIORITY_COLORS]}
                          />
                          {!notification.isRead && (
                            <NotificationsActive color="primary" fontSize="small" />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box component="div">
                          <Box component="div" sx={{ fontSize: '0.875rem', color: 'text.secondary', mb: 0.5 }}>
                            {notification.message}
                          </Box>
                          <Box component="div" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                            {dayjs(notification.createdAt).fromNow()}
                          </Box>
                        </Box>
                      }
                    />
                    
                    <Box display="flex" alignItems="center" gap={1}>
                      {!notification.isRead && (
                        <Button
                          size="small"
                          onClick={() => handleMarkAsRead(notification.id)}
                          disabled={markAsReadMutation.isPending}
                        >
                          标记已读
                        </Button>
                      )}
                      <IconButton
                        onClick={(e) => handleMenuOpen(e, notification.id)}
                        size="small"
                      >
                        <MoreVert />
                      </IconButton>
                    </Box>
                  </ListItem>
                  {index < notifications.length - 1 && <Divider />}
                </Box>
              );
            })}
          </List>
        )}

        {/* 操作菜单 */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleDelete} disabled={deleteNotificationMutation.isPending}>
            <Delete fontSize="small" sx={{ mr: 1 }} />
            删除
          </MenuItem>
        </Menu>

        {/* 创建通知对话框 */}
        <Dialog open={createDialogOpen} onClose={handleCreateDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>创建通知</DialogTitle>
          <DialogContent>
            {createNotificationMutation.error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {createNotificationMutation.error.message}
              </Alert>
            )}
            
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="标题"
                  value={formData.title}
                  onChange={handleInputChange('title')}
                  error={Boolean(formErrors.title)}
                  helperText={formErrors.title}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="消息内容"
                  multiline
                  rows={3}
                  value={formData.message}
                  onChange={handleInputChange('message')}
                  error={Boolean(formErrors.message)}
                  helperText={formErrors.message}
                  required
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(formErrors.type)}>
                  <InputLabel required>通知类型</InputLabel>
                  <Select
                    value={formData.type}
                    label="通知类型"
                    onChange={handleSelectChange('type')}
                  >
                    {NOTIFICATION_TYPES.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.type && (
                    <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                      {formErrors.type}
                    </Box>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(formErrors.priority)}>
                  <InputLabel required>优先级</InputLabel>
                  <Select
                    value={formData.priority}
                    label="优先级"
                    onChange={handleSelectChange('priority')}
                  >
                    {Object.entries(PRIORITY_LABELS).map(([value, label]) => (
                      <MenuItem key={value} value={value}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.priority && (
                    <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                      {formErrors.priority}
                    </Box>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>关联证件（可选）</InputLabel>
                  <Select
                    value={formData.documentId || ''}
                    label="关联证件（可选）"
                    onChange={handleSelectChange('documentId')}
                  >
                    <MenuItem value="">
                      <em>无</em>
                    </MenuItem>
                    {documents?.documents?.map((doc) => (
                      <MenuItem key={doc.id} value={doc.id}>
                        {doc.customerName} - {doc.certNumber}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCreateDialogClose} disabled={createNotificationMutation.isPending}>
              取消
            </Button>
            <Button
              onClick={handleCreateSubmit}
              variant="contained"
              disabled={createNotificationMutation.isPending}
            >
              {createNotificationMutation.isPending ? '创建中...' : '创建'}
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
}