"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  Typography,
  TextField,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Divider,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Save,
  Cancel,
  Close,
  Palette,
  CreditCard,
  Flight,
  DriveEta,
  Business,
  Description,
  Security,
  School,
  Work,
  Home,
  LocalHospital,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

interface DocumentTypeManagementDialogProps {
  open: boolean;
  onClose: () => void;
  tenantId: string;
}

interface DocumentTypeForm {
  name: string;
  icon: string;
  color: string;
  description: string;
  sortOrder: number;
}

// 可用图标列表
const availableIcons = [
  { name: "CreditCard", icon: <CreditCard />, label: "身份证" },
  { name: "Flight", icon: <Flight />, label: "护照" },
  { name: "DriveEta", icon: <DriveEta />, label: "驾驶证" },
  { name: "Business", icon: <Business />, label: "营业执照" },
  { name: "Description", icon: <Description />, label: "文档" },
  { name: "Security", icon: <Security />, label: "安全" },
  { name: "School", icon: <School />, label: "学校" },
  { name: "Work", icon: <Work />, label: "工作" },
  { name: "Home", icon: <Home />, label: "住宅" },
  { name: "LocalHospital", icon: <LocalHospital />, label: "医疗" },
];

// 预设颜色
const presetColors = [
  "#1976d2", "#388e3c", "#f57c00", "#7b1fa2", "#d32f2f",
  "#0288d1", "#689f38", "#fbc02d", "#512da8", "#c2185b",
  "#00796b", "#455a64", "#5d4037", "#616161", "#37474f",
];

export function DocumentTypeManagementDialog({
  open,
  onClose,
  tenantId,
}: DocumentTypeManagementDialogProps) {
  const [editingType, setEditingType] = useState<string | null>(null);
  const [editingTypeData, setEditingTypeData] = useState<any>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [formData, setFormData] = useState<DocumentTypeForm>({
    name: "",
    icon: "Description",
    color: "#1976d2",
    description: "",
    sortOrder: 0,
  });

  // API hooks
  const { data: documentTypes = [], refetch } = api.documentType.getAll.useQuery(
    {
      tenantId,
      includeInactive: true,
    },
    {
      enabled: !!tenantId && open, // 只有当对话框打开且tenantId存在时才执行查询
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      refetchOnWindowFocus: false, // 避免不必要的重新获取
    }
  );

  const createMutation = api.documentType.create.useMutation({
    onSuccess: () => {
      refetch();
      resetForm();
    },
  });

  const updateMutation = api.documentType.update.useMutation({
    onSuccess: () => {
      refetch();
      resetForm();
    },
  });

  const deleteMutation = api.documentType.delete.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const initSystemTypesMutation = api.documentType.initializeSystemTypes.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  // 自动初始化默认证件类型
  useEffect(() => {
    if (open && tenantId && documentTypes.length === 0) {
      // 如果对话框打开且没有证件类型，自动初始化
      initSystemTypesMutation.mutate({ tenantId });
    }
  }, [open, tenantId, documentTypes.length]);

  const resetForm = () => {
    setFormData({
      name: "",
      icon: "Description",
      color: "#1976d2",
      description: "",
      sortOrder: 0,
    });
    setEditingType(null);
    setEditingTypeData(null);
    setIsAddingNew(false);
  };

  const handleEdit = (type: any) => {
    setFormData({
      name: type.name,
      icon: type.icon || "Description",
      color: type.color || "#1976d2",
      description: type.description || "",
      sortOrder: type.sortOrder || 0,
    });
    setEditingType(type.id);
    setEditingTypeData(type);
    setIsAddingNew(false);
  };

  const handleAddNew = () => {
    resetForm();
    setIsAddingNew(true);
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      return;
    }

    try {
      if (editingType) {
        // 对于系统类型，不发送 name 字段
        const updateData: any = {
          id: editingType,
          icon: formData.icon,
          color: formData.color,
          description: formData.description,
          sortOrder: formData.sortOrder,
        };

        // 只有非系统类型才允许修改名称
        if (!editingTypeData?.isSystem) {
          updateData.name = formData.name;
        }

        await updateMutation.mutateAsync(updateData);
      } else {
        await createMutation.mutateAsync({
          tenantId,
          name: formData.name,
          icon: formData.icon,
          color: formData.color,
          description: formData.description,
          sortOrder: formData.sortOrder,
        });
      }
      resetForm();
    } catch (error) {
      console.error("保存失败:", error);
    }
  };

  const handleDelete = async (typeId: string) => {
    if (confirm("确定要删除这个证件类型吗？")) {
      try {
        await deleteMutation.mutateAsync({ id: typeId });
      } catch (error) {
        console.error("删除失败:", error);
      }
    }
  };

  const handleInitSystemTypes = async () => {
    try {
      await initSystemTypesMutation.mutateAsync({ tenantId });
    } catch (error) {
      console.error("初始化失败:", error);
    }
  };

  const getIconComponent = (iconName: string) => {
    const iconData = availableIcons.find(icon => icon.name === iconName);
    return iconData ? iconData.icon : <Description />;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: "80vh" },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography component="div" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
              证件类型管理
            </Typography>
            <Typography variant="body2" color="text.secondary">
              管理商户的证件类型，设置图标和颜色
            </Typography>
          </Box>
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={handleAddNew}
            sx={{ mr: 2 }}
          >
            添加证件类型
          </Button>
        </Box>

        {/* 证件类型列表 */}
        <List>
          {documentTypes.map((type) => (
            <ListItem key={type.id} divider>
              <Box sx={{ display: "flex", alignItems: "center", mr: 2 }}>
                <Box
                  sx={{
                    color: type.color || "#1976d2",
                    display: "flex",
                    alignItems: "center",
                    mr: 1,
                  }}
                >
                  {getIconComponent(type.icon || "Description")}
                </Box>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: "50%",
                    backgroundColor: type.color || "#1976d2",
                  }}
                />
              </Box>
              <ListItemText
                primary={
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Typography variant="subtitle1">{type.name}</Typography>

                    {type.isSystem && (
                      <Chip
                        label="系统"
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    )}
                    {!type.isActive && (
                      <Chip
                        label="已禁用"
                        size="small"
                        color="error"
                        variant="outlined"
                      />
                    )}
                  </Box>
                }
                secondary={type.description}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  onClick={() => handleEdit(type)}
                  size="small"
                  sx={{ mr: 1 }}
                >
                  <Edit />
                </IconButton>
                {!type.isSystem && (
                  <IconButton
                    edge="end"
                    onClick={() => handleDelete(type.id)}
                    size="small"
                    color="error"
                    disabled={deleteMutation.isPending}
                  >
                    <Delete />
                  </IconButton>
                )}
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>

        {documentTypes.length === 0 && !initSystemTypesMutation.isPending && (
          <Alert severity="info" sx={{ mt: 2 }}>
            正在加载默认证件类型...
          </Alert>
        )}

        {initSystemTypesMutation.isPending && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              正在初始化默认证件类型...
            </Box>
          </Alert>
        )}

        {/* 编辑表单 */}
        {(isAddingNew || editingType) && (
          <Box sx={{ mt: 3, p: 2, border: 1, borderColor: "divider", borderRadius: 1 }}>
            <Typography variant="h6" gutterBottom>
              {editingType ? "编辑证件类型" : "添加证件类型"}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="类型名称"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  disabled={editingTypeData?.isSystem}
                  helperText={editingTypeData?.isSystem ? "系统预设类型不允许修改名称" : ""}
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="medium">
                  <InputLabel>图标</InputLabel>
                  <Select
                    value={formData.icon}
                    label="图标"
                    onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  >
                    {availableIcons.map((icon) => (
                      <MenuItem key={icon.name} value={icon.name}>
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                          {icon.icon}
                          <Typography>{icon.label}</Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="body2" gutterBottom>
                    颜色
                  </Typography>
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {presetColors.map((color) => (
                      <Box
                        key={color}
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          backgroundColor: color,
                          cursor: "pointer",
                          border: formData.color === color
                            ? (theme) => `3px solid ${theme.palette.primary.main}`
                            : (theme) => `1px solid ${theme.palette.divider}`,
                          transition: "border 0.2s ease",
                          "&:hover": {
                            transform: "scale(1.1)",
                            transition: "all 0.2s ease",
                          },
                        }}
                        onClick={() => setFormData({ ...formData, color })}
                      />
                    ))}
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="描述"
                  multiline
                  rows={2}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  size="small"
                />
              </Grid>
            </Grid>

            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end", mt: 2 }}>
              <Button onClick={resetForm} startIcon={<Cancel />}>
                取消
              </Button>
              <Button
                onClick={handleSave}
                variant="contained"
                startIcon={<Save />}
                disabled={!formData.name.trim() || createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending ? (
                  <CircularProgress size={16} />
                ) : (
                  "保存"
                )}
              </Button>
            </Box>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
}
