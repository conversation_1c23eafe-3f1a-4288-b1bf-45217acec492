"use client";

import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Visibility,
  Refresh,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

interface MerchantOrdersProps {
  tenantId: string;
}

export default function MerchantOrders({ tenantId }: MerchantOrdersProps) {

  // 获取商户订单
  const { data: orderData, isLoading, refetch } = api.order.getByTenant.useQuery({
    tenantId,
  });

  const orders = orderData?.orders || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING": return "warning";
      case "PAID": return "success";
      case "FAILED": return "error";
      case "CANCELLED": return "default";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING": return "待审核";
      case "PAID": return "已支付";
      case "FAILED": return "支付失败";
      case "CANCELLED": return "已取消";
      default: return status;
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            订单管理
          </Typography>
          <Tooltip title="刷新">
            <IconButton onClick={() => refetch()} size="small">
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : orders.length === 0 ? (
          <Alert severity="info">
            暂无订单记录，点击"升级"按钮开始升级您的套餐
          </Alert>
        ) : (
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>订单号</TableCell>
                  <TableCell>套餐</TableCell>
                  <TableCell>金额</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {order.orderNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {order.plan?.name || '未知套餐'}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        ${order.amount} {order.currency}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(order.status)}
                        color={getStatusColor(order.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="查看详情">
                        <IconButton size="small">
                          <Visibility fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </CardContent>
    </Card>
  );
}
