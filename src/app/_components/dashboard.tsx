'use client';

import {
  <PERSON>,
  Container,
  Grid,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Stack,
  Chip,
  Avatar,
  IconButton,
  Paper,

  List,
  ListItem,
  ListItemText,
  ListItemIcon,

  Alert,
  CircularProgress,
} from '@mui/material';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import {
  Warning,
  CheckCircle,
  Description,
  Notifications,
  TrendingUp,
  Group,
  Business,
  Error,
  NotificationsActive,
  History,
} from '@mui/icons-material';
import React, { useState, useEffect, type MouseEvent } from 'react';
import Link from 'next/link';
import { signOut, useSession } from 'next-auth/react';
import { api } from '~/trpc/react';
import { TenantManagement } from './tenant-management';
import { usePermissions } from '~/hooks/usePermissions';
import { DocumentTypeChart } from './charts/document-type-chart';
import { ExpirationTrendChart } from './charts/expiration-trend-chart';
import {
  StatsCardSkeleton,
  ListItemSkeleton,
  ChartSkeleton,
  NotificationListSkeleton
} from '~/components/ui/Skeleton';



export function Dashboard() {
  const { data: session, status } = useSession();
  const permissions = usePermissions();

  const [currentTenantId, setCurrentTenantId] = useState<string | null>(null);

  // 如果 session 还在加载，避免闪烁直接返回null
  if (status === "loading") {
    return null;
  }

  // 如果没有 session，不应该到达这里（应该被重定向到登录页）
  if (!session) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Alert severity="error">请先登录</Alert>
      </Box>
    );
  }

  // 根据角色决定显示哪种仪表板
  if (permissions.isSuperAdmin) {
    return <SuperAdminDashboard />;
  } else if (permissions.isTenantAdmin || permissions.isTenantMember) {
    // 商户管理员和商户成员使用相同的仪表盘
    return <TenantDashboard />;
  }

  // 默认显示骨架屏
  return <DashboardSkeleton />;
}

// 超级管理员仪表板
function SuperAdminDashboard() {
  const { data: session } = useSession();

  // 获取系统级统计
  const { data: systemStats, isLoading: systemStatsLoading } = api.adminTenant.getStats.useQuery();
  const { data: notificationStats, isLoading: notificationStatsLoading } = api.systemNotification.getNotificationStats.useQuery({ days: 30 });
  const { data: subscriptionStats, isLoading: subscriptionStatsLoading } = api.adminOrder.getStats.useQuery({});
  const { data: recentOrders, isLoading: recentOrdersLoading } = api.adminOrder.getAll.useQuery({ page: 1, limit: 5 });

  // 如果数据加载中，显示加载状态
  if (systemStatsLoading || notificationStatsLoading || subscriptionStatsLoading || recentOrdersLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4 }}>
        系统运营仪表盘
      </Typography>

      <Grid container spacing={3}>
        {/* 关键指标卡片 */}
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    总商户数
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats?.totalTenants || 0}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    活跃: {systemStats?.activeTenants || 0}
                  </Typography>
                </Box>
                <Business sx={{ fontSize: 48, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    总用户数
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats?.totalUsers || 0}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    本月新增: {systemStats?.thisMonthTenants || 0}
                  </Typography>
                </Box>
                <Group sx={{ fontSize: 48, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    总证件数
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats?.totalDocuments || 0}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    系统总计
                  </Typography>
                </Box>
                <Description sx={{ fontSize: 48, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    系统活跃度
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats?.activityScore || 0}%
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    运行良好
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 48, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 商户类型分布 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                商户类型分布
              </Typography>
              <Box sx={{ height: 300 }}>
                {systemStats?.tenantsByType && systemStats.tenantsByType.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={systemStats.tenantsByType.map((item: any) => ({
                          name: item.type === 'FREE' ? '免费版' :
                                item.type === 'BASIC' ? '基础版' :
                                item.type === 'PRO' ? '专业版' : '企业版',
                          value: item.count,
                          type: item.type
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }: any) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {systemStats.tenantsByType.map((entry: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={['#667eea', '#f093fb', '#4facfe', '#fa709a'][index % 4]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                    <Typography color="textSecondary">暂无商户数据</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 通知发送统计 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                通知发送统计（最近30天）
              </Typography>
              <Box sx={{ height: 300 }}>
                <Grid container spacing={2} sx={{ height: '100%' }}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" color="primary">
                        {notificationStats?.totalNotifications || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        总发送数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" color="success.main">
                        {notificationStats?.totalNotifications > 0
                          ? Math.round(((notificationStats?.successCount || 0) / notificationStats.totalNotifications) * 100)
                          : 0}%
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        成功率
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="info.main">
                        {notificationStats?.emailSent || 0}
                      </Typography>
                      <Typography variant="caption">邮件</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="success.main">
                        {notificationStats?.telegramSent || 0}
                      </Typography>
                      <Typography variant="caption">Telegram</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="warning.main">
                        {notificationStats?.pushSent || 0}
                      </Typography>
                      <Typography variant="caption">推送</Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 订阅数据统计 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                订阅数据统计
              </Typography>
              <Box sx={{ height: 300 }}>
                <Grid container spacing={2} sx={{ height: '100%' }}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', bgcolor: 'primary.light', color: 'primary.contrastText' }}>
                      <Typography variant="h3">
                        {subscriptionStats?.totalOrders || 0}
                      </Typography>
                      <Typography variant="body2">
                        总订单数
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', bgcolor: 'success.light', color: 'success.contrastText' }}>
                      <Typography variant="h3">
                        {subscriptionStats?.paidOrders || 0}
                      </Typography>
                      <Typography variant="body2">
                        已付费订单
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="info.main">
                        ${subscriptionStats?.monthlyRevenue || 0}
                      </Typography>
                      <Typography variant="caption">本月收入</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="warning.main">
                        {subscriptionStats?.pendingOrders || 0}
                      </Typography>
                      <Typography variant="caption">待处理订单</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h6" color="success.main">
                        {Math.round(subscriptionStats?.conversionRate || 0)}%
                      </Typography>
                      <Typography variant="caption">转化率</Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 最新订阅信息 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最新订阅信息
              </Typography>
              <Box sx={{ height: 300, overflow: 'auto' }}>
                {recentOrders?.orders?.length ? (
                  recentOrders.orders.map((order: any, index: number) => (
                    <Box key={order.id} sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 1,
                      mb: 1,
                      bgcolor: 'action.hover',
                      border: '1px solid',
                      borderColor: 'divider'
                    }}>
                      <Avatar sx={{
                        bgcolor: order.status === 'PAID' ? 'success.main' :
                                order.status === 'PENDING' ? 'warning.main' : 'error.main',
                        mr: 2,
                        width: 32,
                        height: 32
                      }}>
                        {order.tenant?.name?.charAt(0) || 'T'}
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {order.tenant?.name || '未知商户'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {order.subscription?.plan?.name || '未知套餐'} - ${order.amount}
                        </Typography>
                        <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                          {new Date(order.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                      <Chip
                        label={
                          order.status === 'PAID' ? '已付费' :
                          order.status === 'PENDING' ? '待处理' :
                          order.status === 'FAILED' ? '失败' :
                          order.status === 'CANCELLED' ? '已取消' : '退款'
                        }
                        size="small"
                        color={
                          order.status === 'PAID' ? 'success' :
                          order.status === 'PENDING' ? 'warning' : 'error'
                        }
                      />
                    </Box>
                  ))
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                    <Typography color="textSecondary">暂无订阅数据</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 系统概览统计 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                系统概览统计
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {systemStats?.totalTenants || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      总商户数
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {systemStats?.activeTenants || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      活跃商户
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {subscriptionStats?.totalOrders || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      总订单数
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      ${subscriptionStats?.totalRevenue || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      总收入
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}

// 商户仪表板（管理员和成员共用）
function TenantDashboard() {
  const { data: session } = useSession();
  const tenantId = session?.user?.currentTenantId;

  // 获取用户的商户列表，如果没有 currentTenantId 则使用第一个商户
  const { data: userTenants, isLoading: isLoadingTenants } = api.tenant.getAll.useQuery(undefined, {
    enabled: !tenantId,
  });

  // 如果没有 tenantId 但有可用的商户，使用第一个商户
  const effectiveTenantId = tenantId || userTenants?.[0]?.id;



  // 如果正在加载商户列表，显示骨架屏
  if (!tenantId && isLoadingTenants) {
    return <TenantDashboardSkeleton />;
  }

  // 如果完全没有商户，显示提示信息
  if (!effectiveTenantId) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Alert severity="warning">
          您还没有加入任何商户。请联系管理员邀请您加入商户，或创建新的商户。
        </Alert>
      </Container>
    );
  }

  // 获取租户信息
  const { data: tenantInfo } = api.tenant.getById.useQuery(
    { id: effectiveTenantId! },
    { enabled: Boolean(effectiveTenantId) }
  );

  // 获取证件统计信息
  const { data: documentStats } = api.document.getStats.useQuery(
    { tenantId: effectiveTenantId! },
    { enabled: Boolean(effectiveTenantId) }
  );

  // 获取即将到期的证件
  const { data: expiringDocuments } = api.document.getExpiringSoon.useQuery(
    { tenantId: effectiveTenantId!, days: 30 },
    { enabled: Boolean(effectiveTenantId) }
  );

  // 获取最近的通知
  const { data: recentNotifications } = api.notification.getRecent.useQuery(
    { tenantId: effectiveTenantId!, limit: 5 },
    { enabled: Boolean(effectiveTenantId) }
  );

  // 获取操作记录
  const { data: activityLogs } = api.document.getActivityLogs.useQuery(
    { tenantId: effectiveTenantId!, limit: 10 },
    { enabled: Boolean(effectiveTenantId) }
  );

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Grid container spacing={3}>


        {/* 数据统计卡片 */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Description sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    总证件数
                  </Typography>
                  <Typography variant="h4">
                    {documentStats?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +{documentStats?.thisMonth || 0} 本月新增
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Warning sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    即将到期
                  </Typography>
                  <Typography variant="h4">
                    {expiringDocuments?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="warning.main">
                    30天内到期
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    有效证件
                  </Typography>
                  <Typography variant="h4">
                    {documentStats?.valid || 0}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    状态良好
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Error sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    已过期
                  </Typography>
                  <Typography variant="h4">
                    {documentStats?.expired || 0}
                  </Typography>
                  <Typography variant="body2" color="error.main">
                    需要处理
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 证件类型分布图表 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                证件类型分布
              </Typography>

              <DocumentTypeChart tenantId={effectiveTenantId} />
            </CardContent>
          </Card>
        </Grid>

        {/* 到期趋势图表 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                到期趋势分析
              </Typography>
              <ExpirationTrendChart tenantId={effectiveTenantId} />
            </CardContent>
          </Card>
        </Grid>

        {/* 最近通知 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近通知
              </Typography>
              <List dense>
                {recentNotifications?.map((notification) => (
                  <ListItem key={notification.id} divider>
                    <ListItemIcon>
                      <NotificationsActive color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={notification.title}
                      secondary={
                        <span>
                          <Typography variant="body2" color="text.secondary" component="span" display="block">
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" component="span" display="block">
                            {new Date(notification.createdAt).toLocaleString()}
                          </Typography>
                        </span>
                      }
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="暂无通知"
                      secondary="当前没有新的通知消息"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* 操作记录 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近操作
              </Typography>
              <List dense>
                {activityLogs?.map((log, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <History color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body2" sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {log.action} - {log.details} - 操作人员: {log.user?.name || log.user?.email || '未知用户'} - {new Date(log.createdAt).toLocaleString()}
                        </Typography>
                      }
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="暂无操作记录"
                      secondary="当前没有操作记录"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}








// 保留原有的仪表板逻辑作为备用
function OriginalDashboard() {
  const { data: session } = useSession();

  const [currentTenantId, setCurrentTenantId] = useState<string | null>(null);

  // 获取用户的租户列表
  const { data: tenants, isLoading: isLoadingTenants } = api.tenant.getAll.useQuery();

  // 获取当前租户的统计信息
  const { data: documentStats } = api.document.getStats.useQuery(
    { tenantId: currentTenantId! },
    { enabled: Boolean(currentTenantId) }
  );

  // 获取未读通知数量
  const { data: notificationStats } = api.notification.getUnreadCount.useQuery(
    { tenantId: currentTenantId! },
    { enabled: Boolean(currentTenantId) }
  );

  // 获取即将到期的证件
  const { data: expiringDocuments } = api.document.getExpiringSoon.useQuery(
    { tenantId: currentTenantId!, days: 30 },
    { enabled: Boolean(currentTenantId) }
  );

  // 获取最近的通知
  const { data: recentNotifications } = api.notification.getAll.useQuery(
    { tenantId: currentTenantId!, limit: 5 },
    { enabled: Boolean(currentTenantId) }
  );

  // 设置默认租户
  useEffect(() => {
    if (tenants && tenants.length > 0 && !currentTenantId) {
      setCurrentTenantId(tenants[0]!.id);
    }
  }, [tenants, currentTenantId]);





  const handleTenantChange = (tenantId: string) => {
    setCurrentTenantId(tenantId);
  };

  const getExpiryChipColor = (days: number) => {
    if (days <= 0) return 'error';
    if (days <= 7) return 'warning';
    if (days <= 30) return 'info';
    return 'success';
  };

  const calculateDaysUntilExpiry = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (isLoadingTenants) {
    return <TenantDashboardSkeleton />;
  }

  if (!tenants || tenants.length === 0) {
    return (
      <Box sx={{ flexGrow: 1, p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          您还没有加入任何租户，请先创建或加入一个租户。
        </Alert>
        <TenantManagement onTenantChange={handleTenantChange} />
      </Box>
    );
  }

  if (!currentTenantId) {
    return <TenantDashboardSkeleton />;
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          欢迎回来，{session?.user?.name || '用户'}！
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          这里是您的证件管理概览
        </Typography>

        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      总证件数
                    </Typography>
                    <Typography variant="h4">
                      {documentStats?.total || 0}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Description />
                  </Avatar>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      即将到期
                    </Typography>
                    <Typography variant="h4" color="warning.main">
                      {documentStats?.expiringSoon || 0}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <Warning />
                  </Avatar>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      未读通知
                    </Typography>
                    <Typography variant="h4" color="info.main">
                      {notificationStats?.count || 0}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <Notifications />
                  </Avatar>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      活跃租户
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      {tenants.length}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <Business />
                  </Avatar>
                </Stack>
              </CardContent>
            </Card>
          </Grid>



          {/* Recent Notifications */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    近期通知
                  </Typography>
                  <Button size="small" component={Link} href="/notifications">查看全部</Button>
                </Box>
                
                {!recentNotifications?.notifications || recentNotifications.notifications.length === 0 ? (
                  <Box textAlign="center" py={4}>
                    <Notifications sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      暂无通知
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {recentNotifications.notifications.slice(0, 3).map((notification) => (
                      <ListItem key={notification.id} divider>
                        <ListItemIcon>
                          <Notifications color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={notification.title}
                          secondary={
                            <Box component="div">
                              <Box component="div" sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
                                {notification.message}
                              </Box>
                              <Box component="div" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                                {new Date(notification.createdAt).toLocaleString('zh-CN')}
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Upcoming Expiry */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    即将到期的证件
                  </Typography>
                  <Button size="small" component={Link} href="/documents">查看全部</Button>
                </Box>
                
                {!expiringDocuments || expiringDocuments.length === 0 ? (
                  <Box textAlign="center" py={4}>
                    <Description sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      暂无即将到期的证件
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {expiringDocuments.slice(0, 3).map((document) => {
                      const daysUntilExpiry = calculateDaysUntilExpiry(document.validUntil.toISOString());
                      return (
                        <ListItem key={document.id} divider>
                          <ListItemIcon>
                            <Description color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="subtitle2">
                                  {document.customerName} - {document.certNumber}
                                </Typography>
                                <Chip
                                  label={daysUntilExpiry < 0 ? `已过期 ${Math.abs(daysUntilExpiry)} 天` : `${daysUntilExpiry} 天`}
                                  size="small"
                                  color={getExpiryChipColor(daysUntilExpiry)}
                                />
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  证件类型: {document.certType}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  到期日期: {document.validUntil.toLocaleDateString('zh-CN')}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      );
                    })}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>


        </Grid>


      </Container>
    </Box>
  );
}

// 仪表板骨架屏组件
function DashboardSkeleton() {
  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Grid container spacing={3}>
        {/* 统计卡片骨架 */}
        {Array.from({ length: 4 }).map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatsCardSkeleton />
          </Grid>
        ))}

        {/* 图表骨架 */}
        <Grid item xs={12} md={8}>
          <ChartSkeleton height={400} />
        </Grid>

        {/* 通知列表骨架 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最新通知
              </Typography>
              <NotificationListSkeleton />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}

// 商户仪表板骨架屏组件
function TenantDashboardSkeleton() {
  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Grid container spacing={3}>
        {/* 统计卡片骨架 */}
        {Array.from({ length: 4 }).map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatsCardSkeleton />
          </Grid>
        ))}

        {/* 最近证件骨架 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近证件
              </Typography>
              {Array.from({ length: 5 }).map((_, index) => (
                <ListItemSkeleton key={index} />
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* 即将到期骨架 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                即将到期
              </Typography>
              {Array.from({ length: 5 }).map((_, index) => (
                <ListItemSkeleton key={index} />
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}