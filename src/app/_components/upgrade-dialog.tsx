"use client";

import { useState, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress,
  Divider,
  TextField,
  Stack,
  IconButton,
  Avatar,
  LinearProgress,
} from "@mui/material";
import {
  Check,
  Star,
  Business,
  Person,
  Close,
  CloudUpload,
  Delete,
  Image,
  Receipt,
  AccountBalance,
} from "@mui/icons-material";
import { api } from "~/trpc/react";

interface UpgradeDialogProps {
  open: boolean;
  onClose: () => void;
  currentPlan?: string;
  tenantId: string;
  onSuccess?: () => void;
}

export default function UpgradeDialog({ open, onClose, currentPlan, tenantId, onSuccess }: UpgradeDialogProps) {
  const [step, setStep] = useState<'select' | 'payment'>(open ? 'select' : 'select');
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [selectedPlanData, setSelectedPlanData] = useState<any>(null);
  const [paymentProofFile, setPaymentProofFile] = useState<File | null>(null);
  const [paymentProofUrl, setPaymentProofUrl] = useState<string>("");
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取可用套餐
  const { data: plans = [], isLoading } = api.adminPlan.getPublicPlans.useQuery(undefined, {
    enabled: open,
  });

  // 获取银行信息
  const { data: bankInfo } = api.systemConfig.getBankInfo.useQuery();

  // 创建订单
  const createOrderMutation = api.order.create.useMutation({
    onSuccess: () => {
      setLoading(false);
      onClose();
      onSuccess?.();
      // 重置状态
      setStep('select');
      setSelectedPlan(null);
      setSelectedPlanData(null);
      setPaymentProofFile(null);
      setPaymentProofUrl("");
    },
    onError: (error) => {
      setLoading(false);
      console.error('创建订单失败:', error);
    },
  });

  // 过滤掉免费试用套餐（用户已经在使用）
  const availablePlans = plans.filter(plan => plan.name !== "免费试用");

  const handleSelectPlan = (planId: string) => {
    const plan = plans.find(p => p.id === planId);
    setSelectedPlan(planId);
    setSelectedPlanData(plan);
  };

  const handleNext = () => {
    if (selectedPlan && selectedPlanData) {
      setStep('payment');
    }
  };

  const handleBack = () => {
    setStep('select');
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      alert("请上传图片文件（JPG、PNG、GIF、WebP）");
      return;
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      alert("文件大小不能超过5MB");
      return;
    }

    setPaymentProofFile(file);

    // 上传文件
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setPaymentProofUrl(result.url);
      } else {
        alert(result.error || '上传失败');
        setPaymentProofFile(null);
      }
    } catch (error) {
      console.error('上传错误:', error);
      alert('上传失败，请重试');
      setPaymentProofFile(null);
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveFile = () => {
    setPaymentProofFile(null);
    setPaymentProofUrl("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleCreateOrder = () => {
    if (!selectedPlan || !selectedPlanData || !paymentProofUrl) return;

    setLoading(true);
    createOrderMutation.mutate({
      tenantId,
      planId: selectedPlan,
      amount: selectedPlanData.price,
      currency: selectedPlanData.currency,
      paymentMethod: "MANUAL_TRANSFER",
      paymentProofUrl: paymentProofUrl,
    });
  };

  const handleClose = () => {
    onClose();
    // 重置状态
    setTimeout(() => {
      setStep('select');
      setSelectedPlan(null);
      setSelectedPlanData(null);
      setPaymentProofFile(null);
      setPaymentProofUrl("");
    }, 300);
  };

  const getPlanIcon = (planName: string) => {
    if (planName.includes("个人")) return <Person />;
    if (planName.includes("企业")) return <Business />;
    return <Star />;
  };

  const getPlanColor = (planName: string) => {
    if (planName.includes("个人")) return "primary";
    if (planName.includes("企业")) return "secondary";
    return "default";
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {step === 'payment' && (
                <IconButton onClick={handleBack} size="small">
                  <Close />
                </IconButton>
              )}
              <Box>
                <Typography variant="h5" component="div">
                  {step === 'select' ? '选择套餐' : '支付确认'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {step === 'select'
                    ? '升级您的账户，解锁更多专业功能'
                    : '上传支付凭证完成订单创建'
                  }
                </Typography>
              </Box>
            </Box>
            <Button
              onClick={handleClose}
              sx={{ minWidth: 'auto', p: 1 }}
              color="inherit"
            >
              <Close />
            </Button>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          {step === 'select' ? (
            // 套餐选择步骤
            isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {availablePlans.map((plan) => (
              <Grid item xs={12} md={6} key={plan.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: selectedPlan === plan.id ? 2 : 1,
                    borderColor: selectedPlan === plan.id ? 'primary.main' : 'divider',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      borderColor: 'primary.main',
                      transform: 'translateY(-2px)',
                      boxShadow: 2,
                    },
                  }}
                  onClick={() => handleSelectPlan(plan.id)}
                >
                  <CardContent sx={{ p: 3 }}>
                    {/* 套餐头部 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          bgcolor: `${getPlanColor(plan.name)}.main`,
                          color: 'white',
                          mr: 2,
                        }}
                      >
                        {getPlanIcon(plan.name)}
                      </Box>
                      <Box>
                        <Typography variant="h6" component="div">
                          {plan.name}
                        </Typography>
                        <Chip
                          label={plan.billingCycle === 'YEARLY' ? '年付' : '月付'}
                          size="small"
                          color={getPlanColor(plan.name) as any}
                          variant="outlined"
                        />
                      </Box>
                    </Box>

                    {/* 价格 */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h4" component="div" color="primary">
                        ${plan.price}
                        <Typography variant="body2" component="span" color="text.secondary">
                          /{plan.billingCycle === 'YEARLY' ? '年' : '月'}
                        </Typography>
                      </Typography>
                      {plan.description && (
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          {plan.description}
                        </Typography>
                      )}
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    {/* 功能列表 */}
                    <List dense sx={{ py: 0 }}>
                      {(plan.features as string[]).map((feature, index) => (
                        <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <Check color="success" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={feature}
                            primaryTypographyProps={{
                              variant: 'body2',
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>

                    {/* 选中指示器 */}
                    {selectedPlan === plan.id && (
                      <Box sx={{ mt: 2, textAlign: 'center' }}>
                        <Chip
                          label="已选择"
                          color="primary"
                          size="small"
                          icon={<Check />}
                        />
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
                </Grid>
              )
            ) : (
              // 支付步骤
              <Box>
                {/* 选中的套餐信息 */}
                {selectedPlanData && (
                  <Card sx={{ mb: 3, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <Receipt />
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6">{selectedPlanData.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedPlanData.description}
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="h5" color="primary">
                            ${selectedPlanData.price}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            /{selectedPlanData.billingCycle === 'YEARLY' ? '年' : '月'}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                )}

                {/* 银行信息 */}
                {bankInfo && (
                  <Card sx={{ mb: 3, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AccountBalance color="success" />
                        收款银行信息
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">银行名称</Typography>
                          <Typography variant="body1" fontWeight="medium">{bankInfo.bankName}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">账户名称</Typography>
                          <Typography variant="body1" fontWeight="medium">{bankInfo.accountName}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">账户号码</Typography>
                          <Typography variant="body1" fontWeight="medium" sx={{ fontFamily: 'monospace' }}>
                            {bankInfo.accountNumber}
                          </Typography>
                        </Grid>
                        {bankInfo.branchName && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2" color="text.secondary">开户行</Typography>
                            <Typography variant="body1" fontWeight="medium">{bankInfo.branchName}</Typography>
                          </Grid>
                        )}
                        {bankInfo.notes && (
                          <Grid item xs={12}>
                            <Typography variant="body2" color="text.secondary">备注</Typography>
                            <Typography variant="body2">{bankInfo.notes}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                )}

                {/* 支付说明 */}
                <Alert severity="info" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    <strong>支付流程：</strong><br />
                    1. 请先通过银行转账或其他方式完成付款<br />
                    2. 上传付款截图作为支付凭证<br />
                    3. 提交订单等待管理员审核<br />
                    4. 审核通过后您的套餐将自动激活
                  </Typography>
                </Alert>

                {/* 文件上传区域 */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    上传支付凭证 *
                  </Typography>



                  {!paymentProofFile ? (
                    <Card
                      sx={{
                        border: '2px dashed',
                        borderColor: 'grey.300',
                        bgcolor: 'grey.50',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: 'primary.main',
                          bgcolor: 'primary.50',
                        },
                      }}
                      onClick={handleFileSelect}
                    >
                      <CardContent sx={{ textAlign: 'center', py: 4 }}>
                        <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          点击上传支付凭证
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 5MB
                        </Typography>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ bgcolor: 'success.main' }}>
                            <Image />
                          </Avatar>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="body1" fontWeight="medium">
                              {paymentProofFile.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {(paymentProofFile.size / 1024 / 1024).toFixed(2)} MB
                            </Typography>
                            {uploading && (
                              <Box sx={{ mt: 1 }}>
                                <LinearProgress size="small" />
                                <Typography variant="caption" color="text.secondary">
                                  上传中...
                                </Typography>
                              </Box>
                            )}
                          </Box>
                          <IconButton onClick={handleRemoveFile} color="error" size="small">
                            <Delete />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>

                {/* 预览图片 */}
                {paymentProofUrl && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>
                      支付凭证预览
                    </Typography>
                    <Card>
                      <CardContent sx={{ p: 2 }}>
                        <img
                          src={paymentProofUrl}
                          alt="支付凭证"
                          style={{
                            width: '100%',
                            maxHeight: '300px',
                            objectFit: 'contain',
                            borderRadius: '8px',
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Box>
                )}
              </Box>
            )}

            {step === 'select' && selectedPlan && (
              <Alert severity="info" sx={{ mt: 3 }}>
                <Typography variant="body2">
                  选择套餐后，您需要上传支付凭证。等待激活成功。
                </Typography>
              </Alert>
            )}
          </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button onClick={handleClose} color="inherit">
            取消
          </Button>

          {step === 'select' ? (
            <Button
              onClick={handleNext}
              variant="contained"
              disabled={!selectedPlan}
            >
              下一步
            </Button>
          ) : (
            <Stack direction="row" spacing={1}>
              <Button onClick={handleBack} color="inherit">
                返回
              </Button>
              <Button
                onClick={handleCreateOrder}
                variant="contained"
                disabled={!paymentProofUrl || uploading || loading}
                startIcon={loading ? <CircularProgress size={16} /> : <Receipt />}
              >
                {loading ? '创建中...' : '创建订单'}
              </Button>
            </Stack>
          )}
        </DialogActions>
      </Dialog>

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        style={{ display: 'none' }}
      />
    </>
  );
}
