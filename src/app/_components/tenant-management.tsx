'use client';

import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  Stack,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Business,
  Person,
  Group,
  MoreVert,
  Delete,
  Edit,
  Add,
  Refresh,
  ExitToApp,
  PersonAdd,
  AdminPanelSettings,
  SupervisorAccount,
  AccountCircle,
} from '@mui/icons-material';
import React, { useState } from 'react';
import type { MouseEvent, ChangeEvent } from 'react';
import { api } from '~/trpc/react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface TenantManagementProps {
  currentTenantId?: string;
  onTenantChange?: (tenantId: string) => void;
}

const TENANT_TYPES = [
  { value: 'PERSONAL', label: '个人', icon: Person },
  { value: 'BUSINESS', label: '企业', icon: Business },
  { value: 'ORGANIZATION', label: '组织', icon: Group },
];

const MEMBER_ROLES = [
  { value: 'ADMIN', label: '管理员', icon: SupervisorAccount, color: 'warning' as const },
  { value: 'MEMBER', label: '成员', icon: AccountCircle, color: 'primary' as const },
];

interface CreateTenantFormData {
  name: string;
  description: string;
  type: string;
}

interface InviteMemberFormData {
  email: string;
  role: string;
}

const initialTenantFormData: CreateTenantFormData = {
  name: '',
  description: '',
  type: 'PERSONAL',
};

const initialInviteFormData: InviteMemberFormData = {
  email: '',
  role: 'MEMBER',
};

export function TenantManagement({ currentTenantId, onTenantChange }: TenantManagementProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [membersDialogOpen, setMembersDialogOpen] = useState(false);
  const [tenantFormData, setTenantFormData] = useState<CreateTenantFormData>(initialTenantFormData);
  const [inviteFormData, setInviteFormData] = useState<InviteMemberFormData>(initialInviteFormData);
  const [tenantFormErrors, setTenantFormErrors] = useState<Record<string, string>>({});
  const [inviteFormErrors, setInviteFormErrors] = useState<Record<string, string>>({});

  // 获取用户的商户列表
  const {
    data: tenants,
    isLoading: isLoadingTenants,
    error: tenantsError,
    refetch: refetchTenants,
  } = api.tenant.getAll.useQuery();

  // 获取当前商户的成员列表
  const {
    data: members,
    isLoading: isLoadingMembers,
    refetch: refetchMembers,
  } = api.tenant.getMembers.useQuery(
    { tenantId: selectedTenantId! },
    { enabled: Boolean(selectedTenantId) && membersDialogOpen }
  );

  // 创建商户
  const createTenantMutation = api.tenant.create.useMutation({
    onSuccess: (newTenant) => {
      refetchTenants();
      handleCreateDialogClose();
      onTenantChange?.(newTenant.id);
    },
    onError: (error) => {
      console.error('创建商户失败:', error);
    },
  });

  // 更新商户
  const updateTenantMutation = api.tenant.update.useMutation({
    onSuccess: () => {
      refetchTenants();
      handleMenuClose();
    },
  });

  // 删除商户
  const deleteTenantMutation = api.tenant.delete.useMutation({
    onSuccess: () => {
      refetchTenants();
      handleMenuClose();
      if (selectedTenantId === currentTenantId) {
        // 如果删除的是当前商户，切换到第一个可用商户
        const remainingTenants = tenants?.filter((t: any) => t.id !== selectedTenantId);
        if (remainingTenants && remainingTenants.length > 0) {
          onTenantChange?.(remainingTenants[0]!.id);
        }
      }
    },
  });

  // 邀请成员
  const inviteMemberMutation = api.tenant.inviteMember.useMutation({
    onSuccess: () => {
      refetchMembers();
      handleInviteDialogClose();
    },
    onError: (error) => {
      console.error('邀请成员失败:', error);
    },
  });

  // 更新成员角色
  const updateMemberRoleMutation = api.tenant.updateMemberRole.useMutation({
    onSuccess: () => {
      refetchMembers();
    },
  });

  // 移除成员
  const removeMemberMutation = api.tenant.removeMember.useMutation({
    onSuccess: () => {
      refetchMembers();
    },
  });

  // 离开商户
  const leaveTenantMutation = api.tenant.leave.useMutation({
    onSuccess: () => {
      refetchTenants();
      handleMenuClose();
      if (selectedTenantId === currentTenantId) {
        // 如果离开的是当前商户，切换到第一个可用商户
        const remainingTenants = tenants?.filter((t: any) => t.id !== selectedTenantId);
        if (remainingTenants && remainingTenants.length > 0) {
          onTenantChange?.(remainingTenants[0]!.id);
        }
      }
    },
  });

  const handleMenuOpen = (event: MouseEvent<HTMLElement>, tenantId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedTenantId(tenantId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTenantId(null);
  };

  const handleCreateDialogOpen = () => {
    setCreateDialogOpen(true);
    setTenantFormData(initialTenantFormData);
    setTenantFormErrors({});
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
    setTenantFormData(initialTenantFormData);
    setTenantFormErrors({});
  };

  const handleInviteDialogOpen = () => {
    setInviteDialogOpen(true);
    setInviteFormData(initialInviteFormData);
    setInviteFormErrors({});
    handleMenuClose();
  };

  const handleInviteDialogClose = () => {
    setInviteDialogOpen(false);
    setInviteFormData(initialInviteFormData);
    setInviteFormErrors({});
  };

  const handleMembersDialogOpen = () => {
    setMembersDialogOpen(true);
    handleMenuClose();
  };

  const handleMembersDialogClose = () => {
    setMembersDialogOpen(false);
  };

  const handleTenantInputChange = (field: keyof CreateTenantFormData) => (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    setTenantFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    // 清除错误
    if (tenantFormErrors[field]) {
      setTenantFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleTenantSelectChange = (field: keyof CreateTenantFormData) => (event: any) => {
    setTenantFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleInviteInputChange = (field: keyof InviteMemberFormData) => (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    setInviteFormData((prev: any) => ({
       ...prev,
       [field]: event.target.value,
     }));
     // 清除错误
     if (inviteFormErrors[field]) {
       setInviteFormErrors((prev: any) => {
         const newErrors = { ...prev };
         delete newErrors[field];
         return newErrors;
       });
    }
  };

  const handleInviteSelectChange = (field: keyof InviteMemberFormData) => (event: any) => {
    setInviteFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const validateTenantForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!tenantFormData.name.trim()) {
      newErrors.name = '商户名称不能为空';
    }

    if (!tenantFormData.type) {
      newErrors.type = '请选择商户类型';
    }

    setTenantFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateInviteForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!inviteFormData.email.trim()) {
      newErrors.email = '邮箱地址不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inviteFormData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!inviteFormData.role) {
      newErrors.role = '请选择成员角色';
    }

    setInviteFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateSubmit = () => {
    if (!validateTenantForm()) {
      return;
    }

    createTenantMutation.mutate({
      name: tenantFormData.name.trim(),
      description: tenantFormData.description.trim() || undefined,
      type: tenantFormData.type as any,
    });
  };

  const handleInviteSubmit = () => {
    if (!validateInviteForm() || !selectedTenantId) {
      return;
    }

    inviteMemberMutation.mutate({
      tenantId: selectedTenantId,
      email: inviteFormData.email.trim(),
      role: inviteFormData.role as any,
    });
  };

  const handleDeleteTenant = () => {
    if (selectedTenantId) {
      deleteTenantMutation.mutate({ id: selectedTenantId });
    }
  };

  const handleLeaveTenant = () => {
    if (selectedTenantId) {
      leaveTenantMutation.mutate({ tenantId: selectedTenantId });
    }
  };

  const getTenantTypeInfo = (type: string) => {
    return TENANT_TYPES.find(t => t.value === type) || TENANT_TYPES[0]!;
  };

  const getMemberRoleInfo = (role: string) => {
    return MEMBER_ROLES.find(r => r.value === role) || MEMBER_ROLES[1]!;
  };

  const getCurrentUserRole = (tenantId: string) => {
    const tenant = tenants?.find(t => t.id === tenantId);
    return tenant?.userRole;
  };

  const canManageTenant = (tenantId: string) => {
    const role = getCurrentUserRole(tenantId);
    return role === 'ADMIN';
  };

  const canDeleteTenant = (tenantId: string) => {
    const role = getCurrentUserRole(tenantId);
    return role === 'ADMIN';
  };

  if (isLoadingTenants) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (tenantsError) {
    return (
      <Alert severity="error">
        加载商户列表失败: {tenantsError.message}
      </Alert>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h2">
            商户管理
          </Typography>
          <Stack direction="row" spacing={1}>
            <Button
              startIcon={<Refresh />}
              onClick={() => refetchTenants()}
              size="small"
            >
              刷新
            </Button>
            <Button
              startIcon={<Add />}
              variant="contained"
              onClick={handleCreateDialogOpen}
              size="small"
            >
              创建商户
            </Button>
          </Stack>
        </Box>

        {!tenants || tenants.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Business sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body1" color="text.secondary">
              暂无商户
            </Typography>
          </Box>
        ) : (
          <List>
            {tenants.map((tenant, index) => {
              const typeInfo = getTenantTypeInfo(tenant.type);
              const TypeIcon = typeInfo.icon;
              const roleInfo = getMemberRoleInfo(tenant.userRole || 'TENANT_MEMBER');
              const RoleIcon = roleInfo.icon;
              
              return (
                <Box key={tenant.id}>
                  <ListItem
                    sx={{
                      bgcolor: tenant.id === currentTenantId ? 'action.selected' : 'transparent',
                      borderRadius: 1,
                      mb: 1,
                      cursor: 'pointer',
                    }}
                    onClick={() => onTenantChange?.(tenant.id)}
                  >
                    <ListItemIcon>
                      <TypeIcon color="primary" />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box component="div" display="flex" alignItems="center" gap={1}>
                          <Box component="span" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {tenant.name}
                          </Box>
                          <Chip
                            icon={<RoleIcon />}
                            label={roleInfo.label}
                            size="small"
                            color={roleInfo.color}
                          />
                          {tenant.id === currentTenantId && (
                            <Chip label="当前" size="small" color="success" />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box component="div">
                          <Box component="div" sx={{ fontSize: '0.875rem', color: 'text.secondary', mb: 0.5 }}>
                            {tenant.description || '暂无描述'}
                          </Box>
                          <Box component="div" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                            创建于 {dayjs(tenant.createdAt).fromNow()}
                          </Box>
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMenuOpen(e, tenant.id);
                        }}
                        size="small"
                      >
                        <MoreVert />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < tenants.length - 1 && <Divider />}
                </Box>
              );
            })}
          </List>
        )}

        {/* 操作菜单 */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedTenantId && canManageTenant(selectedTenantId) && (
            <MenuItem onClick={handleInviteDialogOpen}>
              <PersonAdd fontSize="small" sx={{ mr: 1 }} />
              邀请成员
            </MenuItem>
          )}
          <MenuItem onClick={handleMembersDialogOpen}>
            <Group fontSize="small" sx={{ mr: 1 }} />
            查看成员
          </MenuItem>
          {selectedTenantId && !canDeleteTenant(selectedTenantId) && (
            <MenuItem onClick={handleLeaveTenant}>
              <ExitToApp fontSize="small" sx={{ mr: 1 }} />
              离开商户
            </MenuItem>
          )}
          {selectedTenantId && canDeleteTenant(selectedTenantId) && (
            <MenuItem onClick={handleDeleteTenant} sx={{ color: 'error.main' }}>
              <Delete fontSize="small" sx={{ mr: 1 }} />
              删除商户
            </MenuItem>
          )}
        </Menu>

        {/* 创建商户对话框 */}
        <Dialog open={createDialogOpen} onClose={handleCreateDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>创建商户</DialogTitle>
          <DialogContent>
            {createTenantMutation.error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {createTenantMutation.error.message}
              </Alert>
            )}
            
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="商户名称"
                  value={tenantFormData.name}
                  onChange={handleTenantInputChange('name')}
                  error={Boolean(tenantFormErrors.name)}
                  helperText={tenantFormErrors.name}
                  required
                  size="small"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="描述"
                  multiline
                  rows={3}
                  value={tenantFormData.description}
                  onChange={handleTenantInputChange('description')}
                  error={Boolean(tenantFormErrors.description)}
                  helperText={tenantFormErrors.description}
                  size="small"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth error={Boolean(tenantFormErrors.type)} size="small">
                  <InputLabel required>商户类型</InputLabel>
                  <Select
                    value={tenantFormData.type}
                    label="商户类型"
                    onChange={handleTenantSelectChange('type')}
                  >
                    {TENANT_TYPES.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {tenantFormErrors.type && (
                    <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                      {tenantFormErrors.type}
                    </Box>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCreateDialogClose} disabled={createTenantMutation.isPending}>
              取消
            </Button>
            <Button
              onClick={handleCreateSubmit}
              variant="contained"
              disabled={createTenantMutation.isPending}
            >
              {createTenantMutation.isPending ? '创建中...' : '创建'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 邀请成员对话框 */}
        <Dialog open={inviteDialogOpen} onClose={handleInviteDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>邀请成员</DialogTitle>
          <DialogContent>
            {inviteMemberMutation.error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {inviteMemberMutation.error.message}
              </Alert>
            )}
            
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="邮箱地址"
                  type="email"
                  value={inviteFormData.email}
                  onChange={handleInviteInputChange('email')}
                  error={Boolean(inviteFormErrors.email)}
                  helperText={inviteFormErrors.email}
                  required
                  size="small"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth error={Boolean(inviteFormErrors.role)} size="small">
                  <InputLabel required>成员角色</InputLabel>
                  <Select
                    value={inviteFormData.role}
                    label="成员角色"
                    onChange={handleInviteSelectChange('role')}
                  >
                    {MEMBER_ROLES.map((role) => (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {inviteFormErrors.role && (
                    <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                      {inviteFormErrors.role}
                    </Box>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleInviteDialogClose} disabled={inviteMemberMutation.isPending}>
              取消
            </Button>
            <Button
              onClick={handleInviteSubmit}
              variant="contained"
              disabled={inviteMemberMutation.isPending}
            >
              {inviteMemberMutation.isPending ? '邀请中...' : '发送邀请'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 成员列表对话框 */}
        <Dialog open={membersDialogOpen} onClose={handleMembersDialogClose} maxWidth="md" fullWidth>
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">成员列表</Typography>
              {selectedTenantId && canManageTenant(selectedTenantId) && (
                <Button
                  startIcon={<PersonAdd />}
                  onClick={() => {
                    handleMembersDialogClose();
                    handleInviteDialogOpen();
                  }}
                  size="small"
                >
                  邀请成员
                </Button>
              )}
            </Box>
          </DialogTitle>
          <DialogContent>
            {isLoadingMembers ? (
              <Box display="flex" justifyContent="center" py={4}>
                <CircularProgress />
              </Box>
            ) : (
              <List>
                {members?.map((member, index) => {
                  const roleInfo = getMemberRoleInfo(member.role);
                  const RoleIcon = roleInfo.icon;
                  
                  return (
                    <Box key={member.id}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {member.user.name?.[0] || member.user.email?.[0] || '?'}
                          </Avatar>
                        </ListItemIcon>
                        
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="subtitle2">
                                {member.user.name || member.user.email}
                              </Typography>
                              <Chip
                                icon={<RoleIcon />}
                                label={roleInfo.label}
                                size="small"
                                color={roleInfo.color}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {member.user.email}
                            </Typography>
                          }
                        />
                        
                        {selectedTenantId && canManageTenant(selectedTenantId) && (
                          <ListItemSecondaryAction>
                            <Tooltip title="移除成员">
                              <IconButton
                                onClick={() => {
                                  removeMemberMutation.mutate({
                                    tenantId: selectedTenantId,
                                    userId: member.user.id,
                                  });
                                }}
                                size="small"
                                color="error"
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                      {index < (members?.length || 0) - 1 && <Divider />}
                    </Box>
                  );
                })}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleMembersDialogClose}>
              关闭
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
}