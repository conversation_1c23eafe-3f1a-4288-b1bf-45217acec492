'use client';

import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Chip,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  Grid,
  Pagination,
  TextField,
  Select,
  FormControl,
  InputLabel,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  MoreVert,
  Edit,
  Delete,
  Warning,
  CheckCircle,
  Error,
  Add,
  Search,
  FilterList,
} from '@mui/icons-material';
import React, { useState } from 'react';
import type { MouseEvent } from 'react';
import { api } from '~/trpc/react';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

interface DocumentListProps {
  tenantId: string;
  onAddDocument: () => void;
  onEditDocument: (documentId: string) => void;
}

const DOCUMENT_TYPE_LABELS = {
  ID_CARD: '身份证',
  PASSPORT: '护照',
  DRIVER_LICENSE: '驾驶证',
  BUSINESS_LICENSE: '营业执照',
  WORK_PERMIT: '工作许可证',
  VISA: '签证',
  OTHER: '其他',
};

const EXPIRY_STATUS_LABELS = {
  ALL: '全部',
  EXPIRING_SOON: '即将到期',
  EXPIRED: '已过期',
  ACTIVE: '有效',
};

export function DocumentList({ tenantId, onAddDocument, onEditDocument }: DocumentListProps) {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [documentType, setDocumentType] = useState<string>('');
  const [expiryStatus, setExpiryStatus] = useState<'ALL' | 'EXPIRING_SOON' | 'EXPIRED' | 'ACTIVE'>('ALL');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);

  const {
    data: documentsData,
    isLoading,
    error,
    refetch,
  } = api.document.getAll.useQuery({
    tenantId,
    page,
    limit: 12,
    search: search || undefined,
    certType: documentType || undefined,
    expiryStatus,
    sortBy: 'validUntil',
    sortOrder: 'asc',
  });

  const deleteDocumentMutation = api.document.delete.useMutation({
    onSuccess: () => {
      refetch();
      handleMenuClose();
    },
  });

  const handleMenuOpen = (event: MouseEvent<HTMLElement>, documentId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedDocumentId(documentId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDocumentId(null);
  };

  const handleEdit = () => {
    if (selectedDocumentId) {
      onEditDocument(selectedDocumentId);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedDocumentId) {
      deleteDocumentMutation.mutate({
        id: selectedDocumentId,
        tenantId,
      });
    }
  };

  const getExpiryStatus = (validUntil: Date) => {
    const now = new Date();
    const daysLeft = dayjs(validUntil).diff(dayjs(now), 'day');

    if (daysLeft < 0) {
      return { status: 'expired', color: 'error' as const, label: `已过期 ${Math.abs(daysLeft)} 天`, daysLeft };
    } else if (daysLeft <= 30) {
      return { status: 'expiring', color: 'warning' as const, label: `${daysLeft} 天`, daysLeft };
    } else {
      return { status: 'active', color: 'success' as const, label: `${daysLeft} 天`, daysLeft };
    }
  };

  const getExpiryIcon = (status: string) => {
    switch (status) {
      case 'expired':
        return <Error color="error" />;
      case 'expiring':
        return <Warning color="warning" />;
      case 'active':
        return <CheckCircle color="success" />;
      default:
        return null;
    }
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        加载证件列表失败：{error.message}
      </Alert>
    );
  }

  return (
    <Box>
      {/* 搜索和筛选栏 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="搜索客户姓名、证件号码..."
                value={search}
                onChange={(e: any) => setSearch(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>证件类型</InputLabel>
                <Select
                  value={documentType}
                  label="证件类型"
                  onChange={(e: any) => setDocumentType(e.target.value)}
                >
                  <MenuItem value="">全部</MenuItem>
                  {Object.entries(DOCUMENT_TYPE_LABELS).map(([value, label]) => (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>到期状态</InputLabel>
                <Select
                  value={expiryStatus}
                  label="到期状态"
                  onChange={(e: any) => setExpiryStatus(e.target.value as any)}
                >
                  {Object.entries(EXPIRY_STATUS_LABELS).map(([value, label]) => (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<Add />}
                onClick={onAddDocument}
              >
                添加证件
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 证件列表 */}
      {isLoading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {documentsData?.documents.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 6 }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  暂无证件数据
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  开始添加您的第一个证件吧
                </Typography>
                <Button variant="contained" startIcon={<Add />} onClick={onAddDocument}>
                  添加证件
                </Button>
              </CardContent>
            </Card>
          ) : (
            <>
              <Grid container spacing={3}>
                {documentsData?.documents.map((document) => {
                  const expiryInfo = getExpiryStatus(new Date(document.validUntil));
                  
                  return (
                    <Grid item xs={12} sm={6} md={4} key={document.id}>
                      <Card
                        sx={{
                          height: '100%',
                          transition: 'transform 0.2s, box-shadow 0.2s',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 4,
                          },
                        }}
                      >
                        <CardContent>
                          <Stack spacing={2}>
                            {/* 头部：客户姓名和操作菜单 */}
                            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                              <Typography variant="h6" component="h3" noWrap>
                                {document.customerName}
                              </Typography>
                              <IconButton
                                size="small"
                                onClick={(e) => handleMenuOpen(e, document.id)}
                              >
                                <MoreVert />
                              </IconButton>
                            </Stack>

                            {/* 证件类型 */}
                            <Chip
                              label={DOCUMENT_TYPE_LABELS[document.certType as keyof typeof DOCUMENT_TYPE_LABELS]}
                              size="small"
                              variant="outlined"
                            />

                            {/* 证件号码 */}
                            <Typography variant="body2" color="text.secondary">
                              证件号码：{document.certNumber}
                            </Typography>

                            {/* 到期信息 */}
                            <Stack direction="row" alignItems="center" spacing={1}>
                              {getExpiryIcon(expiryInfo.status)}
                              <Typography variant="body2" color={`${expiryInfo.color}.main`}>
                                {dayjs(document.validUntil).format('YYYY年MM月DD日')}
                              </Typography>
                            </Stack>

                            <Chip
                              label={expiryInfo.label}
                              color={expiryInfo.color}
                              size="small"
                            />

                            {/* 联系方式 */}
                            {document.phone && (
                              <Typography variant="body2" color="text.secondary">
                                电话：{document.phone}
                              </Typography>
                            )}
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>

              {/* 分页 */}
              {documentsData && documentsData.pagination.totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={4}>
                  <Pagination
                    count={documentsData.pagination.totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </>
      )}

      {/* 操作菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleEdit}>
          <Edit sx={{ mr: 1 }} />
          编辑
        </MenuItem>
        <MenuItem 
          onClick={handleDelete}
          disabled={deleteDocumentMutation.isPending}
        >
          <Delete sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>
    </Box>
  );
}