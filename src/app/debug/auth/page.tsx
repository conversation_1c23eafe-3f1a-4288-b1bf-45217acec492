"use client";

import { useSession } from "next-auth/react";
import { Box, Typography, Card, CardContent, Alert, Button } from "@mui/material";
import { useRouter } from "next/navigation";

export default function AuthDebugPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        认证状态调试
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Session Status
          </Typography>
          <Alert severity={status === "authenticated" ? "success" : status === "loading" ? "info" : "error"}>
            状态: {status}
          </Alert>
        </CardContent>
      </Card>

      {session && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Session Data
            </Typography>
            <Box component="pre" sx={{ 
              backgroundColor: 'grey.100', 
              p: 2, 
              borderRadius: 1, 
              overflow: 'auto',
              fontSize: '0.875rem'
            }}>
              {JSON.stringify(session, null, 2)}
            </Box>
          </CardContent>
        </Card>
      )}

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            关键字段检查
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography>
              <strong>User ID:</strong> {session?.user?.id || "❌ 未找到"}
            </Typography>
            <Typography>
              <strong>Email:</strong> {session?.user?.email || "❌ 未找到"}
            </Typography>
            <Typography>
              <strong>Name:</strong> {session?.user?.name || "❌ 未找到"}
            </Typography>
            <Typography>
              <strong>Current Tenant ID:</strong> {session?.user?.currentTenantId || "❌ 未找到"}
            </Typography>
            <Typography>
              <strong>Role:</strong> {(session?.user as any)?.role || "❌ 未找到"}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            问题诊断
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {status === "loading" && (
              <Alert severity="info">
                Session 正在加载中...
              </Alert>
            )}
            {status === "unauthenticated" && (
              <Alert severity="error">
                用户未认证，请先登录
              </Alert>
            )}
            {status === "authenticated" && !session?.user?.currentTenantId && (
              <Alert severity="warning">
                用户已认证但 currentTenantId 为空，这可能导致 API 调用失败
              </Alert>
            )}
            {status === "authenticated" && session?.user?.currentTenantId && (
              <Alert severity="success">
                认证状态正常，currentTenantId 存在
              </Alert>
            )}
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button 
          variant="contained" 
          onClick={() => router.push('/documents/new')}
        >
          测试添加证件页面
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => router.push('/auth/signin')}
        >
          重新登录
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => window.location.reload()}
        >
          刷新页面
        </Button>
      </Box>
    </Box>
  );
}
