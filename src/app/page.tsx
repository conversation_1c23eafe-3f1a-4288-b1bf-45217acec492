import { redirect } from "next/navigation";
import { auth } from "~/server/auth";
import { LandingPage } from "~/app/_components/landing-page";
import { HydrateClient } from "~/trpc/server";

export default async function Home() {
  const session = await auth();

  return (
    <HydrateClient>
      {session?.user ? (
        redirect("/dashboard")
      ) : (
        <LandingPage />
      )}
    </HydrateClient>
  );
}
