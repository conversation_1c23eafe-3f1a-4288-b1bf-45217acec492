import { NextRequest, NextResponse } from 'next/server';
import { hash } from 'bcryptjs';
import { db } from '~/server/db';

export async function POST(request: NextRequest) {
  try {
    const { name, email, phone, password, tenantName } = await request.json();

    // 验证输入
    if (!name || !email || !phone || !password || !tenantName) {
      return NextResponse.json(
        { message: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { message: '密码长度至少为6位' },
        { status: 400 }
      );
    }

    // 检查邮箱是否已存在
    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: '该邮箱已被注册' },
        { status: 400 }
      );
    }

    // 哈希密码
    const hashedPassword = await hash(password, 10);

    // 创建用户和个人租户
    const result = await db.$transaction(async (tx) => {
      // 创建商户（使用用户提供的商户名称）
      const tenant = await tx.tenant.create({
        data: {
          name: tenantName,
          type: 'FREE', // 默认创建免费用户，后续可以升级
        },
      });

      // 创建用户并关联租户
      const user = await tx.user.create({
        data: {
          name,
          email,
          phone,
          password: hashedPassword,
          role: 'TENANT_ADMIN', // 商户管理员角色
          tenantId: tenant.id,
          currentTenantId: tenant.id, // 设置当前商户ID
        },
      });

      // 创建成员关系
      await tx.membership.create({
        data: {
          userId: user.id,
          tenantId: tenant.id,
          role: 'TENANT_ADMIN', // 使用TENANT_ADMIN角色
        },
      });

      // 创建默认证件类型
      const defaultDocumentTypes = [
        { name: '签证', code: 'VISA', icon: 'Flight', color: '#1976d2' },
        { name: '劳工证', code: 'WORK_PERMIT', icon: 'Work', color: '#388e3c' },
        { name: '驾驶证', code: 'DRIVER_LICENSE', icon: 'DriveEta', color: '#f57c00' },
        { name: '营业执照', code: 'BUSINESS_LICENSE', icon: 'Business', color: '#7b1fa2' },
      ];

      for (const docType of defaultDocumentTypes) {
        await tx.documentType.create({
          data: {
            ...docType,
            tenantId: tenant.id,
            createdById: user.id,
          },
        });
      }

      return { user, tenant };
    });

    return NextResponse.json(
      {
        message: '注册成功',
        user: {
          id: result.user.id,
          name: result.user.name,
          email: result.user.email,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('注册错误:', error);
    return NextResponse.json(
      { message: '服务器错误，请稍后重试' },
      { status: 500 }
    );
  }
}