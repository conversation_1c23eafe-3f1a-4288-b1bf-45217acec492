import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { db } from '~/server/db';

export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取系统配置
    const systemConfig = await db.systemNotificationConfig.findFirst({
      orderBy: { createdAt: "desc" },
    });

    if (!systemConfig?.telegramBotToken) {
      return NextResponse.json({ error: 'Telegram Bot Token not configured' }, { status: 400 });
    }

    // 检查 NEXTAUTH_URL 配置
    if (!process.env.NEXTAUTH_URL) {
      return NextResponse.json({
        success: false,
        message: 'NEXTAUTH_URL 环境变量未配置',
        suggestion: '请在 Vercel 项目设置中添加 NEXTAUTH_URL 环境变量',
      }, { status: 400 });
    }

    // 设置 Webhook URL
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`;
    const telegramApiUrl = `https://api.telegram.org/bot${systemConfig.telegramBotToken}/setWebhook`;

    // 首先测试域名是否可访问
    try {
      const testResponse = await fetch(process.env.NEXTAUTH_URL, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5秒超时
      });
      console.log(`域名测试结果: ${testResponse.status}`);
    } catch (testError) {
      return NextResponse.json({
        success: false,
        message: '域名无法访问',
        error: `无法访问 ${process.env.NEXTAUTH_URL}`,
        suggestion: '请确认域名已正确配置且可从外网访问',
      }, { status: 400 });
    }

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message'],
        drop_pending_updates: true, // 清除待处理的更新
      }),
    });

    const result = await response.json();

    if (result.ok) {
      return NextResponse.json({
        success: true,
        message: 'Webhook设置成功',
        webhookUrl,
        result,
      });
    } else {
      let suggestion = '';
      if (result.description?.includes('Failed to resolve host')) {
        suggestion = '域名解析失败，请检查: 1) NEXTAUTH_URL 是否正确 2) 域名 DNS 是否生效 3) 域名是否可从外网访问';
      } else if (result.description?.includes('Connection refused')) {
        suggestion = '连接被拒绝，请确认域名和端口配置正确';
      } else if (result.description?.includes('SSL')) {
        suggestion = 'SSL 证书问题，请确认使用 HTTPS 且证书有效';
      }

      return NextResponse.json({
        success: false,
        message: `Webhook设置失败: ${result.description}`,
        error: result,
        suggestion,
        webhookUrl,
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Setup webhook error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}

// 获取 Webhook 信息
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取系统配置
    const systemConfig = await db.systemNotificationConfig.findFirst({
      orderBy: { createdAt: "desc" },
    });

    if (!systemConfig?.telegramBotToken) {
      return NextResponse.json({ error: 'Telegram Bot Token not configured' }, { status: 400 });
    }

    // 获取 Webhook 信息
    const telegramApiUrl = `https://api.telegram.org/bot${systemConfig.telegramBotToken}/getWebhookInfo`;
    const response = await fetch(telegramApiUrl);
    const result = await response.json();

    if (result.ok) {
      return NextResponse.json({
        success: true,
        webhookInfo: result.result,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取Webhook信息失败: ${result.description}`,
        error: result,
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Get webhook info error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
