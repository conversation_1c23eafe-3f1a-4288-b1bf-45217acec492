import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { db } from '~/server/db';

export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取系统配置
    const systemConfig = await db.systemNotificationConfig.findFirst({
      orderBy: { createdAt: "desc" },
    });

    if (!systemConfig?.telegramBotToken) {
      return NextResponse.json({ error: 'Telegram Bot Token not configured' }, { status: 400 });
    }

    // 设置 Webhook URL
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`;
    const telegramApiUrl = `https://api.telegram.org/bot${systemConfig.telegramBotToken}/setWebhook`;

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message'],
      }),
    });

    const result = await response.json();

    if (result.ok) {
      return NextResponse.json({
        success: true,
        message: 'Webhook设置成功',
        webhookUrl,
        result,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `Webhook设置失败: ${result.description}`,
        error: result,
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Setup webhook error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}

// 获取 Webhook 信息
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取系统配置
    const systemConfig = await db.systemNotificationConfig.findFirst({
      orderBy: { createdAt: "desc" },
    });

    if (!systemConfig?.telegramBotToken) {
      return NextResponse.json({ error: 'Telegram Bot Token not configured' }, { status: 400 });
    }

    // 获取 Webhook 信息
    const telegramApiUrl = `https://api.telegram.org/bot${systemConfig.telegramBotToken}/getWebhookInfo`;
    const response = await fetch(telegramApiUrl);
    const result = await response.json();

    if (result.ok) {
      return NextResponse.json({
        success: true,
        webhookInfo: result.result,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取Webhook信息失败: ${result.description}`,
        error: result,
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Get webhook info error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
