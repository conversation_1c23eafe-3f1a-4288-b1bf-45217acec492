import { NextRequest, NextResponse } from 'next/server';
import { db } from '~/server/db';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求来源（可选，增加安全性）
    const telegramToken = await getTelegramBotToken();
    if (!telegramToken) {
      return NextResponse.json({ error: 'Bot not configured' }, { status: 400 });
    }

    // 处理 Telegram 消息
    if (body.message) {
      await handleTelegramMessage(body.message, telegramToken);
    }

    return NextResponse.json({ ok: true });
  } catch (error) {
    console.error('Telegram webhook error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}

async function getTelegramBotToken() {
  const systemConfig = await db.systemNotificationConfig.findFirst({
    orderBy: { createdAt: "desc" },
  });
  return systemConfig?.telegramBotToken;
}

async function handleTelegramMessage(message: any, botToken: string) {
  const chatId = message.chat.id;
  const text = message.text;
  const userName = message.from?.first_name || '用户';

  let responseText = '';

  if (text?.startsWith('/start')) {
    responseText = `
🎉 欢迎使用证件提醒系统机器人！

👋 你好 ${userName}！

🆔 你的Telegram ID: \`${chatId}\`

💡 使用方法：
1. 复制上面的ID
2. 在证件提醒系统中粘贴这个ID
3. 开启Telegram通知功能

📱 常用命令：
/id - 获取我的Telegram ID
/help - 查看帮助信息

🔗 访问系统：${process.env.NEXTAUTH_URL || 'https://your-domain.com'}
    `;
  } else if (text?.startsWith('/id')) {
    responseText = `
🆔 你的Telegram信息：

👤 用户名: ${userName}
🔢 Chat ID: \`${chatId}\`

💡 使用说明：
1. 复制上面的 Chat ID: \`${chatId}\`
2. 在证件提醒系统的通知设置中粘贴这个ID
3. 保存设置后即可接收通知

⚠️ 注意：请确保这个聊天窗口保持开启，否则无法接收通知。
    `;
  } else if (text?.startsWith('/help')) {
    responseText = `
📖 证件提醒系统机器人帮助

🤖 功能介绍：
本机器人用于发送证件到期提醒通知。

📋 可用命令：
/start - 开始使用机器人
/id - 获取我的Telegram ID
/help - 查看此帮助信息

🔧 设置步骤：
1. 发送 /id 命令获取您的Telegram ID
2. 登录证件提醒系统
3. 进入通知设置页面
4. 将获取的ID粘贴到Telegram通知配置中
5. 开启Telegram通知功能

🔗 系统地址：${process.env.NEXTAUTH_URL || 'https://your-domain.com'}
    `;
  } else {
    responseText = `
👋 您好！我是证件提醒系统机器人。

🆔 您的Telegram ID: \`${chatId}\`

💡 常用命令：
/id - 获取我的Telegram ID
/help - 查看帮助信息

📱 如需设置通知，请使用 /id 命令获取您的ID。
    `;
  }

  // 发送回复消息
  await sendTelegramMessage(botToken, chatId, responseText);
}

async function sendTelegramMessage(botToken: string, chatId: number, text: string) {
  try {
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId,
        text: text,
        parse_mode: 'Markdown',
        disable_web_page_preview: true,
      }),
    });

    const result = await response.json();
    if (!result.ok) {
      console.error('Failed to send Telegram message:', result);
    }
  } catch (error) {
    console.error('Error sending Telegram message:', error);
  }
}
