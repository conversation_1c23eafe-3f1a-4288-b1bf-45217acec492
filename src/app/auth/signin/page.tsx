'use client';

import {
  <PERSON>,
  Container,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Stack,
  Divider,
  Alert,
  InputAdornment,
  IconButton,
} from '@mui/material';
import React, { useState } from 'react';
import type { FormEvent, ChangeEvent } from 'react';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowBack,
  Email,
  Visibility,
  VisibilityOff,
  Lock,
} from '@mui/icons-material';

export default function SignInPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!formData.email || !formData.password) {
      setError('请填写邮箱和密码');
      setLoading(false);
      return;
    }

    try {
      const result = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        // 添加调试信息
        console.log('登录错误:', result.error);

        // 处理不同类型的错误
        if (result.error === 'TENANT_DISABLED') {
          setError('您的商户账号已被禁用，请联系管理员');
        } else if (result.error === 'ALL_TENANTS_DISABLED') {
          setError('您所属的所有商户都已被禁用，请联系管理员');
        } else if (result.error === 'CredentialsSignin') {
          setError('邮箱或密码错误');
        } else {
          setError(`登录失败: ${result.error}`);
        }
      } else {
        router.push('/');
      }
    } catch (err) {
      setError('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };



  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Container maxWidth="sm">
        <Card sx={{ boxShadow: 4 }}>
          <CardContent sx={{ p: 4 }}>
            <Stack spacing={3}>
              {/* Header */}
              <Box textAlign="center">
                <Typography variant="h4" component="h1" gutterBottom>
                  登录到证件管理系统
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  使用您的账户登录以管理证件信息
                </Typography>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {/* Email/Password Login Form */}
              <Box component="form" onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="邮箱地址"
                    type="email"
                    value={formData.email}
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setFormData({ ...formData, email: e.target.value })
                    }
                    required
                    fullWidth
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    label="密码"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setFormData({ ...formData, password: e.target.value })
                    }
                    required
                    fullWidth
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Button
                    type="submit"
                    variant="contained"
                    size="small"
                    disabled={loading}
                    fullWidth
                    sx={{
                      py: 1.5,
                      fontSize: '1.1rem',
                      mt: 2,
                    }}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Stack>
              </Box>



              <Divider />

              {/* Register Link */}
              <Box textAlign="center">
                <Typography variant="body2" color="text.secondary">
                  还没有账户？{' '}
                  <Link
                    href="/auth/register"
                    style={{
                      color: '#1976d2',
                      textDecoration: 'none',
                      fontWeight: 500,
                    }}
                  >
                    立即注册
                  </Link>
                </Typography>
              </Box>

              {/* Back to Home */}
              <Box textAlign="center">
                <Button
                  component={Link}
                  href="/"
                  variant="text"
                  size="small"
                  startIcon={<ArrowBack />}
                  sx={{ color: 'text.secondary' }}
                >
                  返回首页
                </Button>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
}