"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  Grid,
  Paper,
  Divider,
} from "@mui/material";
import { Login } from "@mui/icons-material";

export default function TestLoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setResult(null);

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      setResult(result);

      if (result?.error) {
        console.log('登录错误:', result.error);
        
        // 处理不同类型的错误
        if (result.error === 'TENANT_DISABLED') {
          setError('您的商户账号已被禁用，请联系管理员');
        } else if (result.error === 'ALL_TENANTS_DISABLED') {
          setError('您所属的所有商户都已被禁用，请联系管理员');
        } else if (result.error === 'CredentialsSignin') {
          setError('邮箱或密码错误');
        } else {
          setError(`登录失败: ${result.error}`);
        }
      } else {
        setError("");
        alert('登录成功！');
      }
    } catch (err) {
      console.error('登录异常:', err);
      setError('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const testAccounts = [
    {
      name: "超级管理员",
      email: "<EMAIL>",
      password: "123456",
      note: "不受商户禁用影响",
      color: "success.main",
    },
    {
      name: "商户管理员",
      email: "<EMAIL>",
      password: "123456",
      note: "受商户状态影响",
      color: "warning.main",
    },
    {
      name: "商户成员",
      email: "<EMAIL>",
      password: "123456",
      note: "受商户状态影响",
      color: "warning.main",
    },
    {
      name: "商户用户",
      email: "<EMAIL>",
      password: "123456",
      note: "受商户状态影响",
      color: "warning.main",
    },
  ];

  const fillAccount = (account: any) => {
    setEmail(account.email);
    setPassword(account.password);
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom align="center">
        登录测试页面
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }} align="center">
        测试商户禁用后的登录限制功能
      </Typography>

      <Grid container spacing={3}>
        {/* 登录表单 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                登录测试
              </Typography>

              <form onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  label="邮箱"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  margin="normal"
                  required
                  size="small"
                />
                <TextField
                  fullWidth
                  label="密码"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  margin="normal"
                  required
                  size="small"
                />

                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={loading}
                  startIcon={<Login />}
                  sx={{ mt: 2 }}
                >
                  {loading ? '登录中...' : '登录'}
                </Button>
              </form>

              {/* 调试信息 */}
              {result && (
                <Paper variant="outlined" sx={{ mt: 2, p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    调试信息:
                  </Typography>
                  <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </Paper>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 测试账号 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                测试账号
              </Typography>

              {testAccounts.map((account, index) => (
                <Paper 
                  key={index}
                  variant="outlined" 
                  sx={{ p: 2, mb: 2, cursor: 'pointer' }}
                  onClick={() => fillAccount(account)}
                >
                  <Typography variant="subtitle2" gutterBottom>
                    {account.name}
                  </Typography>
                  <Typography variant="body2">
                    邮箱: {account.email}
                  </Typography>
                  <Typography variant="body2">
                    密码: {account.password}
                  </Typography>
                  <Typography variant="body2" sx={{ color: account.color }}>
                    {account.note}
                  </Typography>
                </Paper>
              ))}

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="textSecondary">
                点击上方账号卡片可自动填充登录信息
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 测试说明 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                测试说明
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>1. 正常状态测试:</strong> 使用任意账号登录，应该能够正常登录。
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>2. 商户禁用测试:</strong>
              </Typography>
              <Typography variant="body2" component="div" sx={{ ml: 2 }}>
                • 访问商户管理页面禁用一个商户<br/>
                • 使用该商户下的用户账号登录<br/>
                • 应显示: "您的商户账号已被禁用，请联系管理员"
              </Typography>

              <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                <strong>3. 超级管理员测试:</strong> 超级管理员不受商户禁用影响，始终可以登录。
              </Typography>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>快速测试链接:</strong><br/>
                  • <a href="/admin/test-tenant-disable" target="_blank">商户禁用控制页面</a><br/>
                  • <a href="/auth/signin" target="_blank">正式登录页面</a>
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
