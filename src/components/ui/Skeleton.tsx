import { Box, Skeleton as Mu<PERSON><PERSON><PERSON><PERSON>, Card, CardContent, Grid, Paper } from '@mui/material';

// 基础骨架屏组件
export const Skeleton = {
  // 文本骨架
  Text: ({ width = '100%', height = 20, ...props }: any) => (
    <MuiSkeleton variant="text" width={width} height={height} {...props} />
  ),

  // 圆形骨架（头像等）
  Circle: ({ size = 40, ...props }: any) => (
    <MuiSkeleton variant="circular" width={size} height={size} {...props} />
  ),

  // 矩形骨架
  Rectangle: ({ width = '100%', height = 100, ...props }: any) => (
    <MuiSkeleton variant="rectangular" width={width} height={height} {...props} />
  ),

  // 按钮骨架
  Button: ({ width = 100, height = 36, ...props }: any) => (
    <MuiSkeleton variant="rounded" width={width} height={height} {...props} />
  ),
};

// 卡片骨架屏
export const CardSkeleton = ({ children, ...props }: any) => (
  <Card {...props}>
    <CardContent>
      {children}
    </CardContent>
  </Card>
);

// 表格行骨架屏
export const TableRowSkeleton = ({ columns = 4 }: { columns?: number }) => (
  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', py: 2 }}>
    {Array.from({ length: columns }).map((_, index) => (
      <Skeleton.Text key={index} width={index === 0 ? '30%' : '20%'} />
    ))}
  </Box>
);

// 列表项骨架屏
export const ListItemSkeleton = () => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, py: 2 }}>
    <Skeleton.Circle size={40} />
    <Box sx={{ flex: 1 }}>
      <Skeleton.Text width="60%" />
      <Skeleton.Text width="40%" height={14} />
    </Box>
    <Skeleton.Button width={80} height={32} />
  </Box>
);

// 统计卡片骨架屏
export const StatsCardSkeleton = () => (
  <CardSkeleton>
    <Box sx={{ textAlign: 'center' }}>
      <Skeleton.Text width="40%" height={48} sx={{ mx: 'auto', mb: 1 }} />
      <Skeleton.Text width="60%" height={16} sx={{ mx: 'auto' }} />
    </Box>
  </CardSkeleton>
);

// 表单骨架屏
export const FormSkeleton = () => (
  <Paper sx={{ p: 3 }}>
    <Skeleton.Text width="30%" height={32} sx={{ mb: 3 }} />
    <Grid container spacing={2}>
      {Array.from({ length: 6 }).map((_, index) => (
        <Grid item xs={12} sm={6} key={index}>
          <Skeleton.Text width="25%" height={16} sx={{ mb: 1 }} />
          <Skeleton.Rectangle height={40} />
        </Grid>
      ))}
    </Grid>
    <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
      <Skeleton.Button width={80} />
      <Skeleton.Button width={100} />
    </Box>
  </Paper>
);

// 页面标题骨架屏
export const PageHeaderSkeleton = () => (
  <Box sx={{ mb: 3 }}>
    <Skeleton.Text width="25%" height={40} sx={{ mb: 1 }} />
    <Skeleton.Text width="40%" height={16} />
  </Box>
);

// 导航栏骨架屏
export const NavbarSkeleton = () => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
    <Skeleton.Text width="20%" height={32} />
    <Box sx={{ flex: 1 }} />
    <Skeleton.Circle size={32} />
    <Skeleton.Circle size={32} />
  </Box>
);

// 侧边栏骨架屏
export const SidebarSkeleton = () => (
  <Box sx={{ p: 2 }}>
    {Array.from({ length: 8 }).map((_, index) => (
      <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, py: 1.5 }}>
        <Skeleton.Rectangle width={20} height={20} />
        <Skeleton.Text width="70%" />
      </Box>
    ))}
  </Box>
);

// 图表骨架屏
export const ChartSkeleton = ({ height = 300 }: { height?: number }) => (
  <CardSkeleton>
    <Skeleton.Text width="30%" height={24} sx={{ mb: 2 }} />
    <Skeleton.Rectangle height={height} />
  </CardSkeleton>
);

// 日历骨架屏
export const CalendarSkeleton = () => (
  <Paper sx={{ p: 3 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
      <Skeleton.Button width={40} height={40} />
      <Skeleton.Text width="20%" height={32} />
      <Skeleton.Button width={40} height={40} />
    </Box>
    <Grid container spacing={1}>
      {Array.from({ length: 42 }).map((_, index) => (
        <Grid item xs={12/7} key={index}>
          <Box sx={{ aspectRatio: '1', p: 1 }}>
            <Skeleton.Rectangle height="100%" />
          </Box>
        </Grid>
      ))}
    </Grid>
  </Paper>
);

// 通知列表骨架屏
export const NotificationListSkeleton = () => (
  <Box>
    {Array.from({ length: 5 }).map((_, index) => (
      <Box key={index} sx={{ display: 'flex', gap: 2, p: 2, borderBottom: '1px solid #eee' }}>
        <Skeleton.Circle size={48} />
        <Box sx={{ flex: 1 }}>
          <Skeleton.Text width="80%" />
          <Skeleton.Text width="60%" height={14} />
          <Skeleton.Text width="30%" height={12} />
        </Box>
      </Box>
    ))}
  </Box>
);

// 证件卡片骨架屏
export const DocumentCardSkeleton = () => (
  <CardSkeleton>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
      <Box sx={{ flex: 1 }}>
        <Skeleton.Text width="60%" height={24} />
        <Skeleton.Text width="40%" height={16} />
      </Box>
      <Skeleton.Rectangle width={60} height={24} />
    </Box>
    <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
      <Skeleton.Text width="30%" height={14} />
      <Skeleton.Text width="40%" height={14} />
    </Box>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Skeleton.Text width="25%" height={14} />
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Skeleton.Button width={32} height={32} />
        <Skeleton.Button width={32} height={32} />
      </Box>
    </Box>
  </CardSkeleton>
);
