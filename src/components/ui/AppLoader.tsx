import { Box, useTheme, alpha } from '@mui/material';

// 应用级加载器 - 用于应用初始化和认证检查
export function AppLoader() {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        },
      }}
    >
      {/* 中心加载内容 */}
      <Box
        sx={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 3,
        }}
      >
        {/* Logo 区域 */}
        <Box
          sx={{
            position: 'relative',
            width: 80,
            height: 80,
            bgcolor: 'primary.main',
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'primary.contrastText',
            fontSize: '2rem',
            fontWeight: 'bold',
            boxShadow: theme.shadows[8],
            '&::after': {
              content: '""',
              position: 'absolute',
              top: -4,
              left: -4,
              right: -4,
              bottom: -4,
              border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
              borderRadius: 3,
              animation: 'ripple 2s infinite',
              '@keyframes ripple': {
                '0%': {
                  transform: 'scale(1)',
                  opacity: 1,
                },
                '100%': {
                  transform: 'scale(1.2)',
                  opacity: 0,
                },
              },
            },
          }}
        >
          T3
        </Box>

        {/* 加载动画 */}
        <Box
          sx={{
            width: 120,
            height: 4,
            bgcolor: alpha(theme.palette.primary.main, 0.1),
            borderRadius: 2,
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: '30%',
              bgcolor: 'primary.main',
              borderRadius: 2,
              animation: 'loading 1.5s ease-in-out infinite',
              '@keyframes loading': {
                '0%': {
                  left: '-30%',
                },
                '100%': {
                  left: '100%',
                },
              },
            }}
          />
        </Box>

        {/* 加载点动画 */}
        <Box
          sx={{
            display: 'flex',
            gap: 1,
          }}
        >
          {[0, 1, 2].map((index) => (
            <Box
              key={index}
              sx={{
                width: 8,
                height: 8,
                bgcolor: 'primary.main',
                borderRadius: '50%',
                animation: 'bounce 1.4s ease-in-out infinite both',
                animationDelay: `${index * 0.16}s`,
                '@keyframes bounce': {
                  '0%, 80%, 100%': {
                    transform: 'scale(0)',
                  },
                  '40%': {
                    transform: 'scale(1)',
                  },
                },
              }}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
}

// 简化版应用加载器 - 用于快速切换
export function SimpleAppLoader() {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          border: `3px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          borderTop: `3px solid ${theme.palette.primary.main}`,
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          '@keyframes spin': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' },
          },
        }}
      />
    </Box>
  );
}

// 页面切换加载器 - 用于页面间导航
export function PageTransitionLoader() {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        height: 3,
        bgcolor: alpha(theme.palette.primary.main, 0.1),
        zIndex: 9999,
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          height: '100%',
          bgcolor: 'primary.main',
          animation: 'progress 1s ease-in-out infinite',
          '@keyframes progress': {
            '0%': {
              width: '0%',
              marginLeft: '0%',
            },
            '50%': {
              width: '75%',
              marginLeft: '0%',
            },
            '100%': {
              width: '0%',
              marginLeft: '100%',
            },
          },
        }}
      />
    </Box>
  );
}

// 内容加载占位符 - 用于局部内容加载
export function ContentPlaceholder({ height = 200 }: { height?: number }) {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        height,
        bgcolor: alpha(theme.palette.action.hover, 0.5),
        borderRadius: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.4)}, transparent)`,
          animation: 'shimmer 1.5s infinite',
          '@keyframes shimmer': {
            '0%': { left: '-100%' },
            '100%': { left: '100%' },
          },
        },
      }}
    />
  );
}
