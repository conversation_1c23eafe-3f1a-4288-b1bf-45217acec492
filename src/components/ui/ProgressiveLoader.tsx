import { useState, useEffect, ReactNode } from 'react';
import { Box, Fade, Skeleton as MuiSkeleton } from '@mui/material';

interface ProgressiveLoaderProps {
  isLoading: boolean;
  skeleton: ReactNode;
  children: ReactNode;
  delay?: number; // 延迟显示骨架屏的时间（毫秒）
  fadeInDuration?: number; // 内容淡入动画时长
  minLoadingTime?: number; // 最小加载时间，避免闪烁
}

export function ProgressiveLoader({
  isLoading,
  skeleton,
  children,
  delay = 200,
  fadeInDuration = 300,
  minLoadingTime = 500,
}: ProgressiveLoaderProps) {
  const [showSkeleton, setShowSkeleton] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  useEffect(() => {
    if (isLoading) {
      // 记录开始加载的时间
      setLoadingStartTime(Date.now());
      setShowContent(false);
      
      // 延迟显示骨架屏，避免快速加载时的闪烁
      const timer = setTimeout(() => {
        setShowSkeleton(true);
      }, delay);

      return () => clearTimeout(timer);
    } else {
      // 计算已经加载的时间
      const loadedTime = loadingStartTime ? Date.now() - loadingStartTime : 0;
      const remainingTime = Math.max(0, minLoadingTime - loadedTime);

      // 确保最小加载时间，然后显示内容
      setTimeout(() => {
        setShowSkeleton(false);
        setShowContent(true);
      }, remainingTime);
    }
  }, [isLoading, delay, minLoadingTime, loadingStartTime]);

  if (isLoading && showSkeleton) {
    return (
      <Fade in timeout={fadeInDuration}>
        <Box>{skeleton}</Box>
      </Fade>
    );
  }

  if (!isLoading && showContent) {
    return (
      <Fade in timeout={fadeInDuration}>
        <Box>{children}</Box>
      </Fade>
    );
  }

  // 在延迟期间或过渡期间不显示任何内容
  return null;
}

// 智能骨架屏组件 - 根据内容自动生成骨架屏
interface SmartSkeletonProps {
  lines?: number;
  avatar?: boolean;
  button?: boolean;
  chart?: boolean;
  table?: { rows: number; columns: number };
  card?: boolean;
}

export function SmartSkeleton({
  lines = 3,
  avatar = false,
  button = false,
  chart = false,
  table,
  card = false,
}: SmartSkeletonProps) {
  const content = (
    <Box>
      {avatar && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MuiSkeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
          <Box sx={{ flex: 1 }}>
            <MuiSkeleton width="60%" />
            <MuiSkeleton width="40%" />
          </Box>
        </Box>
      )}
      
      {chart && (
        <MuiSkeleton variant="rectangular" height={200} sx={{ mb: 2 }} />
      )}
      
      {table && (
        <Box>
          {Array.from({ length: table.rows }).map((_, rowIndex) => (
            <Box key={rowIndex} sx={{ display: 'flex', gap: 2, mb: 1 }}>
              {Array.from({ length: table.columns }).map((_, colIndex) => (
                <MuiSkeleton key={colIndex} width={`${100 / table.columns}%`} />
              ))}
            </Box>
          ))}
        </Box>
      )}
      
      {!chart && !table && (
        <Box>
          {Array.from({ length: lines }).map((_, index) => (
            <MuiSkeleton
              key={index}
              width={index === lines - 1 ? '60%' : '100%'}
              sx={{ mb: 1 }}
            />
          ))}
        </Box>
      )}
      
      {button && (
        <Box sx={{ mt: 2 }}>
          <MuiSkeleton variant="rounded" width={100} height={36} />
        </Box>
      )}
    </Box>
  );

  if (card) {
    return (
      <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
        {content}
      </Box>
    );
  }

  return content;
}

// 数据加载状态管理 Hook
export function useProgressiveLoading(isLoading: boolean, options?: {
  delay?: number;
  minLoadingTime?: number;
}) {
  const [state, setState] = useState({
    showSkeleton: false,
    showContent: !isLoading,
    isTransitioning: false,
  });

  useEffect(() => {
    const delay = options?.delay ?? 200;
    const minLoadingTime = options?.minLoadingTime ?? 500;
    const startTime = Date.now();

    if (isLoading) {
      setState(prev => ({ ...prev, showContent: false, isTransitioning: true }));
      
      const timer = setTimeout(() => {
        setState(prev => ({ ...prev, showSkeleton: true }));
      }, delay);

      return () => clearTimeout(timer);
    } else {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, minLoadingTime - elapsed);

      setTimeout(() => {
        setState({
          showSkeleton: false,
          showContent: true,
          isTransitioning: false,
        });
      }, remaining);
    }
  }, [isLoading, options?.delay, options?.minLoadingTime]);

  return state;
}

// 页面级别的渐进式加载组件
interface PageLoaderProps {
  isLoading: boolean;
  children: ReactNode;
  skeletonType?: 'dashboard' | 'list' | 'form' | 'table' | 'custom';
  customSkeleton?: ReactNode;
}

export function PageLoader({
  isLoading,
  children,
  skeletonType = 'dashboard',
  customSkeleton,
}: PageLoaderProps) {
  const getSkeleton = () => {
    if (customSkeleton) return customSkeleton;

    switch (skeletonType) {
      case 'dashboard':
        return (
          <Box sx={{ p: 3 }}>
            <SmartSkeleton lines={1} />
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2, mt: 3 }}>
              {Array.from({ length: 4 }).map((_, i) => (
                <SmartSkeleton key={i} lines={2} card />
              ))}
            </Box>
            <Box sx={{ mt: 3 }}>
              <SmartSkeleton chart card />
            </Box>
          </Box>
        );
      
      case 'list':
        return (
          <Box sx={{ p: 3 }}>
            <SmartSkeleton lines={1} button />
            {Array.from({ length: 8 }).map((_, i) => (
              <SmartSkeleton key={i} avatar lines={2} />
            ))}
          </Box>
        );
      
      case 'form':
        return (
          <Box sx={{ p: 3 }}>
            <SmartSkeleton lines={1} />
            <Box sx={{ mt: 3 }}>
              {Array.from({ length: 6 }).map((_, i) => (
                <Box key={i} sx={{ mb: 2 }}>
                  <MuiSkeleton width="20%" height={20} sx={{ mb: 1 }} />
                  <MuiSkeleton height={40} />
                </Box>
              ))}
              <SmartSkeleton button />
            </Box>
          </Box>
        );
      
      case 'table':
        return (
          <Box sx={{ p: 3 }}>
            <SmartSkeleton lines={1} button />
            <SmartSkeleton table={{ rows: 10, columns: 5 }} />
          </Box>
        );
      
      default:
        return <SmartSkeleton lines={5} />;
    }
  };

  return (
    <ProgressiveLoader
      isLoading={isLoading}
      skeleton={getSkeleton()}
    >
      {children}
    </ProgressiveLoader>
  );
}
