import { Box, Fade, useTheme } from '@mui/material';
import { SidebarSkeleton, NavbarSkeleton } from './Skeleton';

// 全页面加载器 - 模拟完整的应用布局
export function FullPageLoader() {
  const theme = useTheme();

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 9999,
        display: 'flex',
        height: '100vh',
        width: '100vw',
        overflow: 'hidden',
      }}
    >
      {/* 侧边栏骨架 */}
      <Box
        sx={{
          width: 240,
          flexShrink: 0,
          bgcolor: 'background.paper',
          borderRight: `1px solid ${theme.palette.divider}`,
          display: { xs: 'none', md: 'block' },
        }}
      >
        <SidebarSkeleton />
      </Box>

      {/* 主内容区域骨架 */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 顶部导航栏骨架 */}
        <Box
          sx={{
            height: 64,
            bgcolor: 'background.paper',
            borderBottom: `1px solid ${theme.palette.divider}`,
            display: { xs: 'block', md: 'none' },
          }}
        >
          <NavbarSkeleton />
        </Box>

        {/* 页面内容骨架 */}
        <Box
          sx={{
            flex: 1,
            p: 3,
            overflow: 'auto',
            bgcolor: 'background.default',
          }}
        >
          <DashboardContentSkeleton />
        </Box>
      </Box>
    </Box>
  );
}

// 仪表板内容骨架
function DashboardContentSkeleton() {
  return (
    <Box>
      {/* 页面标题骨架 */}
      <Box sx={{ mb: 3 }}>
        <Box
          sx={{
            height: 32,
            width: '25%',
            bgcolor: 'action.hover',
            borderRadius: 1,
            mb: 1,
          }}
        />
        <Box
          sx={{
            height: 16,
            width: '40%',
            bgcolor: 'action.hover',
            borderRadius: 1,
          }}
        />
      </Box>

      {/* 统计卡片骨架 */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(4, 1fr)',
          },
          gap: 2,
          mb: 3,
        }}
      >
        {Array.from({ length: 4 }).map((_, index) => (
          <Box
            key={index}
            sx={{
              p: 2,
              bgcolor: 'background.paper',
              borderRadius: 1,
              border: `1px solid ${useTheme().palette.divider}`,
            }}
          >
            <Box
              sx={{
                height: 48,
                width: '60%',
                bgcolor: 'action.hover',
                borderRadius: 1,
                mb: 1,
              }}
            />
            <Box
              sx={{
                height: 16,
                width: '80%',
                bgcolor: 'action.hover',
                borderRadius: 1,
              }}
            />
          </Box>
        ))}
      </Box>

      {/* 图表和列表骨架 */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', md: '2fr 1fr' },
          gap: 3,
        }}
      >
        {/* 图表骨架 */}
        <Box
          sx={{
            p: 2,
            bgcolor: 'background.paper',
            borderRadius: 1,
            border: `1px solid ${useTheme().palette.divider}`,
          }}
        >
          <Box
            sx={{
              height: 24,
              width: '30%',
              bgcolor: 'action.hover',
              borderRadius: 1,
              mb: 2,
            }}
          />
          <Box
            sx={{
              height: 300,
              bgcolor: 'action.hover',
              borderRadius: 1,
            }}
          />
        </Box>

        {/* 列表骨架 */}
        <Box
          sx={{
            p: 2,
            bgcolor: 'background.paper',
            borderRadius: 1,
            border: `1px solid ${useTheme().palette.divider}`,
          }}
        >
          <Box
            sx={{
              height: 24,
              width: '50%',
              bgcolor: 'action.hover',
              borderRadius: 1,
              mb: 2,
            }}
          />
          {Array.from({ length: 6 }).map((_, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mb: 2,
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  bgcolor: 'action.hover',
                  borderRadius: '50%',
                }}
              />
              <Box sx={{ flex: 1 }}>
                <Box
                  sx={{
                    height: 16,
                    width: '70%',
                    bgcolor: 'action.hover',
                    borderRadius: 1,
                    mb: 0.5,
                  }}
                />
                <Box
                  sx={{
                    height: 12,
                    width: '50%',
                    bgcolor: 'action.hover',
                    borderRadius: 1,
                  }}
                />
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}

// 简化的全页面加载器 - 用于快速加载场景
export function MinimalPageLoader() {
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {/* 简单的加载动画 */}
      <Box
        sx={{
          width: 40,
          height: 40,
          border: '3px solid',
          borderColor: 'action.hover',
          borderTopColor: 'primary.main',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          '@keyframes spin': {
            '0%': {
              transform: 'rotate(0deg)',
            },
            '100%': {
              transform: 'rotate(360deg)',
            },
          },
        }}
      />
    </Box>
  );
}

// 应用启动加载器 - 用于应用初始化
export function AppInitLoader() {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 3,
      }}
    >
      {/* Logo 或品牌区域 */}
      <Box
        sx={{
          width: 80,
          height: 80,
          bgcolor: 'primary.main',
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'primary.contrastText',
          fontSize: '2rem',
          fontWeight: 'bold',
        }}
      >
        T3
      </Box>

      {/* 加载进度条 */}
      <Box sx={{ width: 200 }}>
        <Box
          sx={{
            height: 4,
            bgcolor: 'action.hover',
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              height: '100%',
              bgcolor: 'primary.main',
              borderRadius: 2,
              animation: 'progress 2s ease-in-out infinite',
              '@keyframes progress': {
                '0%': {
                  width: '0%',
                  marginLeft: '0%',
                },
                '50%': {
                  width: '75%',
                  marginLeft: '0%',
                },
                '100%': {
                  width: '0%',
                  marginLeft: '100%',
                },
              },
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}
