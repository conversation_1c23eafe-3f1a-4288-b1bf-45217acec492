import { documentRouter } from "~/server/api/routers/document";
import { documentTypeRouter } from "~/server/api/routers/document-type";
import { tenantRouter } from "~/server/api/routers/tenant";
import { notificationRouter } from "~/server/api/routers/notification";
import { customFieldRouter } from "~/server/api/routers/custom-field";
import { systemRouter } from "~/server/api/routers/system";
import { merchantRouter } from "~/server/api/routers/merchant";
import { subscriptionRouter } from "~/server/api/routers/subscription";
import { notificationConfigRouter } from "~/server/api/routers/notification-config";
import { systemNotificationRouter } from "~/server/api/routers/system-notification";
import { userRouter } from "~/server/api/routers/user";
import { adminPlanRouter } from "~/server/api/routers/admin-plan";
import { adminOrderRouter } from "~/server/api/routers/admin-order";
import { adminTenantRouter } from "~/server/api/routers/admin-tenant";
import { orderRouter } from "~/server/api/routers/order";
import { systemConfigRouter } from "~/server/api/routers/system-config";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  document: documentRouter,
  documentType: documentTypeRouter,
  tenant: tenantRouter,
  notification: notificationRouter,
  customField: customFieldRouter,
  system: systemRouter,
  merchant: merchantRouter,
  subscription: subscriptionRouter,
  notificationConfig: notificationConfigRouter,
  systemNotification: systemNotificationRouter,
  adminPlan: adminPlanRouter,
  adminOrder: adminOrderRouter,
  adminTenant: adminTenantRouter,
  order: orderRouter,
  user: userRouter,
  systemConfig: systemConfigRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
