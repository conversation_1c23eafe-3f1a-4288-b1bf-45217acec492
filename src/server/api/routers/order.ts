import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 创建订单输入验证
const CreateOrderSchema = z.object({
  tenantId: z.string().min(1),
  planId: z.string().min(1),
  amount: z.number().min(0),
  currency: z.string().default("USD"),
  paymentMethod: z.string().min(1, "请选择付款方式"),
  paymentProofUrl: z.string().optional(),
});

// 上传付款凭证验证
const UploadPaymentProofSchema = z.object({
  orderId: z.string().min(1),
  paymentProofUrl: z.string().url("请提供有效的付款凭证URL"),
});

// 生成订单号
function generateOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `ORD${timestamp}${random}`;
}

export const orderRouter = createTRPCRouter({
  // 创建订单（商户）
  create: protectedProcedure
    .input(CreateOrderSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限（只有商户管理员可以创建订单）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以创建订单",
        });
      }

      // 检查套餐是否存在
      const plan = await ctx.db.subscriptionPlan.findUnique({
        where: { id: input.planId },
      });

      if (!plan || !plan.isActive) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "套餐不存在或已禁用",
        });
      }

      // 检查是否有未完成的订单
      const existingOrder = await ctx.db.order.findFirst({
        where: {
          tenantId: input.tenantId,
          status: { in: ["PENDING"] },
        },
      });

      if (existingOrder) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "您有未完成的订单，请先处理完成",
        });
      }

      // 创建订单
      const order = await ctx.db.order.create({
        data: {
          orderNumber: generateOrderNumber(),
          tenantId: input.tenantId,
          planId: input.planId,
          amount: input.amount,
          currency: input.currency,
          status: "PENDING",
          paymentMethod: input.paymentMethod,
          paymentProofUrl: input.paymentProofUrl,
        },
        include: {
          plan: true,
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 记录操作日志
      await ctx.db.orderLog.create({
        data: {
          orderId: order.id,
          action: "订单创建",
          note: `创建${plan.name}订单，金额：${plan.currency} ${plan.price}`,
          operatorId: ctx.session.user.id,
        },
      });

      return {
        success: true,
        order,
        message: "订单创建成功",
      };
    }),

  // 上传付款凭证（商户）
  uploadPaymentProof: protectedProcedure
    .input(UploadPaymentProofSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查订单是否存在且属于当前用户的商户
      const order = await ctx.db.order.findFirst({
        where: {
          id: input.orderId,
          tenant: {
            memberships: {
              some: {
                userId: ctx.session.user.id,
                role: "TENANT_ADMIN",
              },
            },
          },
        },
        include: {
          tenant: true,
          plan: true,
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在或您没有权限操作",
        });
      }

      if (order.status !== "PENDING") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只能为待付款订单上传付款凭证",
        });
      }

      // 更新订单状态和付款凭证
      const updatedOrder = await ctx.db.order.update({
        where: { id: input.orderId },
        data: {
          paymentProofUrl: input.paymentProofUrl,
          status: "PENDING", // 保持待审核状态
          updatedAt: new Date(),
        },
        include: {
          plan: true,
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 记录操作日志
      await ctx.db.orderLog.create({
        data: {
          orderId: order.id,
          action: "上传付款凭证",
          note: "商户已上传付款凭证，等待管理员审核",
          operatorId: ctx.session.user.id,
        },
      });

      return {
        success: true,
        order: updatedOrder,
        message: "付款凭证上传成功，等待管理员审核",
      };
    }),

  // 获取商户订单列表
  getByTenant: protectedProcedure
    .input(z.object({
      tenantId: z.string().min(1),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
      status: z.enum(["PENDING", "PAID", "FAILED", "CANCELLED", "REFUNDED"]).optional(),
    }))
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该商户的订单",
        });
      }

      const { page, limit, status } = input;
      const skip = (page - 1) * limit;

      const where: any = {
        tenantId: input.tenantId,
      };

      if (status) {
        where.status = status;
      }

      const [orders, total] = await Promise.all([
        ctx.db.order.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                currency: true,
                billingCycle: true,
              },
            },
            logs: {
              orderBy: { createdAt: "desc" },
              take: 1,
            },
          },
        }),
        ctx.db.order.count({ where }),
      ]);

      return {
        orders,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // 获取订单详情
  getById: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      const order = await ctx.db.order.findFirst({
        where: {
          id: input.id,
          tenant: {
            memberships: {
              some: {
                userId: ctx.session.user.id,
              },
            },
          },
        },
        include: {
          plan: true,
          tenant: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          logs: {
            orderBy: { createdAt: "desc" },
          },
          payments: {
            orderBy: { createdAt: "desc" },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在或您没有权限查看",
        });
      }

      return order;
    }),

  // 取消订单（商户）
  cancel: protectedProcedure
    .input(z.object({
      orderId: z.string().min(1),
      reason: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查订单是否存在且属于当前用户的商户
      const order = await ctx.db.order.findFirst({
        where: {
          id: input.orderId,
          tenant: {
            memberships: {
              some: {
                userId: ctx.session.user.id,
                role: "TENANT_ADMIN",
              },
            },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在或您没有权限操作",
        });
      }

      if (order.status !== "PENDING") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只能取消待付款的订单",
        });
      }

      // 更新订单状态
      const updatedOrder = await ctx.db.order.update({
        where: { id: input.orderId },
        data: {
          status: "CANCELLED",
          updatedAt: new Date(),
        },
      });

      // 记录操作日志
      await ctx.db.orderLog.create({
        data: {
          orderId: order.id,
          action: "订单取消",
          note: input.reason || "商户主动取消订单",
          operatorId: ctx.session.user.id,
        },
      });

      return {
        success: true,
        order: updatedOrder,
        message: "订单已取消",
      };
    }),
});
