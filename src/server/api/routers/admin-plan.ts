import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 套餐创建/更新的验证模式
const PlanSchema = z.object({
  name: z.string().min(1, "套餐名称不能为空"),
  description: z.string().optional(),
  price: z.number().min(0, "价格不能为负数"),
  currency: z.string().default("USD"),
  billingCycle: z.enum(["MONTHLY", "YEARLY", "TRIAL"]),
  features: z.array(z.string()).default([]),
  limits: z.object({
    maxDocuments: z.number().min(-1).default(-1), // -1 表示无限制
    maxUsers: z.number().min(-1).default(-1),
    maxStorage: z.number().min(-1).default(-1), // MB
    maxNotifications: z.number().min(-1).default(-1),
  }).default({}),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
});

const UpdatePlanSchema = PlanSchema.extend({
  id: z.string().min(1),
});

export const adminPlanRouter = createTRPCRouter({
  // 获取公开套餐列表（用于商户升级选择）
  getPublicPlans: publicProcedure
    .query(async ({ ctx }) => {
      const plans = await ctx.db.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: [
          { sortOrder: "asc" },
          { createdAt: "desc" },
        ],
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          currency: true,
          billingCycle: true,
          features: true,
          limits: true,
          sortOrder: true,
        },
      });

      return plans.map(plan => ({
        ...plan,
        features: plan.features as string[],
        limits: plan.limits as any,
      }));
    }),

  // 获取所有套餐（超级管理员）
  getAll: protectedProcedure
    .query(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以管理套餐",
        });
      }

      const plans = await ctx.db.subscriptionPlan.findMany({
        orderBy: [
          { sortOrder: "asc" },
          { createdAt: "desc" },
        ],
        include: {
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      });

      return plans.map(plan => ({
        ...plan,
        features: plan.features as string[],
        limits: plan.limits as any,
        subscriptionCount: plan._count.subscriptions,
      }));
    }),

  // 创建套餐（超级管理员）
  create: protectedProcedure
    .input(PlanSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以创建套餐",
        });
      }

      // 检查套餐名称是否已存在
      const existingPlan = await ctx.db.subscriptionPlan.findFirst({
        where: { name: input.name },
      });

      if (existingPlan) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "套餐名称已存在",
        });
      }

      const plan = await ctx.db.subscriptionPlan.create({
        data: {
          ...input,
          features: input.features,
          limits: input.limits,
        },
      });

      return {
        success: true,
        plan,
        message: "套餐创建成功",
      };
    }),

  // 更新套餐（超级管理员）
  update: protectedProcedure
    .input(UpdatePlanSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新套餐",
        });
      }

      // 检查套餐是否存在
      const existingPlan = await ctx.db.subscriptionPlan.findUnique({
        where: { id: input.id },
      });

      if (!existingPlan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "套餐不存在",
        });
      }

      // 检查套餐名称是否与其他套餐冲突
      const conflictPlan = await ctx.db.subscriptionPlan.findFirst({
        where: {
          name: input.name,
          id: { not: input.id },
        },
      });

      if (conflictPlan) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "套餐名称已存在",
        });
      }

      const { id, ...updateData } = input;
      const plan = await ctx.db.subscriptionPlan.update({
        where: { id },
        data: {
          ...updateData,
          features: updateData.features,
          limits: updateData.limits,
        },
      });

      return {
        success: true,
        plan,
        message: "套餐更新成功",
      };
    }),

  // 删除套餐（超级管理员）
  delete: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以删除套餐",
        });
      }

      // 检查套餐是否存在
      const existingPlan = await ctx.db.subscriptionPlan.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      });

      if (!existingPlan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "套餐不存在",
        });
      }

      // 检查是否有活跃的订阅
      if (existingPlan._count.subscriptions > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "无法删除有活跃订阅的套餐，请先处理相关订阅",
        });
      }

      await ctx.db.subscriptionPlan.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: "套餐删除成功",
      };
    }),

  // 切换套餐状态（超级管理员）
  toggleStatus: protectedProcedure
    .input(z.object({ 
      id: z.string().min(1),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以管理套餐状态",
        });
      }

      const plan = await ctx.db.subscriptionPlan.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
      });

      return {
        success: true,
        plan,
        message: `套餐已${input.isActive ? '启用' : '禁用'}`,
      };
    }),

  // 获取套餐统计信息（超级管理员）
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看套餐统计",
        });
      }

      const [totalPlans, activePlans, totalSubscriptions] = await Promise.all([
        ctx.db.subscriptionPlan.count(),
        ctx.db.subscriptionPlan.count({ where: { isActive: true } }),
        ctx.db.subscription.count({ where: { status: "ACTIVE" } }),
      ]);

      // 获取每个套餐的订阅数量
      const planSubscriptions = await ctx.db.subscriptionPlan.findMany({
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              subscriptions: {
                where: { status: "ACTIVE" },
              },
            },
          },
        },
      });

      return {
        totalPlans,
        activePlans,
        totalSubscriptions,
        planSubscriptions: planSubscriptions.map(plan => ({
          planId: plan.id,
          planName: plan.name,
          subscriptionCount: plan._count.subscriptions,
        })),
      };
    }),
});
