import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import webpush from 'web-push';

// 系统通知配置验证
const SystemNotificationConfigSchema = z.object({
  // 通知方式开关
  emailEnabled: z.boolean().default(true),
  telegramEnabled: z.boolean().default(true),
  pushEnabled: z.boolean().default(true),
  browserNotificationEnabled: z.boolean().default(true),

  // Telegram Bot配置
  telegramBotToken: z.string().nullable().optional(),
  telegramBotName: z.string().nullable().optional(),

  // 邮件配置
  emailProvider: z.enum(["SMTP", "GMAIL", "OUTLOOK", "SENDGRID", "AWS_SES", "MAILGUN"]).nullable().optional(),
  emailApiKey: z.string().nullable().optional(),
  emailFromAddress: z.string().nullable().optional().refine((val) => {
    // 如果有值，必须是有效的邮箱地址
    return !val || z.string().email().safeParse(val).success;
  }, { message: "邮箱地址格式不正确" }),
  emailFromName: z.string().nullable().optional(),
  smtpHost: z.string().nullable().optional(),
  smtpPort: z.number().min(1).max(65535).nullable().optional(),
  smtpUser: z.string().nullable().optional(),
  smtpPassword: z.string().nullable().optional(),
  smtpSecure: z.boolean().default(true),

  // PWA推送配置
  vapidPublicKey: z.string().nullable().optional(),
  vapidPrivateKey: z.string().nullable().optional(),
  vapidSubject: z.string().nullable().optional(),

  // 系统推送配置
  systemPushEnabled: z.boolean().default(true),
  expiryPushEnabled: z.boolean().default(true),
});

// 商户通知配置验证
const TenantNotificationConfigSchema = z.object({
  tenantId: z.string(),
  telegramChatId: z.string().optional(),
  reminderDays: z.array(z.number()).optional(),
  emailEnabled: z.boolean().optional(),
  telegramEnabled: z.boolean().optional(),
  pushEnabled: z.boolean().optional(),
});

export const systemNotificationRouter = createTRPCRouter({
  // 获取系统通知配置（超级管理员）
  getSystemConfig: protectedProcedure
    .query(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以访问系统通知配置",
        });
      }

      // 获取系统配置
      const config = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      return config || {
        // 通知方式开关
        emailEnabled: true,
        telegramEnabled: true,
        pushEnabled: true,
        browserNotificationEnabled: true,

        // Telegram配置
        telegramBotToken: null,
        telegramBotName: null,

        // 邮件配置
        emailProvider: null,
        emailApiKey: null,
        emailFromAddress: null,
        emailFromName: null,
        smtpHost: null,
        smtpPort: null,
        smtpUser: null,
        smtpPassword: null,
        smtpSecure: true,

        // PWA推送配置
        vapidPublicKey: null,
        vapidPrivateKey: null,
        vapidSubject: null,

        // 系统推送配置
        systemPushEnabled: true,
        expiryPushEnabled: true,
      };
    }),

  // 更新系统通知配置（超级管理员）
  updateSystemConfig: protectedProcedure
    .input(SystemNotificationConfigSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新系统通知配置",
        });
      }

      // 查找现有配置
      const existingConfig = await ctx.db.systemNotificationConfig.findFirst();

      // 处理空字符串，转换为 null
      const processedInput = Object.fromEntries(
        Object.entries(input).map(([key, value]) => [
          key,
          value === "" ? null : value
        ])
      );

      let config;
      if (existingConfig) {
        // 更新现有配置
        config = await ctx.db.systemNotificationConfig.update({
          where: { id: existingConfig.id },
          data: processedInput,
        });
      } else {
        // 创建新配置
        config = await ctx.db.systemNotificationConfig.create({
          data: processedInput,
        });
      }

      return {
        success: true,
        config,
        message: "系统通知配置更新成功",
      };
    }),

  // 获取所有商户的通知配置（超级管理员）
  getAllTenantConfigs: protectedProcedure
    .query(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看所有商户配置",
        });
      }

      const tenants = await ctx.db.tenant.findMany({
        select: {
          id: true,
          name: true,
          type: true,
          telegramChatId: true,
          notificationSettings: true,
          isActive: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
      });

      return tenants.map(tenant => ({
        ...tenant,
        notificationSettings: tenant.notificationSettings as any || {
          reminderDays: [30, 7, 1],
          emailEnabled: true,
          telegramEnabled: false,
          pushEnabled: true,
        },
      }));
    }),

  // 更新商户通知配置（超级管理员）
  updateTenantConfig: protectedProcedure
    .input(TenantNotificationConfigSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新商户通知配置",
        });
      }

      const { tenantId, telegramChatId, ...notificationSettings } = input;

      // 获取现有配置
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: tenantId },
        select: { notificationSettings: true },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      // 合并配置
      const currentSettings = (tenant.notificationSettings as any) || {};
      const updatedSettings = {
        ...currentSettings,
        ...notificationSettings,
      };

      // 更新商户配置
      await ctx.db.tenant.update({
        where: { id: tenantId },
        data: {
          telegramChatId,
          notificationSettings: updatedSettings,
        },
      });

      return {
        success: true,
        message: "商户通知配置更新成功",
      };
    }),

  // 测试Telegram Bot连接
  testTelegramBot: protectedProcedure
    .input(z.object({
      botToken: z.string(),
      chatId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以测试Telegram Bot",
        });
      }

      try {
        // 测试发送消息到Telegram
        const url = `https://api.telegram.org/bot${input.botToken}/sendMessage`;
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            chat_id: input.chatId,
            text: '🤖 这是一条测试消息，证明Telegram Bot配置正确！',
          }),
        });

        const result = await response.json();

        if (result.ok) {
          return {
            success: true,
            message: "Telegram Bot测试成功",
            data: result,
          };
        } else {
          return {
            success: false,
            message: `Telegram Bot测试失败: ${result.description}`,
            error: result,
          };
        }
      } catch (error) {
        return {
          success: false,
          message: `Telegram Bot测试失败: ${error}`,
          error: error,
        };
      }
    }),

  // 获取通知发送统计
  getNotificationStats: protectedProcedure
    .input(z.object({
      tenantId: z.string().optional(),
      days: z.number().default(30),
    }))
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看通知统计",
        });
      }

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - input.days);

      const where: any = {
        createdAt: { gte: startDate },
      };

      if (input.tenantId) {
        where.tenantId = input.tenantId;
      }

      const [totalNotifications, emailSent, telegramSent, pushSent] = await Promise.all([
        ctx.db.notification.count({ where }),
        ctx.db.notification.count({ where: { ...where, emailSent: true } }),
        ctx.db.notification.count({ where: { ...where, telegramSent: true } }),
        ctx.db.notification.count({ where: { ...where, pushSent: true } }),
      ]);

      return {
        totalNotifications,
        emailSent,
        telegramSent,
        pushSent,
        period: `${input.days}天`,
      };
    }),

  // 获取可用的通知方式（公开接口）
  getAvailableNotificationMethods: protectedProcedure
    .query(async ({ ctx }) => {
      // 获取系统配置
      const config = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      const defaultConfig = {
        emailEnabled: true,
        telegramEnabled: true,
        pushEnabled: true,
      };

      const systemConfig = config || defaultConfig;

      return {
        email: systemConfig.emailEnabled,
        telegram: systemConfig.telegramEnabled,
        push: systemConfig.pushEnabled,
      };
    }),

  // 测试通知配置
  testNotificationConfig: protectedProcedure
    .input(z.object({
      type: z.enum(["EMAIL", "TELEGRAM", "PUSH"]),
      testData: z.object({
        email: z.string().email().optional(),
        telegramChatId: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true, email: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以测试通知配置",
        });
      }

      // 获取系统配置
      const config = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      if (!config) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "系统通知配置不存在",
        });
      }

      try {
        switch (input.type) {
          case "EMAIL":
            if (!config.emailEnabled) {
              throw new Error("邮件通知已被禁用");
            }

            if (!config.emailFromAddress) {
              throw new Error("邮件发送地址未配置");
            }

            const testEmail = input.testData?.email || user.email;
            if (!testEmail) {
              throw new Error("测试邮箱地址未提供");
            }

            // 实际发送测试邮件
            await sendTestEmail(config, testEmail);
            break;

          case "TELEGRAM":
            if (!config.telegramEnabled) {
              throw new Error("Telegram通知已被禁用");
            }
            // TODO: 实际发送测试Telegram消息
            console.log("测试Telegram发送:", input.testData?.telegramChatId);
            break;

          case "PUSH":
            if (!config.pushEnabled) {
              throw new Error("推送通知已被禁用");
            }
            // TODO: 实际发送测试推送
            console.log("测试推送发送");
            break;
        }

        return {
          success: true,
          message: `${input.type}通知测试成功`,
        };
      } catch (error) {
        return {
          success: false,
          message: error instanceof Error ? error.message : "测试失败",
        };
      }
    }),

  // 生成VAPID密钥对（超级管理员）
  generateVapidKeys: protectedProcedure
    .mutation(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以生成VAPID密钥",
        });
      }

      try {
        // 生成VAPID密钥对
        const vapidKeys = webpush.generateVAPIDKeys();

        return {
          success: true,
          publicKey: vapidKeys.publicKey,
          privateKey: vapidKeys.privateKey,
          message: "VAPID密钥生成成功",
        };
      } catch (error) {
        console.error('生成VAPID密钥失败:', error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "生成VAPID密钥失败",
        });
      }
    }),
});

// 发送测试邮件的辅助函数
async function sendTestEmail(config: any, email: string) {
  const nodemailer = await import('nodemailer');

  let transporter;

  switch (config.emailProvider) {
    case "GMAIL":
      transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: config.smtpUser || config.emailFromAddress,
          pass: config.smtpPassword, // 应用专用密码
        },
      });
      break;

    case "OUTLOOK":
      transporter = nodemailer.createTransport({
        host: 'smtp-mail.outlook.com',
        port: 587,
        secure: false,
        auth: {
          user: config.smtpUser || config.emailFromAddress,
          pass: config.smtpPassword,
        },
      });
      break;

    case "SMTP":
      transporter = nodemailer.createTransport({
        host: config.smtpHost,
        port: config.smtpPort || 587,
        secure: config.smtpSecure || false,
        auth: {
          user: config.smtpUser,
          pass: config.smtpPassword,
        },
      });
      break;

    case "SENDGRID":
      // TODO: 实现 SendGrid API 发送
      throw new Error("SendGrid 邮件发送功能暂未实现，请使用 SMTP 方式");

    default:
      throw new Error(`不支持的邮件服务商: ${config.emailProvider}`);
  }

  // 发送邮件
  await transporter.sendMail({
    from: `"${config.emailFromName || '证件提醒系统'}" <${config.emailFromAddress}>`,
    to: email,
    subject: '证件提醒系统 - 测试邮件',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1976d2;">📧 邮件配置测试</h2>
        <p>这是一封来自证件提醒系统的测试邮件。</p>
        <p>如果您收到这封邮件，说明邮件配置已经成功！</p>
        <hr style="border: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          发送时间: ${new Date().toLocaleString('zh-CN')}<br>
          邮件服务商: ${config.emailProvider}
        </p>
      </div>
    `,
  });

  console.log(`📧 ${config.emailProvider}测试邮件已发送到 ${email}`);
}
