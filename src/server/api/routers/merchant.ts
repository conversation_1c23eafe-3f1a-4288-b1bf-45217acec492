import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

// 商户升级输入验证
const UpgradeTenantSchema = z.object({
  name: z.string().min(1, "商户名称不能为空"),
  type: z.enum(["PERSONAL", "ENTERPRISE"]),
  description: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  address: z.string().optional(),
  website: z.string().url().optional(),
});

// 商户信息更新验证
const UpdateTenantSchema = z.object({
  tenantId: z.string().min(1),
  name: z.string().min(1, "商户名称不能为空").optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  address: z.string().optional(),
  website: z.string().url().optional(),
  settings: z.record(z.any()).optional(),
});

// 商户用户邀请验证
const InviteUserSchema = z.object({
  tenantId: z.string().min(1),
  email: z.string().email("请输入有效的邮箱地址"),
  role: z.enum(["TENANT_ADMIN", "TENANT_MEMBER"]),
  name: z.string().optional(),
});

// 订阅计划验证
const SubscriptionSchema = z.object({
  tenantId: z.string().min(1),
  plan: z.enum(["FREE", "PERSONAL", "ENTERPRISE"]),
  paymentMethod: z.string().optional(),
  proofUrl: z.string().optional(),
});

export const merchantRouter = createTRPCRouter({
  // 升级商户类型
  upgrade: protectedProcedure
    .input(z.object({
      tenantId: z.string().min(1),
      type: z.enum(["PERSONAL", "ENTERPRISE"]),
    }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限（只有管理员可以升级）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以升级商户类型",
        });
      }

      // 获取当前商户信息
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: input.tenantId },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      if (tenant.type !== "FREE") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有免费用户可以升级",
        });
      }

      // 更新商户类型
      const updatedTenant = await ctx.db.tenant.update({
        where: { id: input.tenantId },
        data: {
          type: input.type,
        },
      });

      return {
        success: true,
        tenant: updatedTenant,
        message: `成功升级为${input.type === 'ENTERPRISE' ? '企业商户' : '个人商户'}`,
      };
    }),

  // 创建新商户（原upgrade功能重命名）
  createMerchant: protectedProcedure
    .input(UpgradeTenantSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // 检查用户是否已经有商户
      const existingMembership = await ctx.db.membership.findFirst({
        where: { userId },
        include: { tenant: true },
      });

      if (existingMembership) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "您已经是商户成员，无法重复升级",
        });
      }

      // 创建商户
      const tenant = await ctx.db.tenant.create({
        data: {
          name: input.name,
          type: input.type,
          description: input.description,
          phone: input.phone,
          email: input.email,
          address: input.address,
          website: input.website,
          settings: {
            notifications: {
              email: true,
              push: true,
              telegram: false,
            },
            reminders: {
              days: [30, 7, 1], // 提前30天、7天、1天提醒
              enabled: true,
            },
          },
        },
      });

      // 创建成员关系，设置为管理员
      await ctx.db.membership.create({
        data: {
          tenantId: tenant.id,
          userId,
          role: "TENANT_ADMIN",
        },
      });

      // 更新用户状态和当前租户
      await ctx.db.user.update({
        where: { id: userId },
        data: {
          status: "TRIAL", // 升级后给予试用期
          currentTenantId: tenant.id,
          role: "TENANT_ADMIN",
        },
      });

      // 创建试用订阅
      const trialEndDate = new Date();
      trialEndDate.setDate(trialEndDate.getDate() + 30); // 30天试用期

      // 先查找对应的套餐
      const planName = input.type === "ENTERPRISE" ? "企业版" : "个人版";
      const subscriptionPlan = await ctx.db.subscriptionPlan.findFirst({
        where: { name: planName }
      });

      if (!subscriptionPlan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "未找到对应的订阅套餐",
        });
      }

      await ctx.db.subscription.create({
        data: {
          tenantId: tenant.id,
          planId: subscriptionPlan.id,
          status: "TRIAL",
          startDate: new Date(),
          trialEndDate,
          features: getSubscriptionFeatures(input.type === "ENTERPRISE" ? "ENTERPRISE" : "PERSONAL"),
          limits: getSubscriptionLimits(input.type === "ENTERPRISE" ? "ENTERPRISE" : "PERSONAL"),
        },
      });

      return {
        success: true,
        tenant,
        message: "商户创建成功，您已获得30天试用期",
      };
    }),

  // 获取商户信息
  getInfo: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该商户",
        });
      }

      const tenant = await ctx.db.tenant.findUnique({
        where: { id: input.tenantId },
        include: {
          subscriptions: {
            where: { status: { in: ["TRIAL", "ACTIVE"] } },
            orderBy: { createdAt: "desc" },
            take: 1,
          },
          memberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                  createdAt: true,
                },
              },
            },
          },
          _count: {
            select: {
              documents: true,
              documentTypes: true,
              notifications: true,
            },
          },
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      return {
        ...tenant,
        currentSubscription: tenant.subscriptions[0] || null,
      };
    }),

  // 更新商户信息
  updateInfo: protectedProcedure
    .input(UpdateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      const { tenantId, ...updateData } = input;

      // 验证用户权限（只有管理员可以更新）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以更新商户信息",
        });
      }

      const updatedTenant = await ctx.db.tenant.update({
        where: { id: tenantId },
        data: updateData,
      });

      return {
        success: true,
        tenant: updatedTenant,
        message: "商户信息更新成功",
      };
    }),

  // 邀请用户加入商户
  inviteUser: protectedProcedure
    .input(InviteUserSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限（只有管理员可以邀请）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以邀请用户",
        });
      }

      // 检查商户类型限制
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: input.tenantId },
        include: {
          memberships: true,
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      if ((tenant.type === "PERSONAL" || tenant.type === "FREE") && tenant.memberships.length >= 1) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: tenant.type === "FREE" ? "免费用户只能有一个成员，请先升级" : "个人商户只能有一个成员",
        });
      }

      // 查找或创建用户
      let user = await ctx.db.user.findUnique({
        where: { email: input.email },
      });

      if (!user) {
        // 创建新用户
        user = await ctx.db.user.create({
          data: {
            email: input.email,
            name: input.name,
            role: input.role,
            status: "FREE",
          },
        });
      }

      // 检查用户是否已经是成员
      const existingMembership = await ctx.db.membership.findFirst({
        where: {
          userId: user.id,
          tenantId: input.tenantId,
        },
      });

      if (existingMembership) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该用户已经是商户成员",
        });
      }

      // 创建成员关系
      await ctx.db.membership.create({
        data: {
          tenantId: input.tenantId,
          userId: user.id,
          role: input.role,
        },
      });

      // 如果用户是新创建的，发送邀请邮件（这里可以集成邮件服务）
      // TODO: 发送邀请邮件

      return {
        success: true,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: input.role,
        },
        message: "用户邀请成功",
      };
    }),

  // 移除商户用户
  removeUser: protectedProcedure
    .input(z.object({
      tenantId: z.string().min(1),
      userId: z.string().min(1),
    }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限（只有管理员可以移除用户）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以移除用户",
        });
      }

      // 不能移除自己
      if (input.userId === ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "不能移除自己",
        });
      }

      // 删除成员关系
      await ctx.db.membership.delete({
        where: {
          tenantId_userId: {
            tenantId: input.tenantId,
            userId: input.userId,
          },
        },
      });

      return {
        success: true,
        message: "用户移除成功",
      };
    }),
});

// 获取订阅功能特性
function getSubscriptionFeatures(plan: string) {
  const features = {
    FREE: {
      documents: 10,
      users: 1,
      notifications: true,
      customFields: false,
      api: false,
      support: "community",
    },
    PERSONAL: {
      documents: 100,
      users: 1,
      notifications: true,
      customFields: true,
      api: false,
      support: "email",
    },
    ENTERPRISE: {
      documents: -1, // 无限制
      users: -1, // 无限制
      notifications: true,
      customFields: true,
      api: true,
      support: "priority",
    },
  };

  return features[plan as keyof typeof features] || features.FREE;
}

// 获取订阅限制
function getSubscriptionLimits(plan: string) {
  const limits = {
    FREE: {
      maxDocuments: 10,
      maxUsers: 1,
      maxCustomFields: 0,
      maxNotifications: 50,
    },
    PERSONAL: {
      maxDocuments: 100,
      maxUsers: 1,
      maxCustomFields: 20,
      maxNotifications: 500,
    },
    ENTERPRISE: {
      maxDocuments: -1,
      maxUsers: -1,
      maxCustomFields: -1,
      maxNotifications: -1,
    },
  };

  return limits[plan as keyof typeof limits] || limits.FREE;
}
