import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";

// 订阅计划配置
const SUBSCRIPTION_PLANS = {
  FREE: {
    name: "免费试用",
    price: 0,
    duration: 90, // 90天试用期（3个月）
    currency: "USD",
    billingCycle: "TRIAL",
    features: {
      documents: 10,
      users: 1,
      customFields: 5,
      notifications: 50,
      support: "社区支持",
      telegram: true,
      pwa: true,
      export: false,
    },
    limits: {
      maxDocuments: 10,
      maxUsers: 1,
      maxCustomFields: 5,
      maxNotifications: 50,
    },
  },
  PERSONAL: {
    name: "个人商业版",
    price: 118,
    duration: 365, // 365天（1年）
    currency: "USD",
    billingCycle: "YEARLY",
    features: {
      documents: 1000,
      users: 1,
      customFields: 50,
      notifications: 2000,
      support: "邮件支持",
      telegram: true,
      pwa: true,
      export: true,
      api: false,
    },
    limits: {
      maxDocuments: 1000,
      maxUsers: 1,
      maxCustomFields: 50,
      maxNotifications: 2000,
    },
  },
  ENTERPRISE: {
    name: "企业商业版",
    price: 288,
    duration: 365, // 365天（1年）
    currency: "USD",
    billingCycle: "YEARLY",
    features: {
      documents: -1, // 无限制
      users: -1, // 无限制
      customFields: -1,
      notifications: -1,
      support: "优先技术支持",
      telegram: true,
      pwa: true,
      export: true,
      api: true,
      customBranding: true,
      advancedReports: true,
    },
    limits: {
      maxDocuments: -1,
      maxUsers: -1,
      maxCustomFields: -1,
      maxNotifications: -1,
    },
  },
};

// 订阅输入验证
const SubscribeSchema = z.object({
  tenantId: z.string().min(1),
  plan: z.enum(["PERSONAL", "ENTERPRISE"]),
  paymentMethod: z.string().optional(),
  proofUrl: z.string().optional(),
});

// 更新订阅状态验证
const UpdateSubscriptionSchema = z.object({
  subscriptionId: z.string().min(1),
  status: z.enum(["TRIAL", "ACTIVE", "EXPIRED", "PENDING", "CANCELLED"]),
  endDate: z.date().optional(),
  proofUrl: z.string().optional(),
});

export const subscriptionRouter = createTRPCRouter({
  // 获取公开的订阅计划列表（用于首页展示）
  getPublicPlans: publicProcedure.query(async () => {
    return Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => ({
      id: key,
      type: key,
      name: plan.name,
      price: plan.price,
      currency: plan.currency,
      billingCycle: plan.billingCycle,
      duration: plan.duration,
      // 格式化的价格显示
      priceDisplay: plan.price === 0
        ? "免费试用"
        : `$${plan.price}/${plan.billingCycle === 'YEARLY' ? '年' : plan.billingCycle === 'MONTHLY' ? '月' : '试用期'}`,
      // 功能列表（用于前端展示）
      featureList: [
        plan.limits.maxDocuments === -1 ? "无限证件管理" : `${plan.limits.maxDocuments}个证件`,
        plan.limits.maxUsers === -1 ? "无限用户" : `${plan.limits.maxUsers}个用户`,
        plan.limits.maxCustomFields === -1 ? "无限自定义字段" : `${plan.limits.maxCustomFields}个自定义字段`,
        plan.limits.maxNotifications === -1 ? "无限通知" : `${plan.limits.maxNotifications}条通知/月`,
        plan.features.telegram && "Telegram通知",
        plan.features.pwa && "PWA支持",
        plan.features.export && "数据导出",
        (plan.features as any).api && "API接口",
        (plan.features as any).customBranding && "自定义品牌",
        (plan.features as any).advancedReports && "高级报表",
        `${plan.features.support}`,
      ].filter(Boolean),
    }));
  }),

  // 获取订阅计划列表（需要登录）
  getPlans: protectedProcedure.query(async () => {
    return Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => ({
      id: key,
      type: key,
      name: plan.name,
      price: plan.price,
      currency: plan.currency,
      billingCycle: plan.billingCycle,
      duration: plan.duration,
      features: plan.features,
      limits: plan.limits,
      // 格式化的价格显示
      priceDisplay: plan.price === 0
        ? "免费"
        : `$${plan.price}/${plan.billingCycle === 'YEARLY' ? '年' : plan.billingCycle === 'MONTHLY' ? '月' : '试用期'}`,
      // 功能列表（用于前端展示）
      featureList: [
        plan.limits.maxDocuments === -1 ? "无限证件管理" : `${plan.limits.maxDocuments}个证件`,
        plan.limits.maxUsers === -1 ? "无限用户" : `${plan.limits.maxUsers}个用户`,
        plan.limits.maxCustomFields === -1 ? "无限自定义字段" : `${plan.limits.maxCustomFields}个自定义字段`,
        plan.limits.maxNotifications === -1 ? "无限通知" : `${plan.limits.maxNotifications}条通知/月`,
        plan.features.telegram && "Telegram通知",
        plan.features.pwa && "PWA支持",
        plan.features.export && "数据导出",
        (plan.features as any).api && "API接口",
        (plan.features as any).customBranding && "自定义品牌",
        (plan.features as any).advancedReports && "高级报表",
        `${plan.features.support}`,
      ].filter(Boolean),
    }));
  }),

  // 获取当前订阅状态
  getCurrent: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该商户",
        });
      }

      const subscription = await ctx.db.subscription.findFirst({
        where: {
          tenantId: input.tenantId,
          status: { in: ["TRIAL", "ACTIVE", "PENDING"] },
        },
        include: {
          plan: true,
        },
        orderBy: { createdAt: "desc" },
      });

      if (!subscription) {
        // 获取商户创建时间作为免费套餐开始时间
        const tenant = await ctx.db.tenant.findUnique({
          where: { id: input.tenantId },
          select: { createdAt: true },
        });

        const startDate = tenant?.createdAt || new Date();
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + SUBSCRIPTION_PLANS.FREE.duration); // 90天后到期

        // 计算剩余天数
        const now = new Date();
        const diffTime = endDate.getTime() - now.getTime();
        const daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // 返回免费版信息
        return {
          plan: "FREE",
          status: daysLeft > 0 ? "TRIAL" : "EXPIRED",
          features: SUBSCRIPTION_PLANS.FREE.features,
          limits: SUBSCRIPTION_PLANS.FREE.limits,
          startDate,
          endDate,
          trialEndDate: endDate,
          daysLeft: Math.max(0, daysLeft),
          planInfo: SUBSCRIPTION_PLANS.FREE,
        };
      }

      // 计算剩余天数
      let daysLeft = -1;
      if (subscription.status === "TRIAL" && subscription.trialEndDate) {
        const now = new Date();
        const diffTime = subscription.trialEndDate.getTime() - now.getTime();
        daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      } else if (subscription.endDate) {
        const now = new Date();
        const diffTime = subscription.endDate.getTime() - now.getTime();
        daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }

      return {
        ...subscription,
        daysLeft,
        planInfo: subscription.plan || SUBSCRIPTION_PLANS[subscription.plan as keyof typeof SUBSCRIPTION_PLANS],
      };
    }),

  // 订阅计划
  subscribe: protectedProcedure
    .input(SubscribeSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限（只有管理员可以订阅）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以订阅计划",
        });
      }

      const planConfig = SUBSCRIPTION_PLANS[input.plan];
      if (!planConfig) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "无效的订阅计划",
        });
      }

      // 检查是否有活跃的订阅
      const existingSubscription = await ctx.db.subscription.findFirst({
        where: {
          tenantId: input.tenantId,
          status: { in: ["TRIAL", "ACTIVE", "PENDING"] },
        },
      });

      if (existingSubscription) {
        // 取消现有订阅
        await ctx.db.subscription.update({
          where: { id: existingSubscription.id },
          data: { status: "CANCELLED" },
        });
      }

      // 查找对应的套餐
      const planName = input.plan === "ENTERPRISE" ? "企业版" : "个人版";
      const subscriptionPlan = await ctx.db.subscriptionPlan.findFirst({
        where: { name: planName }
      });

      if (!subscriptionPlan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "未找到对应的订阅套餐",
        });
      }

      // 创建新订阅
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + planConfig.duration);

      const subscription = await ctx.db.subscription.create({
        data: {
          tenantId: input.tenantId,
          planId: subscriptionPlan.id,
          status: input.proofUrl ? "PENDING" : "ACTIVE", // 有付款凭证则待审核
          startDate,
          endDate: planConfig.duration > 0 ? endDate : null,
          price: planConfig.price,
          currency: planConfig.currency,
          paymentMethod: input.paymentMethod,
          proofUrl: input.proofUrl,
          features: planConfig.features,
          limits: planConfig.limits,
        },
      });

      // 更新用户状态
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          status: input.proofUrl ? "TRIAL" : "SUBSCRIBED",
        },
      });

      return {
        success: true,
        subscription,
        message: input.proofUrl 
          ? "订阅申请已提交，等待审核" 
          : "订阅成功",
      };
    }),

  // 续费订阅
  renew: protectedProcedure
    .input(z.object({
      tenantId: z.string().min(1),
      paymentMethod: z.string().optional(),
      proofUrl: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以续费",
        });
      }

      // 获取当前订阅
      const currentSubscription = await ctx.db.subscription.findFirst({
        where: {
          tenantId: input.tenantId,
          status: { in: ["TRIAL", "ACTIVE", "EXPIRED"] },
        },
        orderBy: { createdAt: "desc" },
      });

      if (!currentSubscription) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "未找到可续费的订阅",
        });
      }

      const planConfig = SUBSCRIPTION_PLANS[currentSubscription.plan as keyof typeof SUBSCRIPTION_PLANS];
      
      // 计算新的结束时间
      const now = new Date();
      const currentEndDate = currentSubscription.endDate || now;
      const newEndDate = new Date(Math.max(now.getTime(), currentEndDate.getTime()));
      newEndDate.setDate(newEndDate.getDate() + planConfig.duration);

      // 更新订阅
      const updatedSubscription = await ctx.db.subscription.update({
        where: { id: currentSubscription.id },
        data: {
          status: input.proofUrl ? "PENDING" : "ACTIVE",
          endDate: newEndDate,
          paymentMethod: input.paymentMethod,
          proofUrl: input.proofUrl,
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        subscription: updatedSubscription,
        message: input.proofUrl 
          ? "续费申请已提交，等待审核" 
          : "续费成功",
      };
    }),

  // 取消订阅
  cancel: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: "TENANT_ADMIN",
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有商户管理员可以取消订阅",
        });
      }

      // 获取当前订阅
      const subscription = await ctx.db.subscription.findFirst({
        where: {
          tenantId: input.tenantId,
          status: { in: ["TRIAL", "ACTIVE", "PENDING"] },
        },
      });

      if (!subscription) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "未找到可取消的订阅",
        });
      }

      // 取消订阅
      await ctx.db.subscription.update({
        where: { id: subscription.id },
        data: { 
          status: "CANCELLED",
          autoRenew: false,
        },
      });

      // 更新用户状态
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: { status: "FREE" },
      });

      return {
        success: true,
        message: "订阅已取消",
      };
    }),

  // 获取使用统计
  getUsage: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该商户",
        });
      }

      // 获取使用统计
      const [documentsCount, usersCount, customFieldsCount, notificationsCount] = await Promise.all([
        ctx.db.document.count({ where: { tenantId: input.tenantId } }),
        ctx.db.membership.count({ where: { tenantId: input.tenantId } }),
        ctx.db.customFieldConfig.count({ 
          where: { tenantId: input.tenantId, isActive: true } 
        }),
        ctx.db.notification.count({ 
          where: { 
            tenantId: input.tenantId,
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // 本月
            },
          } 
        }),
      ]);

      return {
        documents: documentsCount,
        users: usersCount,
        customFields: customFieldsCount,
        notifications: notificationsCount,
      };
    }),

  // 管理员：更新订阅状态
  updateStatus: protectedProcedure
    .input(UpdateSubscriptionSchema)
    .mutation(async ({ ctx, input }) => {
      // 只有超级管理员可以更新订阅状态
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新订阅状态",
        });
      }

      const updatedSubscription = await ctx.db.subscription.update({
        where: { id: input.subscriptionId },
        data: {
          status: input.status,
          endDate: input.endDate,
          proofUrl: input.proofUrl,
        },
      });

      return {
        success: true,
        subscription: updatedSubscription,
        message: "订阅状态更新成功",
      };
    }),
});
