import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 订单查询参数
const GetOrdersSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  status: z.string().optional().transform(val => val === "" ? undefined : val).pipe(z.enum(["PENDING", "PAID", "FAILED", "CANCELLED", "REFUNDED"]).optional()),
  tenantId: z.string().optional().transform(val => val === "" ? undefined : val),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  search: z.string().optional().transform(val => val === "" ? undefined : val),
});

// 订单状态更新
const UpdateOrderStatusSchema = z.object({
  orderId: z.string().min(1),
  status: z.enum(["PENDING", "PAID", "FAILED", "CANCELLED", "REFUNDED"]),
  note: z.string().optional(),
});

// 审核订单
const ReviewOrderSchema = z.object({
  orderId: z.string().min(1),
  approved: z.boolean(),
  note: z.string().optional(),
});

export const adminOrderRouter = createTRPCRouter({
  // 获取所有订单（超级管理员）
  getAll: protectedProcedure
    .input(GetOrdersSchema)
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看所有订单",
        });
      }

      const { page, limit, status, tenantId, startDate, endDate, search } = input;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {};
      
      if (status) {
        where.status = status;
      }
      
      if (tenantId) {
        where.tenantId = tenantId;
      }
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = startDate;
        }
        if (endDate) {
          where.createdAt.lte = endDate;
        }
      }
      
      if (search) {
        where.OR = [
          { orderNumber: { contains: search, mode: "insensitive" } },
          { tenant: { name: { contains: search, mode: "insensitive" } } },
        ];
      }

      const [orders, total] = await Promise.all([
        ctx.db.order.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            subscription: {
              select: {
                id: true,
                plan: true,
              },
            },
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                billingCycle: true,
              },
            },
          },
        }),
        ctx.db.order.count({ where }),
      ]);

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取订单详情（超级管理员）
  getById: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看订单详情",
        });
      }

      const order = await ctx.db.order.findUnique({
        where: { id: input.id },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              type: true,
              createdAt: true,
            },
          },
          subscription: {
            include: {
              plan: true,
            },
          },
          plan: true,
          payments: {
            orderBy: { createdAt: "desc" },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在",
        });
      }

      return order;
    }),

  // 更新订单状态（超级管理员）
  updateStatus: protectedProcedure
    .input(UpdateOrderStatusSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新订单状态",
        });
      }

      const { orderId, status, note } = input;

      // 检查订单是否存在
      const existingOrder = await ctx.db.order.findUnique({
        where: { id: orderId },
        include: {
          subscription: true,
          plan: true,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在",
        });
      }

      // 更新订单状态
      const order = await ctx.db.order.update({
        where: { id: orderId },
        data: {
          status,
          updatedAt: new Date(),
        },
      });

      // 如果订单状态变为已支付，激活对应的订阅
      if (status === "PAID" && existingOrder.subscription && existingOrder.plan) {
        const plan = existingOrder.plan;
        const startDate = new Date();
        let endDate: Date;

        // 根据套餐计费周期计算到期时间
        if (plan.billingCycle === "YEARLY") {
          endDate = new Date(startDate.getTime() + 365 * 24 * 60 * 60 * 1000); // 1年
        } else if (plan.billingCycle === "MONTHLY") {
          endDate = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000); // 1个月
        } else {
          // 试用版或其他类型，默认30天
          endDate = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000);
        }

        // 更新订阅状态
        await ctx.db.subscription.update({
          where: { id: existingOrder.subscription.id },
          data: {
            status: "ACTIVE",
            startDate,
            endDate,
          },
        });

        // 更新商户类型
        if (existingOrder.subscription.tenantId) {
          let tenantType: "PERSONAL" | "ENTERPRISE" = "PERSONAL";

          // 根据套餐名称或价格判断商户类型
          if (plan.name.includes("企业") || plan.name.includes("Enterprise") || plan.price >= 200) {
            tenantType = "ENTERPRISE";
          }

          await ctx.db.tenant.update({
            where: { id: existingOrder.subscription.tenantId },
            data: {
              type: tenantType,
            },
          });
        }
      }

      // 如果订单状态变为取消或退款，停用对应的订阅
      if ((status === "CANCELLED" || status === "REFUNDED") && existingOrder.subscription) {
        await ctx.db.subscription.update({
          where: { id: existingOrder.subscription.id },
          data: {
            status: "CANCELLED",
          },
        });
      }

      // 记录操作日志（如果有note）
      if (note) {
        await ctx.db.orderLog.create({
          data: {
            orderId,
            action: `状态更新为${status}`,
            note,
            operatorId: ctx.session.user.id,
          },
        });
      }

      return {
        success: true,
        order,
        message: "订单状态更新成功",
        tenantId: existingOrder.subscription?.tenantId, // 返回商户ID用于前端刷新
      };
    }),

  // 审核订单（超级管理员）
  review: protectedProcedure
    .input(ReviewOrderSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以审核订单",
        });
      }

      const { orderId, approved, note } = input;

      // 检查订单是否存在
      const existingOrder = await ctx.db.order.findUnique({
        where: { id: orderId },
        include: {
          plan: true,
          tenant: true,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "订单不存在",
        });
      }

      if (existingOrder.status !== "PENDING") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只能审核待处理的订单",
        });
      }

      if (!existingOrder.paymentProofUrl) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "订单缺少付款凭证，无法审核",
        });
      }

      const newStatus = approved ? "PAID" : "FAILED";

      // 更新订单状态
      const order = await ctx.db.order.update({
        where: { id: orderId },
        data: {
          status: newStatus,
          adminNote: note,
          reviewedAt: new Date(),
          reviewedBy: ctx.session.user.id,
          paidAt: approved ? new Date() : null,
          updatedAt: new Date(),
        },
      });

      // 如果审核通过，创建或激活订阅
      if (approved && existingOrder.plan) {
        // 检查是否已有订阅
        let subscription = await ctx.db.subscription.findFirst({
          where: {
            tenantId: existingOrder.tenantId,
            planId: existingOrder.planId,
          },
        });

        if (subscription) {
          // 更新现有订阅
          const endDate = new Date();
          if (existingOrder.plan.billingCycle === "YEARLY") {
            endDate.setFullYear(endDate.getFullYear() + 1);
          } else if (existingOrder.plan.billingCycle === "MONTHLY") {
            endDate.setMonth(endDate.getMonth() + 1);
          } else {
            endDate.setDate(endDate.getDate() + 30); // TRIAL
          }

          subscription = await ctx.db.subscription.update({
            where: { id: subscription.id },
            data: {
              status: "ACTIVE",
              startDate: new Date(),
              endDate,
              price: existingOrder.plan.price,
              currency: existingOrder.plan.currency,
              features: existingOrder.plan.features,
              limits: existingOrder.plan.limits,
            },
          });
        } else {
          // 创建新订阅
          const endDate = new Date();
          if (existingOrder.plan.billingCycle === "YEARLY") {
            endDate.setFullYear(endDate.getFullYear() + 1);
          } else if (existingOrder.plan.billingCycle === "MONTHLY") {
            endDate.setMonth(endDate.getMonth() + 1);
          } else {
            endDate.setDate(endDate.getDate() + 30); // TRIAL
          }

          subscription = await ctx.db.subscription.create({
            data: {
              tenantId: existingOrder.tenantId,
              planId: existingOrder.planId!,
              status: "ACTIVE",
              startDate: new Date(),
              endDate,
              price: existingOrder.plan.price,
              currency: existingOrder.plan.currency,
              features: existingOrder.plan.features,
              limits: existingOrder.plan.limits,
            },
          });
        }

        // 关联订单和订阅
        await ctx.db.order.update({
          where: { id: orderId },
          data: { subscriptionId: subscription.id },
        });
      }

      // 记录操作日志
      await ctx.db.orderLog.create({
        data: {
          orderId,
          action: approved ? "审核通过" : "审核拒绝",
          note: note || (approved ? "订单审核通过，套餐已激活" : "订单审核未通过"),
          operatorId: ctx.session.user.id,
        },
      });

      return {
        success: true,
        order,
        message: approved ? "订单审核通过，套餐已激活" : "订单审核未通过",
      };
    }),

  // 获取订单统计信息（超级管理员）
  getStats: protectedProcedure
    .input(z.object({
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看订单统计",
        });
      }

      const { startDate, endDate } = input;
      const dateFilter: any = {};
      
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) {
          dateFilter.createdAt.gte = startDate;
        }
        if (endDate) {
          dateFilter.createdAt.lte = endDate;
        }
      }

      const [
        totalOrders,
        paidOrders,
        pendingOrders,
        failedOrders,
        totalRevenue,
        monthlyRevenue,
      ] = await Promise.all([
        ctx.db.order.count({ where: dateFilter }),
        ctx.db.order.count({ where: { ...dateFilter, status: "PAID" } }),
        ctx.db.order.count({ where: { ...dateFilter, status: "PENDING" } }),
        ctx.db.order.count({ where: { ...dateFilter, status: "FAILED" } }),
        ctx.db.order.aggregate({
          where: { ...dateFilter, status: "PAID" },
          _sum: { amount: true },
        }),
        ctx.db.order.aggregate({
          where: {
            status: "PAID",
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
          _sum: { amount: true },
        }),
      ]);

      // 获取每日订单统计（最近30天）
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dailyStats = await ctx.db.order.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: { gte: thirtyDaysAgo },
        },
        _count: true,
        _sum: { amount: true },
      });

      return {
        totalOrders,
        paidOrders,
        pendingOrders,
        failedOrders,
        totalRevenue: totalRevenue._sum.amount || 0,
        monthlyRevenue: monthlyRevenue._sum.amount || 0,
        conversionRate: totalOrders > 0 ? (paidOrders / totalOrders) * 100 : 0,
        dailyStats: dailyStats.map(stat => ({
          date: stat.createdAt,
          orderCount: stat._count,
          revenue: stat._sum.amount || 0,
        })),
      };
    }),

  // 导出订单数据（超级管理员）
  export: protectedProcedure
    .input(GetOrdersSchema)
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以导出订单数据",
        });
      }

      const { status, tenantId, startDate, endDate, search } = input;

      // 构建查询条件（不分页，导出所有符合条件的数据）
      const where: any = {};
      
      if (status) where.status = status;
      if (tenantId) where.tenantId = tenantId;
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = startDate;
        if (endDate) where.createdAt.lte = endDate;
      }
      if (search) {
        where.OR = [
          { orderNumber: { contains: search, mode: "insensitive" } },
          { tenant: { name: { contains: search, mode: "insensitive" } } },
        ];
      }

      const orders = await ctx.db.order.findMany({
        where,
        orderBy: { createdAt: "desc" },
        include: {
          tenant: {
            select: {
              name: true,
              type: true,
            },
          },
          subscription: {
            include: {
              plan: {
                select: {
                  name: true,
                  price: true,
                  billingCycle: true,
                },
              },
            },
          },
        },
      });

      return orders.map(order => ({
        orderNumber: order.orderNumber,
        tenantName: order.tenant.name,
        tenantType: order.tenant.type,
        planName: order.subscription?.plan?.name || '',
        amount: order.amount,
        currency: order.currency,
        status: order.status,
        createdAt: order.createdAt,
        paidAt: order.paidAt,
      }));
    }),
});
