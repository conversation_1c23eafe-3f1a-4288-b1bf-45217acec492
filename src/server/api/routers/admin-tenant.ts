import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 商户类型枚举 - 更新为新的类型
const TenantTypeSchema = z.enum(["FREE", "BASIC", "PRO", "ENTERPRISE"]);

// 创建商户的验证模式
const CreateTenantSchema = z.object({
  name: z.string().min(1, "商户名称不能为空").max(100, "商户名称不能超过100个字符"),
  description: z.string().max(500, "描述不能超过500个字符").optional(),
  type: TenantTypeSchema,
  isActive: z.boolean().default(true),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
});

// 更新商户的验证模式
const UpdateTenantSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1, "商户名称不能为空").max(100, "商户名称不能超过100个字符").optional(),
  description: z.string().max(500, "描述不能超过500个字符").optional(),
  type: TenantTypeSchema.optional(),
  isActive: z.boolean().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
});

// 查询参数
const GetTenantsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  type: TenantTypeSchema.optional(),
  isActive: z.boolean().optional(),
});

export const adminTenantRouter = createTRPCRouter({
  // 获取所有商户（超级管理员）
  getAll: protectedProcedure
    .input(GetTenantsSchema)
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看所有商户",
        });
      }

      const { page, limit, search, type, isActive } = input;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {};
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { email: { contains: search, mode: "insensitive" } },
        ];
      }
      
      if (type) {
        where.type = type;
      }
      
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // 先获取基本的商户数据
      const [tenants, total] = await Promise.all([
        ctx.db.tenant.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            _count: {
              select: {
                memberships: true,
                documents: true,
                notifications: true,
                subscriptions: true,
              },
            },
            memberships: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    createdAt: true,
                  },
                },
              },
              orderBy: { createdAt: "asc" },
              take: 10, // 限制数量避免数据过大
            },
            subscriptions: {
              include: {
                plan: {
                  select: {
                    name: true,
                    price: true,
                    billingCycle: true,
                  },
                },
              },
              orderBy: { createdAt: "desc" },
              take: 5, // 最近5个订阅
            },
          },
        }),
        ctx.db.tenant.count({ where }),
      ]);

      // 为每个商户获取管理员信息（简化查询）
      const tenantsWithDetails = await Promise.all(
        tenants.map(async (tenant) => {
          // 获取管理员用户
          const adminMembership = await ctx.db.membership.findFirst({
            where: {
              tenantId: tenant.id,
              role: { in: ["ADMIN", "TENANT_ADMIN"] },
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });

          // 获取当前订阅（如果存在）
          const currentSubscription = await ctx.db.subscription.findFirst({
            where: {
              tenantId: tenant.id,
              status: { in: ["ACTIVE", "TRIAL"] },
            },
            orderBy: { createdAt: "desc" },
            include: {
              plan: {
                select: {
                  name: true,
                  price: true,
                  billingCycle: true,
                },
              },
            },
          });

          return {
            ...tenant,
            adminUser: adminMembership?.user || null,
            currentSubscription: currentSubscription || null,
          };
        })
      );

      return {
        tenants: tenantsWithDetails,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 创建商户（超级管理员）
  create: protectedProcedure
    .input(CreateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以创建商户",
        });
      }

      // 检查商户名称是否已存在
      const existingTenant = await ctx.db.tenant.findFirst({
        where: { name: input.name },
      });

      if (existingTenant) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "商户名称已存在",
        });
      }

      const tenant = await ctx.db.tenant.create({
        data: {
          name: input.name,
          description: input.description,
          type: input.type,
          isActive: input.isActive,
          email: input.email,
          phone: input.phone,
          address: input.address,
        },
        include: {
          _count: {
            select: {
              memberships: true,
              documents: true,
            },
          },
        },
      });

      return {
        success: true,
        tenant,
        message: "商户创建成功",
      };
    }),

  // 更新商户（超级管理员）
  update: protectedProcedure
    .input(UpdateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新商户",
        });
      }

      const { id, ...updateData } = input;

      // 检查商户是否存在
      const existingTenant = await ctx.db.tenant.findUnique({
        where: { id },
      });

      if (!existingTenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      // 如果更新名称，检查是否与其他商户重名
      if (updateData.name && updateData.name !== existingTenant.name) {
        const conflictTenant = await ctx.db.tenant.findFirst({
          where: {
            name: updateData.name,
            id: { not: id },
          },
        });

        if (conflictTenant) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "商户名称已存在",
          });
        }
      }

      const tenant = await ctx.db.tenant.update({
        where: { id },
        data: updateData,
      });

      return {
        success: true,
        tenant,
        message: "商户更新成功",
      };
    }),

  // 删除商户（超级管理员）
  delete: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以删除商户",
        });
      }

      // 检查商户是否存在
      const existingTenant = await ctx.db.tenant.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              documents: true,
              memberships: true,
              subscriptions: true,
            },
          },
        },
      });

      if (!existingTenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      // 检查是否有关联数据
      if (existingTenant._count.documents > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "无法删除有证件数据的商户，请先清理相关数据",
        });
      }

      if (existingTenant._count.subscriptions > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "无法删除有订阅记录的商户，请先处理相关订阅",
        });
      }

      // 删除商户（会级联删除成员关系）
      await ctx.db.tenant.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: "商户删除成功",
      };
    }),

  // 切换商户状态（超级管理员）
  toggleStatus: protectedProcedure
    .input(z.object({ 
      id: z.string().min(1),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以管理商户状态",
        });
      }

      const tenant = await ctx.db.tenant.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
      });

      return {
        success: true,
        tenant,
        message: `商户已${input.isActive ? '启用' : '禁用'}`,
      };
    }),

  // 获取商户详情（超级管理员）
  getById: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看商户详情",
        });
      }

      const tenant = await ctx.db.tenant.findUnique({
        where: { id: input.id },
        include: {
          memberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                  createdAt: true,
                },
              },
            },
            orderBy: { createdAt: "asc" },
          },
          subscriptions: {
            include: {
              plan: true,
            },
            orderBy: { createdAt: "desc" },
          },
          _count: {
            select: {
              memberships: true,
              documents: true,
              subscriptions: true,
              notifications: true,
            },
          },
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      return tenant;
    }),

  // 获取商户统计信息（超级管理员）
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看商户统计",
        });
      }

      const [
        totalTenants,
        activeTenants,
        totalUsers,
        totalDocuments,
        thisMonthTenants,
      ] = await Promise.all([
        ctx.db.tenant.count(),
        ctx.db.tenant.count({ where: { isActive: true } }),
        ctx.db.user.count(),
        ctx.db.document.count(),
        ctx.db.tenant.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
        }),
      ]);

      // 按类型统计商户
      const tenantsByType = await ctx.db.tenant.groupBy({
        by: ['type'],
        _count: true,
      });

      return {
        totalTenants,
        activeTenants,
        totalUsers,
        totalDocuments,
        thisMonthTenants,
        tenantsByType: tenantsByType.map(item => ({
          type: item.type,
          count: item._count,
        })),
        activityScore: Math.min(100, Math.round(
          (thisMonthTenants * 10 + totalUsers * 2 + totalDocuments * 0.1) / 10
        )),
      };
    }),
});
