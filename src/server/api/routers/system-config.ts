import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 银行信息配置验证
const BankInfoSchema = z.object({
  bankName: z.string().min(1, "银行名称不能为空"),
  accountName: z.string().min(1, "账户名称不能为空"),
  accountNumber: z.string().min(1, "账户号码不能为空"),
  branchName: z.string().optional(),
  swiftCode: z.string().optional(),
  routingNumber: z.string().optional(),
  notes: z.string().optional(),
});

// 系统配置更新验证
const SystemConfigSchema = z.object({
  key: z.string().min(1),
  value: z.any(),
  description: z.string().optional(),
});

export const systemConfigRouter = createTRPCRouter({
  // 获取银行信息配置（公开接口，用于支付页面显示）
  getBankInfo: publicProcedure.query(async ({ ctx }) => {
    const config = await ctx.db.systemConfig.findUnique({
      where: { key: "BANK_INFO" },
    });

    if (!config || !config.value) {
      return null;
    }

    return config.value as any;
  }),

  // 获取银行信息配置（管理员接口，用于配置页面）
  getBankConfig: protectedProcedure.query(async ({ ctx }) => {
    try {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以查看银行配置",
        });
      }

      const config = await ctx.db.systemConfig.findUnique({
        where: { key: "BANK_INFO" },
      });

      return config?.value || null;
    } catch (error) {
      console.error('getBankConfig error:', error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取银行配置失败",
      });
    }
  }),

  // 获取系统配置（超级管理员）
  getConfig: protectedProcedure
    .input(z.object({ key: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        // 检查超级管理员权限
        const user = await ctx.db.user.findUnique({
          where: { id: ctx.session.user.id },
          select: { role: true },
        });

        if (user?.role !== "SUPER_ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "只有超级管理员可以查看系统配置",
          });
        }

        const config = await ctx.db.systemConfig.findUnique({
          where: { key: input.key },
        });

        return config;
      } catch (error) {
        console.error('getConfig error:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取配置失败",
        });
      }
    }),

  // 获取所有系统配置（超级管理员）
  getAllConfigs: protectedProcedure.query(async ({ ctx }) => {
    // 检查超级管理员权限
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: { role: true },
    });

    if (user?.role !== "SUPER_ADMIN") {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有超级管理员可以查看系统配置",
      });
    }

    const configs = await ctx.db.systemConfig.findMany({
      orderBy: { key: "asc" },
    });

    return configs;
  }),

  // 更新银行信息配置（超级管理员）
  updateBankInfo: protectedProcedure
    .input(BankInfoSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新银行信息",
        });
      }

      // 更新或创建银行信息配置
      const config = await ctx.db.systemConfig.upsert({
        where: { key: "BANK_INFO" },
        update: {
          value: input,
          description: "系统收款银行信息配置",
          updatedAt: new Date(),
        },
        create: {
          key: "BANK_INFO",
          value: input,
          description: "系统收款银行信息配置",
        },
      });

      return {
        success: true,
        config,
        message: "银行信息更新成功",
      };
    }),

  // 更新系统配置（超级管理员）
  updateConfig: protectedProcedure
    .input(SystemConfigSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以更新系统配置",
        });
      }

      // 更新或创建配置
      const config = await ctx.db.systemConfig.upsert({
        where: { key: input.key },
        update: {
          value: input.value,
          description: input.description,
          updatedAt: new Date(),
        },
        create: {
          key: input.key,
          value: input.value,
          description: input.description,
        },
      });

      return {
        success: true,
        config,
        message: "配置更新成功",
      };
    }),

  // 删除系统配置（超级管理员）
  deleteConfig: protectedProcedure
    .input(z.object({ key: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查超级管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有超级管理员可以删除系统配置",
        });
      }

      // 防止删除重要配置
      const protectedKeys = ["BANK_INFO"];
      if (protectedKeys.includes(input.key)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该配置不能删除",
        });
      }

      await ctx.db.systemConfig.delete({
        where: { key: input.key },
      });

      return {
        success: true,
        message: "配置删除成功",
      };
    }),
});
