import { z } from "zod";
import { TRPCError } from "@trpc/server";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 商户类型枚举
const TenantTypeSchema = z.enum(["PERSONAL", "ENTERPRISE"]);

// 成员角色枚举
const MemberRoleSchema = z.enum(["ADMIN", "MEMBER"]);

// 创建商户的输入验证
const CreateTenantSchema = z.object({
  name: z.string().min(1, "商户名称不能为空").max(100, "商户名称不能超过100个字符"),
  type: TenantTypeSchema,
  description: z.string().max(500, "描述不能超过500个字符").optional(),
});

// 更新商户的输入验证
const UpdateTenantSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1, "商户名称不能为空").max(100, "商户名称不能超过100个字符").optional(),
  description: z.string().max(500, "描述不能超过500个字符").optional(),
});

// 邀请成员的输入验证
const InviteMemberSchema = z.object({
  tenantId: z.string().min(1),
  email: z.string().email("请输入有效的邮箱地址"),
  role: MemberRoleSchema,
});

// 更新成员角色的输入验证
const UpdateMemberRoleSchema = z.object({
  tenantId: z.string().min(1),
  userId: z.string().min(1),
  role: MemberRoleSchema,
});

export const tenantRouter = createTRPCRouter({
  // 创建租户
  create: protectedProcedure
    .input(CreateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查用户是否已有同名租户
      const existingTenant = await ctx.db.tenant.findFirst({
        where: {
          name: input.name,
          memberships: {
            some: {
              userId: ctx.session.user.id,
            },
          },
        },
      });

      if (existingTenant) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "您已有同名的商户",
        });
      }

      // 创建租户并添加创建者为所有者
      const tenant = await ctx.db.tenant.create({
        data: {
          name: input.name,
          type: input.type,
          description: input.description,
          memberships: {
            create: {
              userId: ctx.session.user.id,
              role: "TENANT_ADMIN",
            },
          },
        },
        include: {
          memberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      return tenant;
    }),

  // 获取用户的所有租户
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const tenants = await ctx.db.tenant.findMany({
      where: {
        memberships: {
          some: {
            userId: ctx.session.user.id,
          },
        },
      },
      include: {
        memberships: {
          where: {
            userId: ctx.session.user.id,
          },
          select: {
            role: true,
          },
        },
        _count: {
          select: {
            documents: true,
            memberships: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return tenants.map(tenant => ({
      ...tenant,
      userRole: tenant.memberships[0]?.role,
      memberships: undefined, // 移除原始的 memberships 数据
    }));
  }),

  // 获取单个租户详情
  getById: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      const tenant = await ctx.db.tenant.findFirst({
        where: {
          id: input.id,
          memberships: {
            some: {
              userId: ctx.session.user.id,
            },
          },
        },
        include: {
          memberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
          _count: {
            select: {
              documents: true,
            },
          },
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在或您没有访问权限",
        });
      }

      // 获取当前用户在该租户中的角色
      const userMembership = tenant.memberships.find(
        m => m.userId === ctx.session.user.id
      );

      return {
        ...tenant,
        userRole: userMembership?.role,
        memberCount: tenant.memberships.length,
      };
    }),

  // 更新租户信息
  update: protectedProcedure
    .input(UpdateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查用户是否有权限更新租户（只有管理员可以更新）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: id,
          role: { in: ["ADMIN", "TENANT_ADMIN"] },
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限更新该商户",
        });
      }

      // 如果更新名称，检查是否与用户的其他租户重名
      if (updateData.name) {
        const existingTenant = await ctx.db.tenant.findFirst({
          where: {
            name: updateData.name,
            id: { not: id },
            memberships: {
              some: {
                userId: ctx.session.user.id,
              },
            },
          },
        });

        if (existingTenant) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "您已有同名的商户",
          });
        }
      }

      return ctx.db.tenant.update({
        where: { id },
        data: updateData,
        include: {
          memberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          _count: {
            select: {
              documents: true,
            },
          },
        },
      });
    }),

  // 删除租户
  delete: protectedProcedure
    .input(z.object({ id: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 检查用户是否是租户的管理员
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.id,
          role: { in: ["ADMIN", "TENANT_ADMIN"] },
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有租户管理员可以删除租户",
        });
      }

      // 检查租户是否还有证件数据
      const documentCount = await ctx.db.document.count({
        where: { tenantId: input.id },
      });

      if (documentCount > 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "请先删除所有证件数据后再删除租户",
        });
      }

      // 删除租户（会级联删除成员关系）
      return ctx.db.tenant.delete({
        where: { id: input.id },
      });
    }),

  // 邀请成员
  inviteMember: protectedProcedure
    .input(InviteMemberSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查用户是否有权限邀请成员（只有 ADMIN 可以邀请）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: { in: ["ADMIN", "TENANT_ADMIN"] },
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限邀请成员",
        });
      }

      // 检查被邀请的用户是否存在
      const invitedUser = await ctx.db.user.findUnique({
        where: { email: input.email },
      });

      if (!invitedUser) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "该邮箱对应的用户不存在",
        });
      }

      // 检查用户是否已经是成员
      const existingMembership = await ctx.db.membership.findFirst({
        where: {
          userId: invitedUser.id,
          tenantId: input.tenantId,
        },
      });

      if (existingMembership) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该用户已经是租户成员",
        });
      }

      // 创建成员关系
      const newMembership = await ctx.db.membership.create({
        data: {
          userId: invitedUser.id,
          tenantId: input.tenantId,
          role: input.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return newMembership;
    }),

  // 更新成员角色
  updateMemberRole: protectedProcedure
    .input(UpdateMemberRoleSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查用户是否有权限更新成员角色（只有 ADMIN 可以更新）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: { in: ["ADMIN", "TENANT_ADMIN"] },
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有租户管理员可以更新成员角色",
        });
      }

      // 不能修改自己的角色
      if (input.userId === ctx.session.user.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "不能修改自己的角色",
        });
      }

      // 检查目标成员是否存在
      const targetMembership = await ctx.db.membership.findFirst({
        where: {
          userId: input.userId,
          tenantId: input.tenantId,
        },
      });

      if (!targetMembership) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "目标成员不存在",
        });
      }

      return ctx.db.membership.update({
        where: { id: targetMembership.id },
        data: { role: input.role },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }),

  // 移除成员
  removeMember: protectedProcedure
    .input(z.object({ 
      tenantId: z.string().min(1),
      userId: z.string().min(1),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查用户是否有权限移除成员（只有 ADMIN 可以移除）
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
          role: { in: ["ADMIN", "TENANT_ADMIN"] },
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限移除成员",
        });
      }

      // 不能移除自己
      if (input.userId === ctx.session.user.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "不能移除自己",
        });
      }

      // 检查目标成员是否存在
      const targetMembership = await ctx.db.membership.findFirst({
        where: {
          userId: input.userId,
          tenantId: input.tenantId,
        },
      });

      if (!targetMembership) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "目标成员不存在",
        });
      }

      // 管理员不能移除所有者
      if (["ADMIN", "TENANT_ADMIN"].includes(membership.role) && targetMembership.role === "OWNER") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "管理员不能移除所有者",
        });
      }

      return ctx.db.membership.delete({
        where: { id: targetMembership.id },
      });
    }),

  // 获取租户成员列表
  getMembers: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 验证用户是否有权限访问该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该租户",
        });
      }

      const members = await ctx.db.membership.findMany({
        where: {
          tenantId: input.tenantId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      return members;
    }),

  // 离开租户
  leave: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "您不是该租户的成员",
        });
      }

      // 如果是所有者，检查是否还有其他成员
      if (membership.role === "OWNER") {
        const otherMembersCount = await ctx.db.membership.count({
          where: {
            tenantId: input.tenantId,
            userId: { not: ctx.session.user.id },
          },
        });

        if (otherMembersCount > 0) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: "作为所有者，请先转移所有权或移除其他成员后再离开",
          });
        }

        // 如果是唯一成员且是所有者，检查是否还有证件数据
        const documentCount = await ctx.db.document.count({
          where: { tenantId: input.tenantId },
        });

        if (documentCount > 0) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: "请先删除所有证件数据后再离开租户",
          });
        }

        // 删除整个租户
        await ctx.db.tenant.delete({
          where: { id: input.tenantId },
        });

        return { deleted: true };
      }

      // 非所有者直接删除成员关系
      await ctx.db.membership.delete({
        where: { id: membership.id },
      });

      return { deleted: false };
    }),

  // 切换当前租户
  switch: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该租户",
        });
      }

      // 更新用户的当前租户ID
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: { tenantId: input.tenantId },
      });

      return { success: true };
    }),
});