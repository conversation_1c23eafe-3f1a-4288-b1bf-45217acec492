import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 证件类型的输入验证
const CreateDocumentTypeSchema = z.object({
  tenantId: z.string().min(1),
  name: z.string().min(1, "证件类型名称不能为空"),
  icon: z.string().optional(),
  color: z.string().optional(),
  description: z.string().optional(),
  sortOrder: z.number().default(0),
});

const UpdateDocumentTypeSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1, "证件类型名称不能为空").optional(),
  icon: z.string().optional(),
  color: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.number().optional(),
});

export const documentTypeRouter = createTRPCRouter({
  // 获取租户的证件类型列表
  getAll: protectedProcedure
    .input(
      z.object({
        tenantId: z.string().min(1, "租户ID不能为空"),
        includeInactive: z.boolean().default(false),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 获取证件类型列表
      const documentTypes = await ctx.db.documentType.findMany({
        where: {
          tenantId: input.tenantId,
          ...(input.includeInactive ? {} : { isActive: true }),
        },
        orderBy: [
          { sortOrder: "asc" },
          { createdAt: "desc" },
        ],
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return documentTypes;
    }),

  // 创建证件类型
  create: protectedProcedure
    .input(CreateDocumentTypeSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 智能生成代码：中文名称转换为合适的英文代码
      const generateCode = (name: string): string => {
        // 常见证件类型的映射
        const typeMapping: Record<string, string> = {
          '身份证': 'ID_CARD',
          '护照': 'PASSPORT',
          '驾驶证': 'DRIVER_LICENSE',
          '营业执照': 'BUSINESS_LICENSE',
          '工作许可证': 'WORK_PERMIT',
          '劳工证': 'WORK_PERMIT',
          '签证': 'VISA',
          '税务证': 'TAX_CERTIFICATE',
          '税务登记证': 'TAX_REGISTRATION',
          '组织机构代码证': 'ORG_CODE_CERT',
          '社会信用代码证': 'SOCIAL_CREDIT_CODE',
          '食品经营许可证': 'FOOD_BUSINESS_LICENSE',
          '医疗器械许可证': 'MEDICAL_DEVICE_LICENSE',
          '建筑资质证': 'CONSTRUCTION_QUALIFICATION',
          '安全生产许可证': 'SAFETY_PRODUCTION_LICENSE',
          '进出口许可证': 'IMPORT_EXPORT_LICENSE',
          '免税证': 'TAX_EXEMPTION_CERT',
          '居住证': 'RESIDENCE_PERMIT',
          '暂住证': 'TEMPORARY_RESIDENCE',
          '学生证': 'STUDENT_ID',
          '教师资格证': 'TEACHER_QUALIFICATION',
          '职业资格证': 'PROFESSIONAL_QUALIFICATION',
          '技能证书': 'SKILL_CERTIFICATE',
          '培训证书': 'TRAINING_CERTIFICATE',
          '健康证': 'HEALTH_CERTIFICATE',
          '疫苗接种证': 'VACCINATION_CERTIFICATE',
          '出生证明': 'BIRTH_CERTIFICATE',
          '结婚证': 'MARRIAGE_CERTIFICATE',
          '离婚证': 'DIVORCE_CERTIFICATE',
          '房产证': 'PROPERTY_CERTIFICATE',
          '土地使用证': 'LAND_USE_CERTIFICATE',
          '车辆登记证': 'VEHICLE_REGISTRATION',
          '行驶证': 'VEHICLE_LICENSE',
          '保险单': 'INSURANCE_POLICY',
          '银行开户许可证': 'BANK_ACCOUNT_LICENSE',
          '外汇登记证': 'FOREX_REGISTRATION',
          '海关注册证': 'CUSTOMS_REGISTRATION',
          '质量认证证书': 'QUALITY_CERTIFICATION',
          'ISO证书': 'ISO_CERTIFICATE',
          '专利证书': 'PATENT_CERTIFICATE',
          '商标注册证': 'TRADEMARK_CERTIFICATE',
          '版权证书': 'COPYRIGHT_CERTIFICATE',
          '软件著作权': 'SOFTWARE_COPYRIGHT',
          '网络文化经营许可证': 'INTERNET_CULTURE_LICENSE',
          'ICP许可证': 'ICP_LICENSE',
          '域名证书': 'DOMAIN_CERTIFICATE',
          'SSL证书': 'SSL_CERTIFICATE',
          '其他': 'OTHER',
          '其它': 'OTHER'
        };

        // 首先检查是否有直接映射
        if (typeMapping[name]) {
          return typeMapping[name];
        }

        // 如果没有直接映射，生成基于拼音的代码
        // 简化版：将中文转换为拼音首字母或英文描述
        let code = name
          .replace(/证书?$/, '_CERTIFICATE')  // 证书 -> _CERTIFICATE
          .replace(/许可证$/, '_LICENSE')      // 许可证 -> _LICENSE
          .replace(/登记证$/, '_REGISTRATION') // 登记证 -> _REGISTRATION
          .replace(/资格证$/, '_QUALIFICATION') // 资格证 -> _QUALIFICATION
          .replace(/[证件]$/, '_CERT')         // 证/件 -> _CERT
          .replace(/[^\w]/g, '_')              // 其他字符 -> _
          .toUpperCase();

        // 如果转换后还是包含中文，使用通用前缀
        if (/[\u4e00-\u9fa5]/.test(code)) {
          code = `CUSTOM_${Date.now().toString(36).toUpperCase()}`;
        }

        // 确保代码不超过30个字符
        return code.substring(0, 30);
      };

      const baseCode = generateCode(input.name);

      // 检查代码是否已存在，如果存在则添加数字后缀
      let finalCode = baseCode;
      let counter = 1;
      while (true) {
        const existingType = await ctx.db.documentType.findFirst({
          where: {
            tenantId: input.tenantId,
            code: finalCode,
          },
        });

        if (!existingType) {
          break;
        }

        finalCode = `${baseCode}_${counter}`;
        counter++;

        // 防止无限循环
        if (counter > 999) {
          finalCode = `CUSTOM_${Date.now().toString(36).toUpperCase()}`;
          break;
        }
      }

      // 创建证件类型
      return ctx.db.documentType.create({
        data: {
          ...input,
          code: finalCode,
          createdById: ctx.session.user.id,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }),

  // 更新证件类型
  update: protectedProcedure
    .input(UpdateDocumentTypeSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 获取证件类型信息
      const documentType = await ctx.db.documentType.findUnique({
        where: { id },
        select: { tenantId: true, isSystem: true },
      });

      if (!documentType) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "证件类型不存在",
        });
      }

      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: documentType.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 系统预设类型只允许修改颜色、描述和排序，不允许修改名称
      if (documentType.isSystem && updateData.name !== undefined) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "系统预设类型不允许修改名称",
        });
      }

      // 更新证件类型
      return ctx.db.documentType.update({
        where: { id },
        data: updateData,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }),

  // 删除证件类型
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 获取证件类型信息
      const documentType = await ctx.db.documentType.findUnique({
        where: { id: input.id },
        select: { tenantId: true, isSystem: true, code: true },
      });

      if (!documentType) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "证件类型不存在",
        });
      }

      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: documentType.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 系统预设类型不允许删除
      if (documentType.isSystem) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "系统预设类型不允许删除",
        });
      }

      // 检查是否有证件使用此类型
      const documentTypeRecord = await ctx.db.documentType.findUnique({
        where: { id: input.id },
        select: { name: true, tenantId: true },
      });

      const documentsCount = await ctx.db.document.count({
        where: {
          tenantId: documentType.tenantId,
          certType: documentTypeRecord?.name,
        },
      });

      if (documentsCount > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: `无法删除，还有 ${documentsCount} 个证件使用此类型`,
        });
      }

      // 删除证件类型
      return ctx.db.documentType.delete({
        where: { id: input.id },
      });
    }),

  // 初始化系统预设证件类型
  initializeSystemTypes: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 系统预设证件类型
      const systemTypes = [
        {
          name: "签证",
          code: "VISA",
          icon: "Flight",
          color: "#1976d2",
          description: "签证",
          sortOrder: 1,
        },
        {
          name: "劳工证",
          code: "WORK_PERMIT",
          icon: "Work",
          color: "#388e3c",
          description: "劳工证",
          sortOrder: 2,
        },
        {
          name: "驾驶证",
          code: "DRIVER_LICENSE",
          icon: "DriveEta",
          color: "#f57c00",
          description: "驾驶证",
          sortOrder: 3,
        },
        {
          name: "营业执照",
          code: "BUSINESS_LICENSE",
          icon: "Business",
          color: "#7b1fa2",
          description: "营业执照",
          sortOrder: 4,
        },
      ];

      // 批量创建系统类型（如果不存在）
      const createdTypes = [];
      for (const typeData of systemTypes) {
        const existing = await ctx.db.documentType.findFirst({
          where: {
            tenantId: input.tenantId,
            code: typeData.code,
          },
        });

        if (!existing) {
          const created = await ctx.db.documentType.create({
            data: {
              ...typeData,
              tenantId: input.tenantId,
              isSystem: true,
              createdById: ctx.session.user.id,
            },
          });
          createdTypes.push(created);
        }
      }

      return {
        created: createdTypes.length,
        message: `成功初始化 ${createdTypes.length} 个系统证件类型`,
      };
    }),
});
