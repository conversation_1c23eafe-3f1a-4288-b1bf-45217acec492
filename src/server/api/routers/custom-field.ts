import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 自定义字段配置的输入验证
const CustomFieldConfigSchema = z.object({
  tenantId: z.string().min(1),
  category: z.enum(["customer", "document"]),
  name: z.string().min(1, "字段名称不能为空"),
  type: z.enum(["text", "number", "date", "textarea", "select"]),
  required: z.boolean().default(false),
  options: z.array(z.string()).optional(),
  placeholder: z.string().optional(),
  defaultValue: z.string().optional(),
  sortOrder: z.number().default(0),
});

const UpdateCustomFieldConfigSchema = CustomFieldConfigSchema.partial().extend({
  id: z.string().min(1),
});

// 辅助函数：验证租户访问权限
async function validateTenantAccess(ctx: { db: any; session: any }, tenantId: string) {
  if (!ctx.db) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "数据库连接错误",
    });
  }

  if (!ctx.session?.user?.id) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "用户未登录",
    });
  }

  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (user?.role !== "SUPER_ADMIN") {
    const membership = await ctx.db.membership.findFirst({
      where: {
        userId: ctx.session.user.id,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "您没有权限访问该租户",
      });
    }
  }
}

// 辅助函数：检查字段使用情况
async function checkFieldUsage(ctx: { db: any }, tenantId: string, fieldName: string): Promise<number> {
  if (!ctx.db) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "数据库连接错误",
    });
  }

  const documents = await ctx.db.document.findMany({
    where: { tenantId },
    select: { customFields: true },
  });

  let usageCount = 0;
  documents.forEach((doc: any) => {
    if (doc.customFields && typeof doc.customFields === 'object') {
      const customFields = doc.customFields as Record<string, any>;
      if (customFields[fieldName] !== null && customFields[fieldName] !== undefined && customFields[fieldName] !== '') {
        usageCount++;
      }
    }
  });

  return usageCount;
}

export const customFieldRouter = createTRPCRouter({
  // 获取租户的自定义字段配置
  getFieldConfigs: protectedProcedure
    .input(
      z.object({
        tenantId: z.string(),
        category: z.enum(["customer", "document"]).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 从已有证件的自定义字段中提取字段配置
      const documents = await ctx.db.document.findMany({
        where: { tenantId: input.tenantId },
        select: { customFields: true },
      });

      const fieldConfigs = new Map<string, { name: string; type: string; category: string; usageCount: number }>();

      // 分析现有证件的自定义字段
      documents.forEach((doc) => {
        if (doc.customFields && typeof doc.customFields === 'object') {
          const customFields = doc.customFields as Record<string, any>;
          
          Object.entries(customFields).forEach(([fieldName, value]) => {
            if (value !== null && value !== undefined && value !== '') {
              // 根据字段名推断类别和类型
              const category = inferFieldCategory(fieldName);
              const type = inferFieldType(value);
              
              const key = `${category}-${fieldName}`;
              if (fieldConfigs.has(key)) {
                const existing = fieldConfigs.get(key)!;
                existing.usageCount += 1;
              } else {
                fieldConfigs.set(key, {
                  name: fieldName,
                  type,
                  category,
                  usageCount: 1,
                });
              }
            }
          });
        }
      });

      // 转换为数组并按使用次数排序
      const result = Array.from(fieldConfigs.values())
        .filter(config => !input.category || config.category === input.category)
        .sort((a, b) => b.usageCount - a.usageCount);

      return result;
    }),

  // 添加字段配置（记录用户使用的字段）
  addFieldConfig: protectedProcedure
    .input(CustomFieldConfigSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (user?.role !== "SUPER_ADMIN") {
        // 验证租户成员权限
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 创建新的字段配置
      const fieldConfig = await ctx.db.customFieldConfig.create({
        data: {
          ...input,
          createdById: ctx.session.user.id,
        },
      });

      return {
        success: true,
        message: "字段配置已保存",
        fieldConfig,
      };
    }),

  // 获取所有字段配置（新的完整API）
  getAll: protectedProcedure
    .input(
      z.object({
        tenantId: z.string(),
        category: z.enum(["customer", "document"]).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      await validateTenantAccess(ctx, input.tenantId);

      const where: any = {
        tenantId: input.tenantId,
        isActive: true,
      };

      if (input.category) {
        where.category = input.category;
      }

      const fieldConfigs = await ctx.db.customFieldConfig.findMany({
        where,
        orderBy: [
          { sortOrder: "asc" },
          { createdAt: "asc" },
        ],
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return fieldConfigs;
    }),

  // 创建新的字段配置
  create: protectedProcedure
    .input(CustomFieldConfigSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证用户权限
      await validateTenantAccess(ctx, input.tenantId);

      // 检查字段名是否已存在
      const existingField = await ctx.db.customFieldConfig.findFirst({
        where: {
          tenantId: input.tenantId,
          name: input.name,
          category: input.category,
          isActive: true,
        },
      });

      if (existingField) {
        throw new TRPCError({
          code: "CONFLICT",
          message: `字段"${input.name}"在${input.category === "customer" ? "客户" : "证件"}类别中已存在`,
        });
      }

      const fieldConfig = await ctx.db.customFieldConfig.create({
        data: {
          ...input,
          createdById: ctx.session.user.id,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return fieldConfig;
    }),

  // 更新字段配置
  update: protectedProcedure
    .input(UpdateCustomFieldConfigSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 获取现有字段配置
      const existingField = await ctx.db.customFieldConfig.findUnique({
        where: { id },
      });

      if (!existingField) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "字段配置不存在",
        });
      }

      // 验证用户权限
      await validateTenantAccess(ctx, existingField.tenantId);

      // 如果更新名称，检查是否与其他字段冲突
      if (updateData.name && updateData.name !== existingField.name) {
        const conflictField = await ctx.db.customFieldConfig.findFirst({
          where: {
            tenantId: existingField.tenantId,
            name: updateData.name,
            category: updateData.category || existingField.category,
            isActive: true,
            id: { not: id },
          },
        });

        if (conflictField) {
          throw new TRPCError({
            code: "CONFLICT",
            message: `字段"${updateData.name}"已存在`,
          });
        }
      }

      const updatedField = await ctx.db.customFieldConfig.update({
        where: { id },
        data: updateData,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return updatedField;
    }),

  // 删除字段配置
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 获取字段配置
      const fieldConfig = await ctx.db.customFieldConfig.findUnique({
        where: { id: input.id },
      });

      if (!fieldConfig) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "字段配置不存在",
        });
      }

      // 验证用户权限
      await validateTenantAccess(ctx, fieldConfig.tenantId);

      // 检查字段是否被使用
      const usageCount = await checkFieldUsage(ctx, fieldConfig.tenantId, fieldConfig.name);

      if (usageCount > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: `字段"${fieldConfig.name}"正在被 ${usageCount} 条记录使用，无法删除`,
        });
      }

      // 软删除（设置为不活跃）
      await ctx.db.customFieldConfig.update({
        where: { id: input.id },
        data: { isActive: false },
      });

      return { success: true, message: "字段已删除" };
    }),

  // 检查字段使用情况
  checkUsage: protectedProcedure
    .input(z.object({
      tenantId: z.string(),
      fieldName: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      // 验证用户权限
      await validateTenantAccess(ctx, input.tenantId);

      const usageCount = await checkFieldUsage(ctx, input.tenantId, input.fieldName);

      return { usageCount };
    }),
});

// 根据字段名推断类别
function inferFieldCategory(fieldName: string): "customer" | "document" {
  const customerKeywords = ["客户", "customer", "用户", "user", "联系", "contact", "等级", "level", "来源", "source"];
  const documentKeywords = ["证件", "document", "认证", "cert", "机构", "authority", "备注", "note", "续期", "renewal"];
  
  const lowerFieldName = fieldName.toLowerCase();
  
  if (customerKeywords.some(keyword => lowerFieldName.includes(keyword))) {
    return "customer";
  }
  
  if (documentKeywords.some(keyword => lowerFieldName.includes(keyword))) {
    return "document";
  }
  
  // 默认归类为客户字段
  return "customer";
}

// 根据值推断字段类型
function inferFieldType(value: any): "text" | "number" | "date" | "textarea" {
  if (typeof value === "number") {
    return "number";
  }
  
  if (value instanceof Date || (typeof value === "string" && !isNaN(Date.parse(value)))) {
    return "date";
  }
  
  if (typeof value === "string") {
    // 如果文本很长，推断为多行文本
    if (value.length > 50 || value.includes('\n')) {
      return "textarea";
    }
    return "text";
  }
  
  return "text";
}
