import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { NotificationService } from "~/server/services/notification-service";

// 商户通知配置输入验证
const TenantNotificationConfigSchema = z.object({
  tenantId: z.string().optional(),

  // 通知渠道开关
  emailEnabled: z.boolean().default(true),
  telegramEnabled: z.boolean().default(false),
  pushEnabled: z.boolean().default(true),
  browserNotificationEnabled: z.boolean().default(true),

  // 商户Telegram配置
  telegramChatId: z.string().nullable().optional(),

  // 通知邮箱配置
  notificationEmail: z.string().email().nullable().optional(),

  // 通知类型开关
  documentExpiry: z.boolean().default(true),
  documentReminder: z.boolean().default(true),

  // 提醒时间配置
  reminderDays: z.array(z.number()).default([30, 7, 1]),
});

// PWA推送订阅验证
const PushSubscriptionSchema = z.object({
  endpoint: z.string(),
  keys: z.object({
    p256dh: z.string(),
    auth: z.string(),
  }),
});

// Telegram绑定验证
const TelegramBindingSchema = z.object({
  chatId: z.string(),
  username: z.string().optional(),
});

export const notificationConfigRouter = createTRPCRouter({
  // 获取可用的通知方式
  getAvailableMethods: protectedProcedure
    .query(async ({ ctx }) => {
      // 获取系统配置
      const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      const defaultConfig = {
        emailEnabled: true,
        telegramEnabled: true,
        pushEnabled: true,
        browserNotificationEnabled: true,
      };

      const config = systemConfig || defaultConfig;

      return {
        email: config.emailEnabled,
        telegram: config.telegramEnabled,
        push: config.pushEnabled,
        browserNotification: config.browserNotificationEnabled,
      };
    }),

  // 获取商户通知配置
  getConfig: protectedProcedure
    .input(z.object({ tenantId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // 获取用户信息
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          currentTenantId: true,
          role: true,
          memberships: {
            select: {
              tenantId: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "用户不存在",
        });
      }

      // 超级管理员特殊处理
      if (user.role === "SUPER_ADMIN") {
        // 超级管理员返回默认配置或系统配置
        return {
          emailEnabled: false,
          telegramEnabled: false,
          pushEnabled: false,
          telegramChatId: "",
          reminderDays: 7,
          notificationTypes: ["EXPIRY_REMINDER"],
        };
      }

      // 确定要查询的商户ID
      let tenantId = input.tenantId || user.currentTenantId;

      // 如果没有currentTenantId，尝试从成员关系中获取第一个商户ID
      if (!tenantId && user.memberships.length > 0) {
        tenantId = user.memberships[0]?.tenantId;
      }

      if (!tenantId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "未找到关联的商户，请联系管理员",
        });
      }

      // 验证用户权限
      if (user.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId,
            tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该商户的通知配置",
          });
        }
      }

      // 获取商户的通知配置
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: tenantId },
        select: {
          telegramChatId: true,
          notificationSettings: true,
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      // 默认配置
      const defaultConfig = {
        emailEnabled: true,
        telegramEnabled: false,
        pushEnabled: true,
        browserNotificationEnabled: true,
        documentExpiry: true,
        documentReminder: true,
        reminderDays: [30, 7, 1],
      };

      // 获取系统配置，检查哪些通知方式被系统禁用
      const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      const systemDefaults = {
        emailEnabled: true,
        telegramEnabled: true,
        pushEnabled: true,
        browserNotificationEnabled: true,
      };

      const systemSettings = systemConfig || systemDefaults;

      // 合并商户配置，但要考虑系统级别的禁用设置
      const settings = tenant.notificationSettings as any || {};
      const config = {
        ...defaultConfig,
        ...settings,
        // 应用系统级别的禁用设置
        emailEnabled: (settings.emailEnabled ?? defaultConfig.emailEnabled) && systemSettings.emailEnabled,
        telegramEnabled: (settings.telegramEnabled ?? defaultConfig.telegramEnabled) && systemSettings.telegramEnabled,
        pushEnabled: (settings.pushEnabled ?? defaultConfig.pushEnabled) && systemSettings.pushEnabled,
        browserNotificationEnabled: (settings.browserNotificationEnabled ?? defaultConfig.browserNotificationEnabled) && systemSettings.browserNotificationEnabled,
        telegramChatId: tenant.telegramChatId,
        hasTelegramConfig: !!tenant.telegramChatId,
        // 通知邮箱配置，默认使用登录用户邮箱
        notificationEmail: settings.notificationEmail || user.email,
        tenantId,
      };

      return config;
    }),

  // 更新商户通知配置
  updateConfig: protectedProcedure
    .input(TenantNotificationConfigSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { tenantId: inputTenantId, telegramChatId, notificationEmail, ...settings } = input;

      // 获取系统配置，检查哪些通知方式被系统禁用
      const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      const systemDefaults = {
        emailEnabled: true,
        telegramEnabled: true,
        pushEnabled: true,
        browserNotificationEnabled: true,
      };

      const systemSettings = systemConfig || systemDefaults;

      // 获取用户信息
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          currentTenantId: true,
          role: true,
          memberships: {
            select: {
              tenantId: true,
              role: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "用户不存在",
        });
      }

      // 确定要更新的商户ID
      let tenantId = inputTenantId || user.currentTenantId;

      // 如果没有currentTenantId，尝试从成员关系中获取第一个商户ID
      if (!tenantId && user.memberships.length > 0) {
        tenantId = user.memberships[0]?.tenantId;
      }

      if (!tenantId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "未找到关联的商户，请联系管理员",
        });
      }

      // 验证用户权限（只有商户管理员可以更新配置）
      if (user.role !== "SUPER_ADMIN") {
        const membership = user.memberships.find(
          m => m.tenantId === tenantId
        );

        // 超级管理员特殊处理
        if (user.role === "SUPER_ADMIN") {
          // 超级管理员不需要更新商户通知配置，直接返回成功
          return {
            success: true,
            message: "超级管理员请使用系统通知配置页面",
          };
        }

        console.log('用户权限检查:', {
          userId,
          tenantId,
          userRole: user.role,
          membership: membership,
          membershipRole: membership?.role,
          allMemberships: user.memberships,
          allowedRoles: ["ADMIN", "MEMBER", "TENANT_ADMIN", "TENANT_MEMBER"],
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该商户",
          });
        }

        // 检查是否为管理员角色（只有管理员可以更新通知配置）
        if (!["ADMIN", "TENANT_ADMIN"].includes(membership.role)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "只有商户管理员可以设置通知配置",
          });
        }
      }

      // 获取现有配置
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: tenantId },
        select: { notificationSettings: true },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      // 合并配置，但要确保系统禁用的通知方式在商户配置中也被禁用
      const currentSettings = (tenant.notificationSettings as any) || {};
      const updatedSettings = {
        ...currentSettings,
        ...settings,
        // 强制应用系统级别的禁用设置
        emailEnabled: settings.emailEnabled && systemSettings.emailEnabled,
        telegramEnabled: settings.telegramEnabled && systemSettings.telegramEnabled,
        pushEnabled: settings.pushEnabled && systemSettings.pushEnabled,
        browserNotificationEnabled: settings.browserNotificationEnabled && systemSettings.browserNotificationEnabled,
        // 通知邮箱配置
        notificationEmail: notificationEmail,
      };

      // 更新商户配置
      console.log('更新通知配置:', {
        tenantId,
        telegramChatId,
        updatedSettings,
      });

      await ctx.db.tenant.update({
        where: { id: tenantId },
        data: {
          telegramChatId,
          notificationSettings: updatedSettings,
        },
      });

      console.log('通知配置更新成功');

      return {
        success: true,
        message: "通知配置更新成功",
      };
    }),

  // 订阅PWA推送
  subscribePush: protectedProcedure
    .input(PushSubscriptionSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // 检查是否已存在相同的订阅
      const existingSubscription = await ctx.db.pushSubscription.findFirst({
        where: {
          userId,
          endpoint: input.endpoint,
        },
      });

      if (existingSubscription) {
        return {
          success: true,
          message: "推送订阅已存在",
        };
      }

      // 创建新的推送订阅
      await ctx.db.pushSubscription.create({
        data: {
          userId,
          endpoint: input.endpoint,
          p256dh: input.keys.p256dh,
          auth: input.keys.auth,
        },
      });

      return {
        success: true,
        message: "推送订阅成功",
      };
    }),

  // 取消PWA推送订阅
  unsubscribePush: protectedProcedure
    .input(z.object({ endpoint: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      await ctx.db.pushSubscription.deleteMany({
        where: {
          userId,
          endpoint: input.endpoint,
        },
      });

      return {
        success: true,
        message: "推送订阅已取消",
      };
    }),



  // 获取VAPID公钥 (用于PWA推送订阅)
  getVapidPublicKey: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
          orderBy: { createdAt: "desc" },
          select: {
            vapidPublicKey: true,
          },
        });

        return {
          success: true,
          vapidPublicKey: systemConfig?.vapidPublicKey || null,
        };
      } catch (error) {
        console.error("获取VAPID公钥失败:", error);
        return {
          success: false,
          vapidPublicKey: null,
        };
      }
    }),

  // 测试通知发送
  testNotification: protectedProcedure
    .input(z.object({
      tenantId: z.string().optional(),
      channels: z.array(z.enum(["EMAIL", "TELEGRAM", "PWA_PUSH", "BROWSER_NOTIFICATION"])),
      message: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // 获取用户信息
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        include: {
          memberships: {
            select: {
              tenantId: true,
            },
          },
          pushSubscriptions: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "用户不存在",
        });
      }

      // 确定商户ID
      let tenantId = input.tenantId || user.currentTenantId;

      // 如果没有currentTenantId，尝试从成员关系中获取第一个商户ID
      if (!tenantId && user.memberships.length > 0) {
        tenantId = user.memberships[0]?.tenantId;
      }

      if (!tenantId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "未找到关联的商户，请联系管理员",
        });
      }

      // 获取商户配置
      const tenant = await ctx.db.tenant.findUnique({
        where: { id: tenantId },
        select: {
          telegramChatId: true,
          notificationSettings: true,
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "商户不存在",
        });
      }

      const testMessage = input.message || "这是一条测试通知";
      const results: any[] = [];

      // 获取商户通知配置
      const tenantSettings = (tenant.notificationSettings as any) || {};
      const notificationEmail = tenantSettings.notificationEmail || user.email;

      // 发送测试通知到各个渠道
      for (const channel of input.channels) {
        try {
          switch (channel) {
            case "EMAIL":
              if (notificationEmail) {
                try {
                  // 调用邮件发送服务
                  await NotificationService.testEmailSend(notificationEmail, "测试通知", testMessage);
                  results.push({
                    channel: "EMAIL",
                    success: true,
                    message: `测试邮件已发送到 ${notificationEmail}`,
                  });
                } catch (error) {
                  results.push({
                    channel: "EMAIL",
                    success: false,
                    message: `邮件发送失败: ${error}`,
                  });
                }
              } else {
                results.push({
                  channel: "EMAIL",
                  success: false,
                  message: "通知邮箱未设置",
                });
              }
              break;

            case "TELEGRAM":
              if (tenant.telegramChatId) {
                try {
                  // 调用Telegram发送服务
                  await NotificationService.testTelegramSend(tenant.telegramChatId, "测试通知", testMessage);
                  results.push({
                    channel: "TELEGRAM",
                    success: true,
                    message: `测试消息已发送到 Telegram (${tenant.telegramChatId})`,
                  });
                } catch (error) {
                  results.push({
                    channel: "TELEGRAM",
                    success: false,
                    message: `Telegram发送失败: ${error}`,
                  });
                }
              } else {
                results.push({
                  channel: "TELEGRAM",
                  success: false,
                  message: "商户Telegram ID未配置",
                });
              }
              break;

            case "PWA_PUSH":
              if (user.pushSubscriptions.length > 0) {
                // TODO: 实现PWA推送
                results.push({
                  channel: "PWA_PUSH",
                  success: true,
                  message: `推送通知已发送到 ${user.pushSubscriptions.length} 个设备`,
                });
              } else {
                results.push({
                  channel: "PWA_PUSH",
                  success: false,
                  message: "未找到推送订阅",
                });
              }
              break;

            case "BROWSER_NOTIFICATION":
              // 浏览器通知测试（本地通知）
              results.push({
                channel: "BROWSER_NOTIFICATION",
                success: true,
                message: "浏览器通知测试完成 - 请检查浏览器是否显示通知",
              });
              break;
          }
        } catch (error) {
          results.push({
            channel,
            success: false,
            message: `发送失败: ${error}`,
          });
        }
      }

      return {
        success: true,
        results,
        message: "测试通知发送完成",
      };
    }),

  // 获取通知历史
  getNotifications: protectedProcedure
    .input(z.object({
      tenantId: z.string().optional(),
      page: z.number().default(1),
      limit: z.number().default(20),
      type: z.enum(["DOCUMENT_EXPIRY", "DOCUMENT_REMINDER"]).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { page, limit, type, tenantId } = input;

      const where: any = {
        createdById: userId,
      };

      if (tenantId) {
        where.tenantId = tenantId;
      }

      if (type) {
        where.type = type;
      }

      const [notifications, total] = await Promise.all([
        ctx.db.notification.findMany({
          where,
          include: {
            document: {
              select: {
                id: true,
                customerName: true,
                certType: true,
                validUntil: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        }),
        ctx.db.notification.count({ where }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),
});
