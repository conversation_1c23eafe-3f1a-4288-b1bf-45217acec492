import { z } from "zod";
import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 通知类型枚举
const NotificationTypeSchema = z.enum([
  "EXPIRY_WARNING",
  "EXPIRY_ALERT", 
  "DOCUMENT_ADDED",
  "DOCUMENT_UPDATED",
  "SYSTEM",
]);

// 优先级枚举
const NotificationPrioritySchema = z.enum([
  "LOW",
  "MEDIUM",
  "HIGH",
  "URGENT",
]);

// 创建通知输入验证
const CreateNotificationSchema = z.object({
  tenantId: z.string(),
  title: z.string().min(1, "标题不能为空"),
  message: z.string().min(1, "消息内容不能为空"),
  type: NotificationTypeSchema,
  priority: NotificationPrioritySchema.default("MEDIUM"),
  documentId: z.string().optional(),
});

// 更新通知输入验证
const UpdateNotificationSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  title: z.string().min(1, "标题不能为空").optional(),
  message: z.string().min(1, "消息内容不能为空").optional(),
  type: NotificationTypeSchema.optional(),
  priority: NotificationPrioritySchema.optional(),
  isRead: z.boolean().optional(),
});

// 获取通知列表输入验证
const GetNotificationsSchema = z.object({
  tenantId: z.string(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  isRead: z.boolean().optional(),
  type: NotificationTypeSchema.optional(),
  priority: NotificationPrioritySchema.optional(),
});

// 标记为已读输入验证
const MarkAsReadSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
});

// 删除通知输入验证
const DeleteNotificationSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
});

export const notificationRouter = createTRPCRouter({
  // 获取最近通知
  getRecent: protectedProcedure
    .input(z.object({
      tenantId: z.string(),
      limit: z.number().default(5)
    }))
    .query(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该商户",
          });
        }
      }

      // 获取真实的通知数据
      const notifications = await ctx.db.notification.findMany({
        where: {
          tenantId: input.tenantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: input.limit,
        select: {
          id: true,
          title: true,
          message: true,
          type: true,
          isRead: true,
          createdAt: true,
          document: {
            select: {
              customerName: true,
              certType: true,
            },
          },
        },
      });

      return notifications;
    }),
  // 创建通知
  create: protectedProcedure
    .input(CreateNotificationSchema)
    .mutation(async ({ ctx, input }) => {
      const { tenantId, documentId, ...notificationData } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      // 如果指定了 documentId，验证证件是否存在且属于该租户
      if (documentId) {
        const document = await ctx.db.document.findFirst({
          where: {
            id: documentId,
            tenantId,
          },
        });

        if (!document) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "指定的证件不存在",
          });
        }
      }

      // 创建通知
      const notification = await ctx.db.notification.create({
        data: {
          ...notificationData,
          tenantId,
          documentId,
          createdById: ctx.session.user.id,
        },
        include: {
          document: {
            select: {
              id: true,
              customerName: true,
              certNumber: true,
              certType: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return notification;
    }),

  // 获取通知列表
  getAll: protectedProcedure
    .input(GetNotificationsSchema)
    .query(async ({ ctx, input }) => {
      const { tenantId, page, limit, isRead, type, priority } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      const where = {
        tenantId,
        ...(isRead !== undefined && { isRead }),
        ...(type && { type }),
        ...(priority && { priority }),
      };

      const [notifications, total] = await Promise.all([
        ctx.db.notification.findMany({
          where,
          include: {
            document: {
              select: {
                id: true,
                customerName: true,
                certNumber: true,
                certType: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: [
            { isRead: 'asc' }, // 未读的在前
            { priority: 'desc' }, // 高优先级在前
            { createdAt: 'desc' }, // 最新的在前
          ],
          skip: (page - 1) * limit,
          take: limit,
        }),
        ctx.db.notification.count({ where }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取单个通知详情
  getById: protectedProcedure
    .input(z.object({ id: z.string(), tenantId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { id, tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      const notification = await ctx.db.notification.findFirst({
        where: {
          id,
          tenantId,
        },
        include: {
          document: {
            select: {
              id: true,
              customerName: true,
              certNumber: true,
              certType: true,
              validUntil: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!notification) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "通知不存在",
        });
      }

      return notification;
    }),

  // 更新通知
  update: protectedProcedure
    .input(UpdateNotificationSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, tenantId, ...updateData } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      // 验证通知是否存在
      const existingNotification = await ctx.db.notification.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingNotification) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "通知不存在",
        });
      }

      // 更新通知
      const notification = await ctx.db.notification.update({
        where: { id },
        data: updateData,
        include: {
          document: {
            select: {
              id: true,
              customerName: true,
              certNumber: true,
              certType: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return notification;
    }),

  // 标记为已读
  markAsRead: protectedProcedure
    .input(MarkAsReadSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      // 验证通知是否存在
      const existingNotification = await ctx.db.notification.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingNotification) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "通知不存在",
        });
      }

      // 标记为已读
      const notification = await ctx.db.notification.update({
        where: { id },
        data: { 
          isRead: true,
          readAt: new Date(),
        },
      });

      return notification;
    }),

  // 批量标记为已读
  markAllAsRead: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      // 批量标记为已读
      const result = await ctx.db.notification.updateMany({
        where: {
          tenantId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      return { count: result.count };
    }),

  // 删除通知
  delete: protectedProcedure
    .input(DeleteNotificationSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      // 验证通知是否存在
      const existingNotification = await ctx.db.notification.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingNotification) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "通知不存在",
        });
      }

      // 删除通知
      await ctx.db.notification.delete({
        where: { id },
      });

      return { success: true };
    }),

  // 获取未读通知数量
  getUnreadCount: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      const count = await ctx.db.notification.count({
        where: {
          tenantId,
          isRead: false,
        },
      });

      return { count };
    }),

  // 获取通知统计信息
  getStats: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { tenantId } = input;

      // 验证用户是否属于该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问此租户",
        });
      }

      const [total, unread, byType, byPriority] = await Promise.all([
        // 总通知数
        ctx.db.notification.count({
          where: { tenantId },
        }),
        // 未读通知数
        ctx.db.notification.count({
          where: { tenantId, isRead: false },
        }),
        // 按类型分组
        ctx.db.notification.groupBy({
          by: ['type'],
          where: { tenantId },
          _count: { type: true },
        }),
        // 按优先级分组
        ctx.db.notification.groupBy({
          by: ['priority'],
          where: { tenantId },
          _count: { priority: true },
        }),
      ]);

      return {
        total,
        unread,
        read: total - unread,
        byType: byType.reduce((acc, item) => {
          acc[item.type] = item._count.type;
          return acc;
        }, {} as Record<string, number>),
        byPriority: byPriority.reduce((acc, item) => {
          acc[item.priority] = item._count.priority;
          return acc;
        }, {} as Record<string, number>),
      };
    }),
});