import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

export const systemRouter = createTRPCRouter({
  // 获取系统统计信息
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      // 获取商户统计
      const tenants = await ctx.db.tenant.findMany();
      const thisMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      const newTenantsThisMonth = tenants.filter(tenant =>
        tenant.createdAt >= thisMonth
      ).length;

      // 获取用户统计
      const users = await ctx.db.user.findMany();
      const totalUsers = users.length;

      // 获取证件统计
      const documents = await ctx.db.document.findMany();
      const totalDocuments = documents.length;

      // 计算系统活跃度（简单的模拟算法）
      const activityScore = Math.min(100, Math.round(
        (newTenantsThisMonth * 10 + totalUsers * 2 + totalDocuments * 0.1) / 10
      ));

      return {
        totalTenants: tenants.length,
        newTenantsThisMonth,
        totalUsers,
        totalDocuments,
        activityScore,
      };
    }),
});
