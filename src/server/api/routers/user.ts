import { z } from "zod";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

// 更新个人信息验证
const UpdateProfileSchema = z.object({
  name: z.string().min(1, "姓名不能为空").optional(),
  phone: z.string().optional(),
});

// 修改密码验证
const ChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, "请输入当前密码"),
  newPassword: z.string().min(6, "新密码长度至少6位"),
});

export const userRouter = createTRPCRouter({
  // 获取用户个人信息
  getProfile: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "用户不存在",
        });
      }

      return user;
    }),

  // 更新个人信息
  updateProfile: protectedProcedure
    .input(UpdateProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const updatedUser = await ctx.db.user.update({
        where: { id: userId },
        data: {
          name: input.name,
          phone: input.phone,
        },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
        },
      });

      return {
        success: true,
        user: updatedUser,
        message: "个人信息更新成功",
      };
    }),

  // 修改密码
  changePassword: protectedProcedure
    .input(ChangePasswordSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // 获取用户当前密码
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          password: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "用户不存在",
        });
      }

      // 检查用户是否设置了密码
      if (!user.password) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "用户未设置密码，无法修改",
        });
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(
        input.currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "当前密码不正确",
        });
      }

      // 哈希新密码
      const hashedNewPassword = await bcrypt.hash(input.newPassword, 12);

      // 更新密码
      await ctx.db.user.update({
        where: { id: userId },
        data: {
          password: hashedNewPassword,
        },
      });

      return {
        success: true,
        message: "密码修改成功",
      };
    }),

  // 更新头像
  updateAvatar: protectedProcedure
    .input(z.object({
      imageUrl: z.string().url("请提供有效的图片URL"),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const updatedUser = await ctx.db.user.update({
        where: { id: userId },
        data: {
          image: input.imageUrl,
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      });

      return {
        success: true,
        user: updatedUser,
        message: "头像更新成功",
      };
    }),

  // 获取用户统计信息
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;
      const tenantId = ctx.session.user.currentTenantId;

      if (!tenantId) {
        return {
          documentsCount: 0,
          notificationsCount: 0,
          expiringDocumentsCount: 0,
        };
      }

      // 获取用户创建的证件数量
      const documentsCount = await ctx.db.document.count({
        where: {
          createdById: userId,
          tenantId,
        },
      });

      // 获取用户的通知数量
      const notificationsCount = await ctx.db.notification.count({
        where: {
          tenantId,
          isRead: false,
        },
      });

      // 获取即将到期的证件数量（30天内）
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      const expiringDocumentsCount = await ctx.db.document.count({
        where: {
          tenantId,
          validUntil: {
            lte: thirtyDaysFromNow,
            gte: new Date(),
          },
        },
      });

      return {
        documentsCount,
        notificationsCount,
        expiringDocumentsCount,
      };
    }),
});
