import { z } from "zod";
import { TRPCError } from "@trpc/server";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";
import { ActivityLogService } from "~/server/services/activity-log-service";

// 证件类型验证 - 改为字符串以支持动态类型
const DocumentTypeSchema = z.string().min(1, "证件类型不能为空");

// 创建证件的输入验证
const CreateDocumentSchema = z.object({
  tenantId: z.string().min(1),
  customerName: z.string().min(1, "客户姓名不能为空"),
  phone: z.string().optional(),
  email: z.string().optional(),
  address: z.string().optional(),
  company: z.string().optional(),
  certType: DocumentTypeSchema,
  certNumber: z.string().optional(),
  issueDate: z.date().optional(),
  expiryDate: z.date(),
  reminderDate: z.date().optional(),
  customFields: z.record(z.any()).optional(),
});

// 更新证件的输入验证
const UpdateDocumentSchema = CreateDocumentSchema.partial().extend({
  id: z.string().min(1),
});

// 查询证件的输入验证
const GetDocumentsSchema = z.object({
  tenantId: z.string().nullable(), // 允许超级管理员传递 null
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  certType: DocumentTypeSchema.optional(),
  expiryStatus: z.enum(["ALL", "EXPIRING_SOON", "EXPIRED", "ACTIVE"]).default("ALL"),
  sortBy: z.enum(["validUntil", "createdAt", "customerName"]).default("validUntil"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export const documentRouter = createTRPCRouter({
  // 获取统计信息
  getStats: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .query(async ({ ctx, input }) => {
      const documents = await ctx.db.document.findMany({
        where: { tenantId: input.tenantId },
      });

      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const total = documents.length;
      const thisMonthCount = documents.filter(doc =>
        doc.createdAt >= thisMonth
      ).length;

      const valid = documents.filter(doc =>
        doc.validUntil && new Date(doc.validUntil) > now
      ).length;

      const expired = documents.filter(doc =>
        doc.validUntil && new Date(doc.validUntil) <= now
      ).length;

      return {
        total,
        thisMonth: thisMonthCount,
        valid,
        expired,
      };
    }),

  // 获取即将到期的证件
  getExpiringSoon: protectedProcedure
    .input(z.object({ tenantId: z.string(), days: z.number().default(30) }))
    .query(async ({ ctx, input }) => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + input.days);

      return await ctx.db.document.findMany({
        where: {
          tenantId: input.tenantId,
          validUntil: {
            gte: new Date(),
            lte: futureDate,
          },
        },
        orderBy: {
          validUntil: 'asc',
        },
      });
    }),

  // 获取证件类型统计
  getTypeStats: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .query(async ({ ctx, input }) => {
      const documents = await ctx.db.document.findMany({
        where: { tenantId: input.tenantId },
        select: {
          certType: true,
        },
      });

      const typeMap = new Map<string, { name: string; count: number }>();

      documents.forEach(doc => {
        const typeName = doc.certType || '未分类';
        const current = typeMap.get(typeName) || { name: typeName, count: 0 };
        current.count++;
        typeMap.set(typeName, current);
      });

      const total = documents.length;
      return Array.from(typeMap.values()).map(item => ({
        typeName: item.name,
        count: item.count,
        percentage: total > 0 ? (item.count / total) * 100 : 0,
      }));
    }),

  // 获取到期趋势数据
  getExpirationTrend: protectedProcedure
    .input(z.object({ tenantId: z.string(), months: z.number().default(12) }))
    .query(async ({ ctx, input }) => {
      const documents = await ctx.db.document.findMany({
        where: { tenantId: input.tenantId },
      });

      const now = new Date();
      const trends = [];

      for (let i = input.months - 1; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

        const monthName = monthStart.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short'
        });

        const expiringCount = documents.filter(doc =>
          doc.validUntil &&
          new Date(doc.validUntil) >= monthStart &&
          new Date(doc.validUntil) <= monthEnd
        ).length;

        const expiredCount = documents.filter(doc =>
          doc.validUntil &&
          new Date(doc.validUntil) < monthStart
        ).length;

        trends.push({
          month: monthName,
          expiringCount,
          expiredCount,
          totalCount: documents.length,
        });
      }

      return trends;
    }),

  // 获取操作记录
  getActivityLogs: protectedProcedure
    .input(z.object({ tenantId: z.string(), limit: z.number().default(10) }))
    .query(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该商户",
          });
        }
      }

      // 获取真实的操作记录数据
      const activityLogs = await ctx.db.activityLog.findMany({
        where: {
          tenantId: input.tenantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: input.limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          document: {
            select: {
              id: true,
              customerName: true,
              certType: true,
            },
          },
        },
      });

      return activityLogs;
    }),
  // 创建证件
  create: protectedProcedure
    .input(CreateDocumentSchema)
    .mutation(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该商户",
          });
        }
      }

      // 验证证件类型是否存在
      const documentType = await ctx.db.documentType.findFirst({
        where: {
          tenantId: input.tenantId,
          name: input.certType, // 使用name字段而不是code
          isActive: true,
        },
      });

      if (!documentType) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "证件类型不存在或已被禁用",
        });
      }

      // 如果提供了证件号码，检查是否已存在
      if (input.certNumber) {
        const existingDocument = await ctx.db.document.findFirst({
          where: {
            tenantId: input.tenantId,
            certNumber: input.certNumber,
            certType: input.certType,
          },
        });

        if (existingDocument) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "该证件号码已存在",
          });
        }
      }

      // 合并自定义字段和系统字段
      const allCustomFields = {
        ...(input.customFields || {}),
        ...(input.issueDate && { issueDate: input.issueDate.toISOString() }),
        ...(input.reminderDate && { reminderDate: input.reminderDate.toISOString() }),
      };

      // 从自定义字段中提取邮箱、地址、公司信息
      let extractedEmail = input.email;
      let extractedAddress = input.address;
      let extractedCompany = input.company;

      if (input.customFields) {
        // 提取邮箱地址
        if (input.customFields['邮箱地址']) {
          extractedEmail = input.customFields['邮箱地址'];
        }

        // 提取地址
        if (input.customFields['地址']) {
          extractedAddress = input.customFields['地址'];
        }

        // 提取公司名称
        if (input.customFields['公司名称']) {
          extractedCompany = input.customFields['公司名称'];
        }
      }

      const document = await ctx.db.document.create({
        data: {
          tenantId: input.tenantId,
          customerName: input.customerName,
          phone: input.phone || null,
          email: extractedEmail || null,
          address: extractedAddress || null,
          company: extractedCompany || null,
          certType: input.certType,
          certNumber: input.certNumber || null,
          validUntil: input.expiryDate,
          issueBy: input.issueDate ? `签发日期: ${input.issueDate.toISOString().split('T')[0]}` : null,
          customFields: Object.keys(allCustomFields).length > 0 ? allCustomFields as any : undefined,
          createdById: ctx.session.user.id,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // 记录操作日志
      await ActivityLogService.logDocumentAction(
        "创建",
        document.id,
        {
          customerName: document.customerName,
          certType: document.certType,
        },
        ctx.session.user.id,
        input.tenantId
      );

      return document;
    }),

  // 获取证件列表
  getAll: protectedProcedure
    .input(GetDocumentsSchema)
    .query(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果 tenantId 为 null，只有超级管理员可以访问
      if (!input.tenantId) {
        if (user?.role !== "SUPER_ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "请选择一个租户",
          });
        }
        // 超级管理员没有选择租户时，返回空结果并提示选择租户
        return {
          documents: [],
          pagination: {
            page: input.page,
            limit: input.limit,
            total: 0,
            totalPages: 0,
          },
          message: "请先选择一个租户来查看证件数据",
        };
      }

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      const { page, limit, search, certType, expiryStatus, sortBy, sortOrder } = input;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {
        tenantId: input.tenantId,
      };

      // 搜索条件
      if (search) {
        where.OR = [
          { customerName: { contains: search, mode: "insensitive" } },
          { certNumber: { contains: search, mode: "insensitive" } },
          { phone: { contains: search, mode: "insensitive" } },
          { email: { contains: search, mode: "insensitive" } },
        ];
      }

      // 证件类型过滤
      if (certType) {
        where.certType = certType;
      }

      // 到期状态过滤
      const now = new Date();
      const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      switch (expiryStatus) {
        case "EXPIRED":
          where.validUntil = { lt: now };
          break;
        case "EXPIRING_SOON":
          where.validUntil = { gte: now, lte: thirtyDaysFromNow };
          break;
        case "ACTIVE":
          where.validUntil = { gt: thirtyDaysFromNow };
          break;
        // "ALL" 不添加额外条件
      }

      // 排序
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [documents, total] = await Promise.all([
        ctx.db.document.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            createdBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        }),
        ctx.db.document.count({ where }),
      ]);

      return {
        documents,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取单个证件
  getById: protectedProcedure
    .input(z.object({ id: z.string().min(1), tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      const document = await ctx.db.document.findFirst({
        where: {
          id: input.id,
          tenantId: input.tenantId,
        },
        include: {
          tenant: true,
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!document) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "证件不存在",
        });
      }

      return document;
    }),

  // 更新证件
  update: protectedProcedure
    .input(UpdateDocumentSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: updateData.tenantId!,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 检查证件是否存在
      const existingDocument = await ctx.db.document.findFirst({
        where: {
          id,
          tenantId: updateData.tenantId!,
        },
      });

      if (!existingDocument) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "证件不存在",
        });
      }

      // 如果更新了证件号码且不为空，检查是否与其他证件冲突
      if (updateData.certNumber && updateData.certNumber.trim() !== "" && updateData.certType) {
        const conflictDocument = await ctx.db.document.findFirst({
          where: {
            tenantId: updateData.tenantId!,
            certNumber: updateData.certNumber,
            certType: updateData.certType,
            id: { not: id },
          },
        });

        if (conflictDocument) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "该证件号码已存在",
          });
        }
      }

      // 从自定义字段中提取邮箱、地址、公司信息
      let extractedEmail = null;
      let extractedAddress = null;
      let extractedCompany = null;

      if (updateData.customFields) {
        const customFields = updateData.customFields as Record<string, any>;

        // 提取邮箱地址
        if (customFields['邮箱地址']) {
          extractedEmail = customFields['邮箱地址'];
        }

        // 提取地址
        if (customFields['地址']) {
          extractedAddress = customFields['地址'];
        }

        // 提取公司名称
        if (customFields['公司名称']) {
          extractedCompany = customFields['公司名称'];
        }
      }

      // 合并更新数据，包含从自定义字段提取的信息
      const finalUpdateData = {
        ...updateData,
        email: extractedEmail || updateData.email,
        address: extractedAddress || updateData.address,
        company: extractedCompany || updateData.company,
      };

      const updatedDocument = await ctx.db.document.update({
        where: { id },
        data: finalUpdateData,
        include: {
          tenant: true,
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // 记录操作日志
      await ActivityLogService.logDocumentAction(
        "更新",
        updatedDocument.id,
        {
          customerName: updatedDocument.customerName,
          certType: updatedDocument.certType,
        },
        ctx.session.user.id,
        updatedDocument.tenantId
      );

      return updatedDocument;
    }),

  // 删除证件
  delete: protectedProcedure
    .input(z.object({ id: z.string().min(1), tenantId: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      // 检查证件是否存在
      const existingDocument = await ctx.db.document.findFirst({
        where: {
          id: input.id,
          tenantId: input.tenantId,
        },
      });

      if (!existingDocument) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "证件不存在",
        });
      }

      return ctx.db.document.delete({
        where: { id: input.id },
      });
    }),

  // 获取统计信息
  getStats: protectedProcedure
    .input(z.object({ tenantId: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      // 获取用户信息以检查是否为超级管理员
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 如果不是超级管理员，验证用户是否有权限访问该租户
      if (user?.role !== "SUPER_ADMIN") {
        const membership = await ctx.db.membership.findFirst({
          where: {
            userId: ctx.session.user.id,
            tenantId: input.tenantId,
          },
        });

        if (!membership) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "您没有权限访问该租户",
          });
        }
      }

      const now = new Date();
      const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      const [total, expired, expiringSoon, active] = await Promise.all([
        ctx.db.document.count({
          where: { tenantId: input.tenantId },
        }),
        ctx.db.document.count({
          where: {
            tenantId: input.tenantId,
            validUntil: { lt: now },
          },
        }),
        ctx.db.document.count({
          where: {
            tenantId: input.tenantId,
            validUntil: { gte: now, lte: thirtyDaysFromNow },
          },
        }),
        ctx.db.document.count({
          where: {
            tenantId: input.tenantId,
            validUntil: { gt: thirtyDaysFromNow },
          },
        }),
      ]);

      return {
        total,
        expired,
        expiringSoon,
        active,
      };
    }),

  // 获取即将到期的证件
  getUpcomingExpiry: protectedProcedure
    .input(z.object({ 
      tenantId: z.string().min(1),
      days: z.number().min(1).max(365).default(30),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      // 验证用户是否有权限访问该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该租户",
        });
      }

      const now = new Date();
      const futureDate = new Date(now.getTime() + input.days * 24 * 60 * 60 * 1000);

      return ctx.db.document.findMany({
        where: {
          tenantId: input.tenantId,
          validUntil: {
            gte: now,
            lte: futureDate,
          },
        },
        take: input.limit,
        orderBy: {
          validUntil: "asc",
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }),

  // 获取即将到期的证件（别名方法）
  getExpiringSoon: protectedProcedure
    .input(z.object({ 
      tenantId: z.string().min(1),
      days: z.number().min(1).max(365).default(30),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      // 验证用户是否有权限访问该租户
      const membership = await ctx.db.membership.findFirst({
        where: {
          userId: ctx.session.user.id,
          tenantId: input.tenantId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限访问该租户",
        });
      }

      const now = new Date();
      const futureDate = new Date(now.getTime() + input.days * 24 * 60 * 60 * 1000);

      return ctx.db.document.findMany({
        where: {
          tenantId: input.tenantId,
          validUntil: {
            gte: now,
            lte: futureDate,
          },
        },
        take: input.limit,
        orderBy: {
          validUntil: "asc",
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }),
});