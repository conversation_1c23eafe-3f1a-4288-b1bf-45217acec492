import { db } from "~/server/db";

export interface ActivityLogData {
  action: string;
  details: string;
  entityType: string;
  entityId?: string;
  userId: string;
  tenantId: string;
  documentId?: string;
}

export class ActivityLogService {
  // 记录操作日志
  static async log(data: ActivityLogData) {
    try {
      const activityLog = await db.activityLog.create({
        data: {
          action: data.action,
          details: data.details,
          entityType: data.entityType,
          entityId: data.entityId,
          userId: data.userId,
          tenantId: data.tenantId,
          documentId: data.documentId,
        },
      });

      console.log(`📝 操作日志已记录: ${data.action} - ${data.details}`);
      return activityLog;
    } catch (error) {
      console.error("❌ 记录操作日志失败:", error);
      // 不抛出错误，避免影响主要业务流程
    }
  }

  // 记录证件相关操作
  static async logDocumentAction(
    action: "创建" | "更新" | "删除",
    documentId: string,
    documentInfo: {
      customerName: string;
      certType: string;
    },
    userId: string,
    tenantId: string
  ) {
    const actionMap = {
      创建: "创建证件",
      更新: "更新证件", 
      删除: "删除证件",
    };

    await this.log({
      action: actionMap[action],
      details: `${action}了${documentInfo.customerName}的${documentInfo.certType}`,
      entityType: "Document",
      entityId: documentId,
      userId,
      tenantId,
      documentId,
    });
  }

  // 记录用户相关操作
  static async logUserAction(
    action: "创建" | "更新" | "删除" | "登录" | "注销",
    targetUserId: string,
    userInfo: {
      name?: string;
      email?: string;
    },
    operatorUserId: string,
    tenantId: string
  ) {
    const actionMap = {
      创建: "创建用户",
      更新: "更新用户",
      删除: "删除用户",
      登录: "用户登录",
      注销: "用户注销",
    };

    const details = action === "登录" || action === "注销" 
      ? `${userInfo.name || userInfo.email}${action}`
      : `${action}了用户${userInfo.name || userInfo.email}`;

    await this.log({
      action: actionMap[action],
      details,
      entityType: "User",
      entityId: targetUserId,
      userId: operatorUserId,
      tenantId,
    });
  }

  // 记录商户相关操作
  static async logTenantAction(
    action: "创建" | "更新" | "删除" | "激活" | "禁用",
    tenantId: string,
    tenantInfo: {
      name: string;
    },
    userId: string
  ) {
    const actionMap = {
      创建: "创建商户",
      更新: "更新商户",
      删除: "删除商户",
      激活: "激活商户",
      禁用: "禁用商户",
    };

    await this.log({
      action: actionMap[action],
      details: `${action}了商户${tenantInfo.name}`,
      entityType: "Tenant",
      entityId: tenantId,
      userId,
      tenantId,
    });
  }

  // 记录系统配置操作
  static async logSystemAction(
    action: string,
    details: string,
    userId: string,
    tenantId: string
  ) {
    await this.log({
      action,
      details,
      entityType: "System",
      userId,
      tenantId,
    });
  }

  // 记录通知相关操作
  static async logNotificationAction(
    action: "发送" | "创建" | "删除",
    notificationInfo: {
      title: string;
      type?: string;
    },
    userId: string,
    tenantId: string,
    documentId?: string
  ) {
    const actionMap = {
      发送: "发送通知",
      创建: "创建通知",
      删除: "删除通知",
    };

    await this.log({
      action: actionMap[action],
      details: `${action}了通知: ${notificationInfo.title}`,
      entityType: "Notification",
      userId,
      tenantId,
      documentId,
    });
  }

  // 记录订阅相关操作
  static async logSubscriptionAction(
    action: "创建" | "更新" | "取消" | "续费",
    subscriptionInfo: {
      planName: string;
    },
    userId: string,
    tenantId: string
  ) {
    const actionMap = {
      创建: "创建订阅",
      更新: "更新订阅",
      取消: "取消订阅",
      续费: "续费订阅",
    };

    await this.log({
      action: actionMap[action],
      details: `${action}了订阅计划: ${subscriptionInfo.planName}`,
      entityType: "Subscription",
      userId,
      tenantId,
    });
  }

  // 批量清理旧日志（可选，用于定期清理）
  static async cleanupOldLogs(daysToKeep: number = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const result = await db.activityLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      console.log(`🧹 已清理 ${result.count} 条旧操作日志`);
      return result.count;
    } catch (error) {
      console.error("❌ 清理旧操作日志失败:", error);
      return 0;
    }
  }
}
