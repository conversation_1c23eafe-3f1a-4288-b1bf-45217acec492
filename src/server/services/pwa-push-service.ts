import webpush from 'web-push';
import { db } from '~/server/db';

export interface PushNotificationData {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

export class PWAPushService {
  private static isConfigured = false;

  // 初始化VAPID配置
  static async initialize() {
    if (this.isConfigured) return;

    try {
      // 获取系统配置
      const config = await db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: 'desc' },
      });

      if (config?.vapidPublicKey && config?.vapidPrivateKey && config?.vapidSubject) {
        webpush.setVapidDetails(
          config.vapidSubject,
          config.vapidPublicKey,
          config.vapidPrivateKey
        );
        this.isConfigured = true;
        console.log('✅ PWA推送服务已初始化');
      } else {
        console.log('⚠️ PWA推送配置不完整，跳过初始化');
      }
    } catch (error) {
      console.error('❌ PWA推送服务初始化失败:', error);
    }
  }

  // 发送推送通知给指定用户
  static async sendToUser(userId: string, notification: PushNotificationData) {
    await this.initialize();

    if (!this.isConfigured) {
      console.log('⚠️ PWA推送服务未配置，跳过发送');
      return { success: false, message: 'PWA推送服务未配置' };
    }

    try {
      // 获取用户的所有推送订阅
      const subscriptions = await db.pushSubscription.findMany({
        where: { userId },
      });

      if (subscriptions.length === 0) {
        console.log(`⚠️ 用户 ${userId} 没有推送订阅`);
        return { success: false, message: '用户没有推送订阅' };
      }

      const results = [];
      for (const subscription of subscriptions) {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh,
              auth: subscription.auth,
            },
          };

          const payload = JSON.stringify({
            title: notification.title,
            body: notification.body,
            icon: notification.icon || '/icon-192x192.svg',
            badge: notification.badge || '/badge-72x72.png',
            data: notification.data || {},
            actions: notification.actions || [],
            timestamp: Date.now(),
          });

          await webpush.sendNotification(pushSubscription, payload);
          results.push({ success: true, endpoint: subscription.endpoint });
          console.log(`🔔 推送通知已发送到 ${subscription.endpoint.substring(0, 50)}...`);
        } catch (error) {
          console.error(`❌ 推送发送失败 ${subscription.endpoint}:`, error);
          
          // 如果订阅无效，删除它
          if (error instanceof Error && (
            error.message.includes('410') || 
            error.message.includes('invalid') ||
            error.message.includes('expired')
          )) {
            await db.pushSubscription.delete({
              where: { id: subscription.id },
            });
            console.log(`🗑️ 已删除无效的推送订阅: ${subscription.endpoint}`);
          }
          
          results.push({
            success: false,
            endpoint: subscription.endpoint,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      return {
        success: successCount > 0,
        message: `成功发送 ${successCount}/${results.length} 个推送`,
        results,
      };
    } catch (error) {
      console.error('❌ 发送推送通知失败:', error);
      return { success: false, message: error instanceof Error ? error.message : '发送失败' };
    }
  }

  // 发送推送通知给商户的所有用户
  static async sendToTenant(tenantId: string, notification: PushNotificationData) {
    try {
      // 获取商户的所有用户
      const memberships = await db.membership.findMany({
        where: { tenantId },
        include: { user: true },
      });

      const results = [];
      for (const membership of memberships) {
        const result = await this.sendToUser(membership.userId, notification);
        results.push({
          userId: membership.userId,
          userEmail: membership.user.email,
          ...result,
        });
      }

      const successCount = results.filter(r => r.success).length;
      return {
        success: successCount > 0,
        message: `成功发送给 ${successCount}/${results.length} 个用户`,
        results,
      };
    } catch (error) {
      console.error('❌ 发送商户推送通知失败:', error);
      return { success: false, message: error instanceof Error ? error.message : '发送失败' };
    }
  }

  // 发送系统推送通知给所有商户
  static async sendSystemNotification(notification: PushNotificationData) {
    try {
      // 检查系统推送是否启用
      const config = await db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: 'desc' },
      });

      if (!config?.systemPushEnabled) {
        console.log('⚠️ 系统推送通知已被禁用');
        return { success: false, message: '系统推送通知已被禁用' };
      }

      // 获取所有活跃商户
      const tenants = await db.tenant.findMany({
        where: { isActive: true },
        select: { id: true, name: true },
      });

      const results = [];
      for (const tenant of tenants) {
        const result = await this.sendToTenant(tenant.id, notification);
        results.push({
          tenantId: tenant.id,
          tenantName: tenant.name,
          ...result,
        });
      }

      const successCount = results.filter(r => r.success).length;
      return {
        success: successCount > 0,
        message: `成功发送给 ${successCount}/${results.length} 个商户`,
        results,
      };
    } catch (error) {
      console.error('❌ 发送系统推送通知失败:', error);
      return { success: false, message: error instanceof Error ? error.message : '发送失败' };
    }
  }

  // 发送证件到期推送通知
  static async sendExpiryNotification(
    userId: string,
    documentInfo: {
      customerName: string;
      certType: string;
      validUntil: Date;
      daysUntilExpiry: number;
    }
  ) {
    try {
      // 检查证件到期推送是否启用
      const config = await db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: 'desc' },
      });

      if (!config?.expiryPushEnabled) {
        console.log('⚠️ 证件到期推送通知已被禁用');
        return { success: false, message: '证件到期推送通知已被禁用' };
      }

      const { customerName, certType, daysUntilExpiry } = documentInfo;
      
      let title: string;
      let body: string;
      
      if (daysUntilExpiry <= 0) {
        title = '证件已过期';
        body = `${customerName}的${certType}已过期，请及时处理！`;
      } else if (daysUntilExpiry === 1) {
        title = '证件明天到期';
        body = `${customerName}的${certType}将于明天到期，请及时续期！`;
      } else if (daysUntilExpiry <= 7) {
        title = '证件即将到期';
        body = `${customerName}的${certType}将于${daysUntilExpiry}天后到期，请及时续期！`;
      } else {
        title = '证件到期提醒';
        body = `${customerName}的${certType}将于${daysUntilExpiry}天后到期`;
      }

      const notification: PushNotificationData = {
        title,
        body,
        icon: '/icon-192x192.svg',
        badge: '/badge-72x72.png',
        data: {
          type: 'document_expiry',
          customerName,
          certType,
          daysUntilExpiry,
          validUntil: documentInfo.validUntil.toISOString(),
        },
        actions: [
          {
            action: 'view',
            title: '查看详情',
          },
          {
            action: 'dismiss',
            title: '忽略',
          },
        ],
      };

      return await this.sendToUser(userId, notification);
    } catch (error) {
      console.error('❌ 发送证件到期推送通知失败:', error);
      return { success: false, message: error instanceof Error ? error.message : '发送失败' };
    }
  }

  // 生成VAPID密钥对（用于初始化）
  static generateVapidKeys() {
    return webpush.generateVAPIDKeys();
  }

  // 测试推送配置
  static async testPushConfig(userId: string) {
    const notification: PushNotificationData = {
      title: '推送测试',
      body: '这是一条测试推送通知，如果您收到此消息，说明推送配置正常。',
      icon: '/icon-192x192.svg',
      data: { type: 'test' },
    };

    return await this.sendToUser(userId, notification);
  }
}
