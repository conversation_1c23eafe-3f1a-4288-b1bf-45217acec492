import { NotificationType, NotificationChannel } from "@prisma/client";
import { PWAPushService } from "./pwa-push-service";
import { TelegramBotService } from "./telegram-bot-service";
import { db } from "~/server/db";

interface NotificationData {
  tenantId: string;
  documentId: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: string;
  channels: NotificationChannel[];
  createdById: string;
}

export class NotificationService {
  // 检查即将到期的证件并发送通知
  static async checkExpiringDocuments() {
    console.log("🔍 开始检查即将到期的证件...");

    try {
      // 获取所有活跃的商户
      const tenants = await db.tenant.findMany({
        where: { isActive: true },
        include: {
          users: {
            where: { status: { in: ["TRIAL", "SUBSCRIBED"] } },
            select: {
              id: true,
              email: true,
              notificationSettings: true,
              telegramChatId: true,
              telegramUsername: true,
            },
          },
        },
      });

      for (const tenant of tenants) {
        await this.checkTenantDocuments(tenant);
      }

      console.log("✅ 证件到期检查完成");
    } catch (error) {
      console.error("❌ 证件到期检查失败:", error);
    }
  }

  // 检查单个商户的证件
  private static async checkTenantDocuments(tenant: any) {
    console.log(`🏢 检查商户 ${tenant.name} 的证件...`);

    const now = new Date();

    // 获取商户的通知配置
    const notificationSettings = (tenant.notificationSettings as any) || {};
    const tenantReminderDays = notificationSettings.reminderDays || [30, 7, 1];

    // 计算最大提醒天数，用于查询范围
    const maxReminderDays = Math.max(...tenantReminderDays, 30);
    const maxReminderDate = new Date(now.getTime() + maxReminderDays * 24 * 60 * 60 * 1000);

    // 获取即将到期的证件
    const expiringDocuments = await db.document.findMany({
      where: {
        tenantId: tenant.id,
        validUntil: {
          gte: now, // 还未过期
          lte: maxReminderDate, // 在提醒范围内
        },
      },
      orderBy: { validUntil: "asc" },
    });

    for (const document of expiringDocuments) {
      await this.processDocumentNotification(document, tenant.users, tenantReminderDays);
    }
  }

  // 处理单个证件的通知
  private static async processDocumentNotification(
    document: any,
    tenantUsers: any[],
    tenantReminderDays: number[]
  ) {
    const now = new Date();
    const daysUntilExpiry = Math.ceil(
      (document.validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 获取证件的自定义提醒时间（如果有）
    const customFields = document.customFields as any || {};
    const customReminderDate = customFields.reminderDate ? new Date(customFields.reminderDate) : null;

    // 检查是否需要发送通知
    let shouldNotify = false;
    let notificationType: NotificationType = "DOCUMENT_REMINDER";
    let priority: string = "LOW";
    let notificationReason = "";

    // 1. 检查是否已过期
    if (daysUntilExpiry <= 0) {
      shouldNotify = true;
      notificationType = "DOCUMENT_EXPIRY";
      priority = "HIGH";
      notificationReason = "证件已过期";
    }
    // 2. 检查商户统一设置的提醒周期
    else if (tenantReminderDays.includes(daysUntilExpiry)) {
      shouldNotify = true;
      notificationType = "DOCUMENT_REMINDER";
      priority = this.getPriorityByDays(daysUntilExpiry);
      notificationReason = `商户设置：${daysUntilExpiry}天提醒`;
    }
    // 3. 检查证件自定义提醒时间
    else if (customReminderDate) {
      const reminderDaysFromCustom = Math.ceil(
        (customReminderDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      // 如果今天是自定义提醒日期（允许1天误差）
      if (Math.abs(reminderDaysFromCustom) <= 1) {
        shouldNotify = true;
        notificationType = "DOCUMENT_REMINDER";
        priority = this.getPriorityByDays(daysUntilExpiry);
        notificationReason = `自定义提醒时间`;
      }
    }

    if (shouldNotify) {
      // 检查是否已经发送过相同的通知（24小时内）
      const existingNotification = await db.notification.findFirst({
        where: {
          documentId: document.id,
          type: notificationType,
          createdAt: {
            gte: new Date(now.getTime() - 24 * 60 * 60 * 1000), // 24小时内
          },
        },
      });

      if (!existingNotification) {
        console.log(`📋 发送通知: ${document.customerName} - ${document.certType} (${notificationReason})`);

        // 为每个用户发送通知
        for (const user of tenantUsers) {
          await this.sendDocumentExpiryNotification(
            document,
            user,
            { id: document.tenantId }, // 简化的tenant对象
            notificationType,
            priority,
            daysUntilExpiry
          );
        }
      }
    }
  }

  // 根据天数确定优先级
  private static getPriorityByDays(days: number): string {
    if (days <= 1) return "HIGH";
    if (days <= 7) return "MEDIUM";
    return "LOW";
  }

  // 发送证件到期通知
  private static async sendDocumentExpiryNotification(
    document: any,
    user: any,
    tenant: any,
    type: NotificationType,
    priority: string,
    daysUntilExpiry: number
  ) {
    const settings = (user.notificationSettings as any) || {};
    
    // 检查用户是否启用了相应的通知类型
    const isReminderEnabled = type === "DOCUMENT_REMINDER" ? 
      (settings.documentReminder !== false) : true;
    const isExpiryEnabled = type === "DOCUMENT_EXPIRY" ? 
      (settings.documentExpiry !== false) : true;

    if (!isReminderEnabled && !isExpiryEnabled) {
      return; // 用户禁用了此类通知
    }

    // 生成通知内容
    const { title, message } = this.generateNotificationContent(
      document,
      type,
      daysUntilExpiry
    );

    // 获取系统通知配置
    const systemConfig = await db.systemNotificationConfig.findFirst({
      orderBy: { createdAt: "desc" },
    });

    // 确定发送渠道
    const channels: NotificationChannel[] = [];

    // 邮件通知：系统启用 && 用户启用 && 用户有邮箱
    if ((systemConfig?.emailEnabled ?? true) &&
        settings.emailEnabled !== false &&
        user.email) {
      channels.push("EMAIL");
    }

    // Telegram通知：系统启用 && 用户启用 && 用户配置了ChatId
    if ((systemConfig?.telegramEnabled ?? true) &&
        settings.telegramEnabled === true &&
        user.telegramChatId) {
      channels.push("TELEGRAM");
    }

    // PWA推送：系统启用 && 用户启用
    if ((systemConfig?.pushEnabled ?? true) &&
        settings.pushEnabled !== false) {
      channels.push("PWA_PUSH");
    }

    if (channels.length === 0) {
      console.log(`⚠️ 用户 ${user.email} 没有启用任何通知渠道`);
      return;
    }

    // 创建通知记录
    const notification = await db.notification.create({
      data: {
        tenantId: tenant.id,
        documentId: document.id,
        type,
        title,
        message,
        priority,
        channels,
        createdById: user.id,
        emailSent: false,
        telegramSent: false,
        pushSent: false,
      },
    });

    // 发送到各个渠道
    await this.sendToChannels(notification, user, channels);
  }

  // 发送到各个渠道
  private static async sendToChannels(
    notification: any,
    user: any,
    channels: NotificationChannel[]
  ) {
    const updateData: any = {};

    // 获取商户的通知配置
    const tenant = await db.tenant.findUnique({
      where: { id: notification.tenantId },
      select: { notificationSettings: true },
    });

    const tenantSettings = (tenant?.notificationSettings as any) || {};
    const notificationEmail = tenantSettings.notificationEmail || user.email;

    for (const channel of channels) {
      try {
        switch (channel) {
          case "EMAIL":
            if (notificationEmail) {
              await this.sendEmail(notificationEmail, notification.title, notification.message);
              updateData.emailSent = true;
              console.log(`📧 邮件通知已发送到 ${notificationEmail}`);
            }
            break;

          case "TELEGRAM":
            if (user.telegramChatId) {
              await this.sendTelegram(user.telegramChatId, notification.title, notification.message);
              updateData.telegramSent = true;
              console.log(`📱 Telegram通知已发送到 ${user.telegramChatId}`);
            }
            break;

          case "PWA_PUSH":
            const pushResult = await PWAPushService.sendToUser(user.id, {
              title: notification.title,
              body: notification.message,
              data: {
                type: notification.type,
                notificationId: notification.id,
                documentId: notification.documentId,
              },
            });
            updateData.pushSent = pushResult.success;
            console.log(`🔔 推送通知发送结果: ${pushResult.message}`);
            break;
        }
      } catch (error) {
        console.error(`❌ 发送 ${channel} 通知失败:`, error);
        updateData.error = `${channel} 发送失败: ${error}`;
      }
    }

    // 更新通知发送状态
    await db.notification.update({
      where: { id: notification.id },
      data: updateData,
    });
  }

  // 生成通知内容
  private static generateNotificationContent(
    document: any,
    type: NotificationType,
    daysUntilExpiry: number
  ) {
    const customerName = document.customerName;
    const certType = document.certType;
    const expiryDate = document.validUntil.toLocaleDateString();

    let title: string;
    let message: string;

    if (type === "DOCUMENT_EXPIRY") {
      title = `证件已过期提醒`;
      message = `${customerName} 的 ${certType} 已于 ${expiryDate} 过期，请及时处理。`;
    } else {
      if (daysUntilExpiry <= 1) {
        title = `证件即将过期提醒`;
        message = `${customerName} 的 ${certType} 将在今天过期（${expiryDate}），请及时处理。`;
      } else {
        title = `证件到期提醒`;
        message = `${customerName} 的 ${certType} 将在 ${daysUntilExpiry} 天后过期（${expiryDate}），请提前准备。`;
      }
    }

    return { title, message };
  }

  // 发送邮件通知
  private static async sendEmail(email: string, title: string, message: string) {
    try {
      // 获取系统邮件配置
      const systemConfig = await db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      if (!systemConfig?.emailEnabled || !systemConfig?.emailFromAddress) {
        console.log(`📧 邮件服务未配置，跳过发送到 ${email}`);
        return;
      }

      // 根据配置的邮件服务商发送邮件
      switch (systemConfig.emailProvider) {
        case "GMAIL":
          await this.sendGmailEmail(systemConfig, email, title, message);
          break;
        case "OUTLOOK":
          await this.sendOutlookEmail(systemConfig, email, title, message);
          break;
        case "SMTP":
          await this.sendSmtpEmail(systemConfig, email, title, message);
          break;
        case "SENDGRID":
          await this.sendSendGridEmail(systemConfig, email, title, message);
          break;
        default:
          console.log(`📧 不支持的邮件服务商: ${systemConfig.emailProvider}`);
      }
    } catch (error) {
      console.error(`📧 邮件发送失败到 ${email}:`, error);
      throw error;
    }
  }

  // Gmail SMTP发送
  private static async sendGmailEmail(config: any, to: string, subject: string, message: string) {
    const nodemailer = await import('nodemailer');

    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: config.smtpUser || config.emailFromAddress,
        pass: config.smtpPassword, // 应用专用密码
      },
    });

    await transporter.sendMail({
      from: `"${config.emailFromName || '证件提醒系统'}" <${config.emailFromAddress}>`,
      to,
      subject,
      html: this.generateEmailTemplate(subject, message),
    });

    console.log(`📧 Gmail邮件已发送到 ${to}`);
  }

  // Outlook SMTP发送
  private static async sendOutlookEmail(config: any, to: string, subject: string, message: string) {
    const nodemailer = await import('nodemailer');

    const transporter = nodemailer.createTransport({
      host: 'smtp-mail.outlook.com',
      port: 587,
      secure: false,
      auth: {
        user: config.smtpUser || config.emailFromAddress,
        pass: config.smtpPassword,
      },
    });

    await transporter.sendMail({
      from: `"${config.emailFromName || '证件提醒系统'}" <${config.emailFromAddress}>`,
      to,
      subject,
      html: this.generateEmailTemplate(subject, message),
    });

    console.log(`📧 Outlook邮件已发送到 ${to}`);
  }

  // 通用SMTP发送
  private static async sendSmtpEmail(config: any, to: string, subject: string, message: string) {
    const nodemailer = await import('nodemailer');

    const transporter = nodemailer.createTransport({
      host: config.smtpHost,
      port: config.smtpPort || 587,
      secure: config.smtpSecure || false,
      auth: {
        user: config.smtpUser,
        pass: config.smtpPassword,
      },
    });

    await transporter.sendMail({
      from: `"${config.emailFromName || '证件提醒系统'}" <${config.emailFromAddress}>`,
      to,
      subject,
      html: this.generateEmailTemplate(subject, message),
    });

    console.log(`📧 SMTP邮件已发送到 ${to}`);
  }

  // SendGrid API发送
  private static async sendSendGridEmail(config: any, to: string, subject: string, message: string) {
    if (!config.emailApiKey) {
      throw new Error('SendGrid API Key未配置');
    }

    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.emailApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [{
          to: [{ email: to }],
          subject,
        }],
        from: {
          email: config.emailFromAddress,
          name: config.emailFromName || '证件提醒系统',
        },
        content: [{
          type: 'text/html',
          value: this.generateEmailTemplate(subject, message),
        }],
      }),
    });

    if (!response.ok) {
      throw new Error(`SendGrid API错误: ${response.status}`);
    }

    console.log(`📧 SendGrid邮件已发送到 ${to}`);
  }

  // 生成邮件HTML模板
  private static generateEmailTemplate(subject: string, message: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2196F3; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
          .alert { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔔 证件提醒通知</h1>
          </div>
          <div class="content">
            <div class="alert">
              <h2>${subject}</h2>
              <p>${message.replace(/\n/g, '<br>')}</p>
            </div>
            <p>请及时处理相关证件事务，避免因证件过期造成的不便。</p>
          </div>
          <div class="footer">
            <p>此邮件由证件管理系统自动发送，请勿回复。</p>
            <p>如有疑问，请联系系统管理员。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // 发送Telegram通知
  private static async sendTelegram(chatId: string, title: string, message: string) {
    try {
      // 使用新的TelegramBotService发送通知
      const success = await TelegramBotService.sendNotification(chatId, title, message);

      if (success) {
        console.log(`📱 Telegram消息已发送到 ${chatId}`);
      } else {
        throw new Error('Telegram消息发送失败');
      }
    } catch (error) {
      console.error(`📱 Telegram发送失败到 ${chatId}:`, error);
      throw error;
    }
  }

  // 发送PWA推送通知
  private static async sendPushNotification(userId: string, title: string, message: string) {
    // TODO: 集成Web Push API
    console.log(`🔔 模拟发送推送通知到用户 ${userId}: ${title}`);
    
    // 获取用户的推送订阅
    const subscriptions = await db.pushSubscription.findMany({
      where: { userId },
    });

    // 这里可以集成真实的Web Push服务
    // 例如使用 web-push 库:
    /*
    const webpush = require('web-push');
    
    for (const subscription of subscriptions) {
      try {
        await webpush.sendNotification(
          {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh,
              auth: subscription.auth,
            },
          },
          JSON.stringify({
            title,
            body: message,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
          })
        );
      } catch (error) {
        console.error('推送发送失败:', error);
      }
    }
    */
  }

  // 测试邮件发送（公共方法）
  static async testEmailSend(email: string, title: string, message: string) {
    return await this.sendEmail(email, title, message);
  }

  // 测试Telegram发送（公共方法）
  static async testTelegramSend(chatId: string, title: string, message: string) {
    return await this.sendTelegram(chatId, title, message);
  }

  // 手动发送通知
  static async sendNotification(data: NotificationData) {
    const notification = await db.notification.create({
      data: {
        ...data,
        emailSent: false,
        telegramSent: false,
        pushSent: false,
      },
    });

    // 获取用户信息
    const user = await db.user.findUnique({
      where: { id: data.createdById },
      select: {
        email: true,
        telegramChatId: true,
        notificationSettings: true,
      },
    });

    if (user) {
      await this.sendToChannels(notification, user, data.channels);
    }

    return notification;
  }
}
