import TelegramBot from 'node-telegram-bot-api';
import { db } from '~/server/db';

export class TelegramBotService {
  private static bot: TelegramBot | null = null;
  private static isInitialized = false;

  // 初始化机器人
  static async initialize() {
    if (this.isInitialized) return;

    try {
      // 获取系统配置中的机器人Token
      const systemConfig = await db.systemNotificationConfig.findFirst({
        orderBy: { createdAt: "desc" },
      });

      if (!systemConfig?.telegramBotToken) {
        console.log('⚠️ Telegram Bot Token 未配置，跳过机器人初始化');
        return;
      }

      // 创建机器人实例
      this.bot = new TelegramBot(systemConfig.telegramBotToken, { 
        polling: true,
        request: {
          agentOptions: {
            keepAlive: true,
            family: 4
          }
        }
      });

      // 设置机器人命令
      await this.setupCommands();

      // 设置消息处理器
      this.setupMessageHandlers();

      this.isInitialized = true;
      console.log('🤖 Telegram Bot 初始化成功');

    } catch (error) {
      console.error('❌ Telegram Bot 初始化失败:', error);
    }
  }

  // 设置机器人命令
  private static async setupCommands() {
    if (!this.bot) return;

    const commands = [
      { command: 'start', description: '开始使用机器人' },
      { command: 'id', description: '获取我的Telegram ID' },
      { command: 'help', description: '查看帮助信息' },
      { command: 'bind', description: '绑定到证件提醒系统' },
    ];

    try {
      await this.bot.setMyCommands(commands);
      console.log('✅ Telegram Bot 命令设置成功');
    } catch (error) {
      console.error('❌ 设置机器人命令失败:', error);
    }
  }

  // 设置消息处理器
  private static setupMessageHandlers() {
    if (!this.bot) return;

    // 处理 /start 命令
    this.bot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const userName = msg.from?.first_name || '用户';

      const welcomeMessage = `
🎉 欢迎使用证件提醒系统机器人！

👋 你好 ${userName}！

📋 我可以帮助你：
• 获取你的Telegram ID
• 接收证件到期提醒通知
• 绑定到证件提醒系统

🆔 你的Telegram ID: \`${chatId}\`

💡 使用方法：
1. 复制上面的ID
2. 在证件提醒系统中粘贴这个ID
3. 开启Telegram通知功能

📱 常用命令：
/id - 获取我的Telegram ID
/help - 查看帮助信息
/bind - 绑定系统账户

🔗 访问系统：[证件提醒系统](${process.env.NEXTAUTH_URL || 'https://your-domain.com'})
      `;

      await this.sendMessage(chatId, welcomeMessage);
    });

    // 处理 /id 命令
    this.bot.onText(/\/id/, async (msg) => {
      const chatId = msg.chat.id;
      const userName = msg.from?.first_name || '用户';

      const idMessage = `
🆔 你的Telegram信息：

👤 用户名: ${userName}
🔢 Chat ID: \`${chatId}\`
📱 用户ID: \`${msg.from?.id}\`

💡 使用说明：
1. 复制上面的 Chat ID: \`${chatId}\`
2. 在证件提醒系统的通知设置中粘贴这个ID
3. 保存设置后即可接收通知

⚠️ 注意：请确保这个聊天窗口保持开启，否则无法接收通知。
      `;

      await this.sendMessage(chatId, idMessage);
    });

    // 处理 /help 命令
    this.bot.onText(/\/help/, async (msg) => {
      const chatId = msg.chat.id;

      const helpMessage = `
📖 证件提醒系统机器人帮助

🤖 功能介绍：
本机器人用于发送证件到期提醒通知，帮助您及时处理即将到期的证件。

📋 可用命令：
/start - 开始使用机器人
/id - 获取我的Telegram ID
/help - 查看此帮助信息
/bind - 绑定系统账户

🔧 设置步骤：
1. 发送 /id 命令获取您的Telegram ID
2. 登录证件提醒系统
3. 进入通知设置页面
4. 将获取的ID粘贴到Telegram通知配置中
5. 开启Telegram通知功能

📞 技术支持：
如有问题，请联系系统管理员。

🔗 系统地址：${process.env.NEXTAUTH_URL || 'https://your-domain.com'}
      `;

      await this.sendMessage(chatId, helpMessage);
    });

    // 处理 /bind 命令
    this.bot.onText(/\/bind/, async (msg) => {
      const chatId = msg.chat.id;

      const bindMessage = `
🔗 绑定证件提醒系统

🆔 您的Telegram ID: \`${chatId}\`

📝 绑定步骤：
1. 登录证件提醒系统
2. 进入 "通知设置" 页面
3. 在Telegram配置中输入您的ID: \`${chatId}\`
4. 开启Telegram通知开关
5. 保存设置

✅ 绑定成功后，您将收到：
• 证件到期提醒
• 系统重要通知
• 测试消息确认

🔗 立即访问：${process.env.NEXTAUTH_URL || 'https://your-domain.com'}/notifications

💡 提示：请保持此聊天窗口开启以接收通知。
      `;

      await this.sendMessage(chatId, bindMessage);
    });

    // 处理普通文本消息
    this.bot.on('message', async (msg) => {
      // 跳过命令消息
      if (msg.text?.startsWith('/')) return;

      const chatId = msg.chat.id;
      const text = msg.text;

      if (text) {
        const responseMessage = `
👋 您好！我是证件提醒系统机器人。

🆔 您的Telegram ID: \`${chatId}\`

💡 常用命令：
/id - 获取我的Telegram ID
/help - 查看帮助信息
/bind - 绑定系统账户

📱 如需设置通知，请使用 /id 命令获取您的ID。
        `;

        await this.sendMessage(chatId, responseMessage);
      }
    });

    // 错误处理
    this.bot.on('error', (error) => {
      console.error('❌ Telegram Bot 错误:', error);
    });

    // 轮询错误处理
    this.bot.on('polling_error', (error) => {
      console.error('❌ Telegram Bot 轮询错误:', error);
    });

    console.log('✅ Telegram Bot 消息处理器设置完成');
  }

  // 发送消息
  static async sendMessage(chatId: string | number, text: string, options?: any) {
    if (!this.bot) {
      console.error('❌ Telegram Bot 未初始化');
      return false;
    }

    try {
      await this.bot.sendMessage(chatId, text, {
        parse_mode: 'Markdown',
        disable_web_page_preview: true,
        ...options
      });
      return true;
    } catch (error) {
      console.error(`❌ 发送Telegram消息失败 (${chatId}):`, error);
      return false;
    }
  }

  // 发送通知消息
  static async sendNotification(chatId: string, title: string, message: string) {
    const notificationText = `
🔔 ${title}

${message}

📅 发送时间: ${new Date().toLocaleString('zh-CN')}
    `;

    return await this.sendMessage(chatId, notificationText);
  }

  // 停止机器人
  static async stop() {
    if (this.bot) {
      await this.bot.stopPolling();
      this.bot = null;
      this.isInitialized = false;
      console.log('🛑 Telegram Bot 已停止');
    }
  }

  // 获取机器人信息
  static async getBotInfo() {
    if (!this.bot) return null;

    try {
      const me = await this.bot.getMe();
      return {
        id: me.id,
        username: me.username,
        first_name: me.first_name,
        is_bot: me.is_bot,
      };
    } catch (error) {
      console.error('❌ 获取机器人信息失败:', error);
      return null;
    }
  }
}
