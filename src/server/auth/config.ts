import { PrismaAdapter } from "@auth/prisma-adapter";
import { type DefaultSession, type NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";

import { db } from "~/server/db";
import { env } from "~/env";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      currentTenantId?: string;
      // ...other properties
      // role: UserRole;
    } & DefaultSession["user"];
  }

  // interface User {
  //   // ...other properties
  //   // role: UserRole;
  // }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  secret: env.AUTH_SECRET,
  adapter: PrismaAdapter(db),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await db.user.findUnique({
            where: { email: credentials.email as string },
            include: {
              tenant: true,
              memberships: {
                include: {
                  tenant: true,
                },
              },
            },
          });

        if (!user || !user.password) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password as string,
            user.password,
          );

        if (!isPasswordValid) {
          return null;
        }

        // 检查用户状态和商户状态
        // 超级管理员不受商户状态限制
        if (user.role !== 'SUPER_ADMIN') {
          // 检查用户的主商户状态
          if (user.tenant && !user.tenant.isActive) {
            throw new Error('TENANT_DISABLED');
          }

          // 检查用户所属的所有商户是否都被禁用
          const activeMemberships = user.memberships.filter(m => m.tenant.isActive);
          if (activeMemberships.length === 0 && user.memberships.length > 0) {
            throw new Error('ALL_TENANTS_DISABLED');
          }
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (token.sub) {
        // 获取用户的当前租户ID和角色
        const user = await db.user.findUnique({
          where: { id: token.sub },
          select: { tenantId: true, role: true },
        });

        return {
          ...session,
          user: {
            ...session.user,
            id: token.sub,
            currentTenantId: user?.tenantId || undefined,
            role: user?.role || undefined,
          },
        };
      }
      return session;
    },
    jwt: ({ token, user }) => {
      if (user) {
        token.sub = user.id;
      }
      return token;
    },
  },
} satisfies NextAuthConfig;
