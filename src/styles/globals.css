@import "tailwindcss";

@theme {
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* 隐藏默认的loading状态，防止闪烁 */
body {
  overflow-x: hidden;
}

/* 防止页面加载时的布局偏移 */
html {
  scroll-behavior: smooth;
}

/* 优化骨架屏动画性能 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 深色模式下的骨架屏 */
@media (prefers-color-scheme: dark) {
  .skeleton-shimmer {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a3a 75%);
    background-size: 200px 100%;
  }
}

/* 减少动画对性能敏感用户的影响 */
@media (prefers-reduced-motion: reduce) {
  .skeleton-shimmer {
    animation: none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
