import { useState, useEffect, useCallback } from 'react';

interface LoadingState {
  isLoading: boolean;
  showSkeleton: boolean;
  showContent: boolean;
  error?: string;
}

interface SmartLoadingOptions {
  delay?: number; // 延迟显示骨架屏的时间
  minLoadingTime?: number; // 最小加载时间
  fadeInDuration?: number; // 淡入动画时长
  retryCount?: number; // 重试次数
  retryDelay?: number; // 重试延迟
}

const defaultOptions: Required<SmartLoadingOptions> = {
  delay: 200,
  minLoadingTime: 500,
  fadeInDuration: 300,
  retryCount: 3,
  retryDelay: 1000,
};

export function useSmartLoading(
  asyncFunction: () => Promise<any>,
  dependencies: any[] = [],
  options: SmartLoadingOptions = {}
) {
  const opts = { ...defaultOptions, ...options };
  
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    showSkeleton: false,
    showContent: false,
  });
  
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  const executeWithRetry = useCallback(async (attempt = 0): Promise<any> => {
    try {
      return await asyncFunction();
    } catch (error) {
      if (attempt < opts.retryCount) {
        await new Promise(resolve => setTimeout(resolve, opts.retryDelay));
        return executeWithRetry(attempt + 1);
      }
      throw error;
    }
  }, [asyncFunction, opts.retryCount, opts.retryDelay]);

  const load = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, showContent: false, error: undefined }));
    setLoadingStartTime(Date.now());
    setRetryAttempts(0);

    // 延迟显示骨架屏
    const skeletonTimer = setTimeout(() => {
      setState(prev => ({ ...prev, showSkeleton: true }));
    }, opts.delay);

    try {
      const result = await executeWithRetry();
      
      // 确保最小加载时间
      const elapsed = Date.now() - (loadingStartTime || 0);
      const remaining = Math.max(0, opts.minLoadingTime - elapsed);
      
      setTimeout(() => {
        clearTimeout(skeletonTimer);
        setState({
          isLoading: false,
          showSkeleton: false,
          showContent: true,
        });
      }, remaining);
      
      return result;
    } catch (error) {
      clearTimeout(skeletonTimer);
      setState({
        isLoading: false,
        showSkeleton: false,
        showContent: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      });
      setRetryAttempts(opts.retryCount);
      throw error;
    }
  }, [executeWithRetry, opts.delay, opts.minLoadingTime, loadingStartTime]);

  const retry = useCallback(() => {
    if (retryAttempts < opts.retryCount) {
      setRetryAttempts(prev => prev + 1);
      load();
    }
  }, [load, retryAttempts, opts.retryCount]);

  useEffect(() => {
    load();
  }, dependencies);

  return {
    ...state,
    retry,
    canRetry: retryAttempts < opts.retryCount,
    retryAttempts,
    load,
  };
}

// 多个异步操作的智能加载
export function useMultipleSmartLoading(
  asyncFunctions: (() => Promise<any>)[],
  options: SmartLoadingOptions = {}
) {
  const opts = { ...defaultOptions, ...options };
  
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    showSkeleton: false,
    showContent: false,
  });

  const [results, setResults] = useState<any[]>([]);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  const loadAll = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, showContent: false, error: undefined }));
    setLoadingStartTime(Date.now());

    // 延迟显示骨架屏
    const skeletonTimer = setTimeout(() => {
      setState(prev => ({ ...prev, showSkeleton: true }));
    }, opts.delay);

    try {
      const allResults = await Promise.all(asyncFunctions.map(fn => fn()));
      
      // 确保最小加载时间
      const elapsed = Date.now() - (loadingStartTime || 0);
      const remaining = Math.max(0, opts.minLoadingTime - elapsed);
      
      setTimeout(() => {
        clearTimeout(skeletonTimer);
        setState({
          isLoading: false,
          showSkeleton: false,
          showContent: true,
        });
        setResults(allResults);
      }, remaining);
      
      return allResults;
    } catch (error) {
      clearTimeout(skeletonTimer);
      setState({
        isLoading: false,
        showSkeleton: false,
        showContent: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      });
      throw error;
    }
  }, [asyncFunctions, opts.delay, opts.minLoadingTime, loadingStartTime]);

  useEffect(() => {
    if (asyncFunctions.length > 0) {
      loadAll();
    }
  }, [loadAll]);

  return {
    ...state,
    results,
    loadAll,
  };
}

// 分页数据的智能加载
export function usePaginatedSmartLoading<T>(
  fetchFunction: (page: number, limit: number) => Promise<{ data: T[]; total: number }>,
  initialPage = 1,
  pageSize = 10,
  options: SmartLoadingOptions = {}
) {
  const [page, setPage] = useState(initialPage);
  const [data, setData] = useState<T[]>([]);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const loadPage = useCallback(async () => {
    const result = await fetchFunction(page, pageSize);
    return result;
  }, [fetchFunction, page, pageSize]);

  const {
    isLoading,
    showSkeleton,
    showContent,
    error,
    load,
  } = useSmartLoading(loadPage, [page], options);

  const loadMore = useCallback(async () => {
    if (hasMore && !isLoading) {
      try {
        const result = await fetchFunction(page + 1, pageSize);
        setData(prev => [...prev, ...result.data]);
        setTotal(result.total);
        setPage(prev => prev + 1);
        setHasMore(result.data.length === pageSize);
      } catch (error) {
        console.error('Failed to load more data:', error);
      }
    }
  }, [fetchFunction, page, pageSize, hasMore, isLoading]);

  const refresh = useCallback(() => {
    setPage(initialPage);
    setData([]);
    setTotal(0);
    setHasMore(true);
    load();
  }, [initialPage, load]);

  useEffect(() => {
    if (showContent) {
      // 更新数据当加载完成时
      loadPage().then(result => {
        if (page === initialPage) {
          setData(result.data);
        } else {
          setData(prev => [...prev, ...result.data]);
        }
        setTotal(result.total);
        setHasMore(result.data.length === pageSize);
      });
    }
  }, [showContent, loadPage, page, initialPage, pageSize]);

  return {
    data,
    total,
    hasMore,
    isLoading,
    showSkeleton,
    showContent,
    error,
    page,
    loadMore,
    refresh,
    setPage,
  };
}
