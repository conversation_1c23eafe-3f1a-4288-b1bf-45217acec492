import { useSession } from "next-auth/react";
import { useMemo } from "react";
import { api } from "~/trpc/react";

export interface UserPermissions {
  // 系统级权限
  isSuperAdmin: boolean;
  isTenantAdmin: boolean;
  isTenantMember: boolean;
  
  // 租户级权限
  canManageTenant: boolean;
  canDeleteTenant: boolean;
  canInviteMembers: boolean;
  canManageMembers: boolean;
  canUpdateMemberRoles: boolean;
  
  // 功能级权限
  canManageDocumentTypes: boolean;
  canManageFields: boolean;
  canViewAllTenants: boolean;
  canSwitchTenants: boolean;
  
  // 数据访问权限
  canCreateDocuments: boolean;
  canEditDocuments: boolean;
  canDeleteDocuments: boolean;
  canViewDocuments: boolean;
  
  // 当前用户信息
  currentUserRole: string | null;
  currentTenantRole: string | null;
  currentTenantId: string | null;
}

export function usePermissions(): UserPermissions {
  const { data: session } = useSession();
  
  // 获取当前租户的成员信息（如果有租户ID）
  const { data: tenants } = api.tenant.getAll.useQuery(undefined, {
    enabled: !!session?.user,
  });

  const permissions = useMemo(() => {
    if (!session?.user) {
      return getDefaultPermissions();
    }

    const userRole = session.user.role;
    const currentTenantId = session.user.currentTenantId;
    
    // 获取当前商户中的角色
    const currentTenant = tenants?.find(t => t.id === currentTenantId);
    const tenantRole = currentTenant?.userRole || null;

    // 系统级权限
    const isSuperAdmin = userRole === "SUPER_ADMIN";
    const isTenantAdmin = userRole === "TENANT_ADMIN";
    const isTenantMember = userRole === "TENANT_MEMBER";

    // 商户级权限（基于商户内角色）
    const isAdminInTenant = tenantRole === "TENANT_ADMIN";
    const isMemberInTenant = tenantRole === "TENANT_MEMBER";

    return {
      // 系统级权限
      isSuperAdmin,
      isTenantAdmin,
      isTenantMember,
      
      // 商户管理权限
      canManageTenant: isSuperAdmin || isAdminInTenant,
      canDeleteTenant: isSuperAdmin || isAdminInTenant,
      canInviteMembers: isSuperAdmin || isAdminInTenant,
      canManageMembers: isSuperAdmin || isAdminInTenant,
      canUpdateMemberRoles: isSuperAdmin || isAdminInTenant,
      
      // 功能管理权限
      canManageDocumentTypes: isTenantAdmin || isTenantMember, // 商户用户都可以管理证件类型
      canManageFields: isTenantAdmin || isTenantMember, // 商户用户都可以管理字段
      canViewAllTenants: isSuperAdmin,
      canSwitchTenants: isSuperAdmin,
      
      // 数据操作权限
      canCreateDocuments: isTenantAdmin || isTenantMember, // 只有商户用户可以创建证件
      canEditDocuments: isTenantAdmin || isTenantMember,   // 只有商户用户可以编辑证件
      canDeleteDocuments: isAdminInTenant, // 只有商户管理员可以删除
      canViewDocuments: isTenantAdmin || isTenantMember,   // 只有商户用户可以查看证件
      
      // 当前用户信息
      currentUserRole: userRole,
      currentTenantRole: tenantRole,
      currentTenantId,
    };
  }, [session, tenants]);

  return permissions;
}

function getDefaultPermissions(): UserPermissions {
  return {
    isSuperAdmin: false,
    isTenantAdmin: false,
    isTenantMember: false,
    canManageTenant: false,
    canDeleteTenant: false,
    canInviteMembers: false,
    canManageMembers: false,
    canUpdateMemberRoles: false,
    canManageDocumentTypes: false,
    canManageFields: false,
    canViewAllTenants: false,
    canSwitchTenants: false,
    canCreateDocuments: false,
    canEditDocuments: false,
    canDeleteDocuments: false,
    canViewDocuments: false,
    currentUserRole: null,
    currentTenantRole: null,
    currentTenantId: null,
  };
}

// 权限检查的辅助函数
export function hasPermission(
  permissions: UserPermissions,
  requiredPermission: keyof UserPermissions
): boolean {
  return Boolean(permissions[requiredPermission]);
}

// 角色检查的辅助函数
export function hasRole(
  permissions: UserPermissions,
  roles: string[]
): boolean {
  return roles.includes(permissions.currentUserRole || '') || 
         roles.includes(permissions.currentTenantRole || '');
}

// 权限组合检查
export function hasAnyPermission(
  permissions: UserPermissions,
  requiredPermissions: (keyof UserPermissions)[]
): boolean {
  return requiredPermissions.some(permission => 
    Boolean(permissions[permission])
  );
}

export function hasAllPermissions(
  permissions: UserPermissions,
  requiredPermissions: (keyof UserPermissions)[]
): boolean {
  return requiredPermissions.every(permission => 
    Boolean(permissions[permission])
  );
}
