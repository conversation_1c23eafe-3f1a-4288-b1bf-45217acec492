// 生成VAPID密钥对的脚本
// 运行: node generate-vapid-keys.js

const crypto = require('crypto');

function generateVapidKeys() {
  // 生成ECDSA P-256密钥对
  const { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
    namedCurve: 'prime256v1',
    publicKeyEncoding: {
      type: 'spki',
      format: 'der'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'der'
    }
  });

  // 转换为base64url格式
  const publicKeyBase64 = publicKey.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  const privateKeyBase64 = privateKey.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return {
    publicKey: publicKeyBase64,
    privateKey: privateKeyBase64
  };
}

// 生成密钥
const keys = generateVapidKeys();

console.log('VAPID密钥对已生成:');
console.log('');
console.log('公钥 (添加到 NEXT_PUBLIC_VAPID_PUBLIC_KEY):');
console.log(keys.publicKey);
console.log('');
console.log('私钥 (添加到 VAPID_PRIVATE_KEY):');
console.log(keys.privateKey);
console.log('');
console.log('请将以下内容添加到 .env.local 文件:');
console.log('');
console.log(`NEXT_PUBLIC_VAPID_PUBLIC_KEY=${keys.publicKey}`);
console.log(`VAPID_PRIVATE_KEY=${keys.privateKey}`);
console.log('VAPID_SUBJECT=mailto:<EMAIL>');
