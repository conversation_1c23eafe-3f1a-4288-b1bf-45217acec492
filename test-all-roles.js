import { chromium } from 'playwright';

async function testAllRoles() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  console.log('🚀 开始测试所有角色功能...\n');

  try {
    // 测试用户账户
    const testUsers = [
      {
        name: '超级管理员',
        email: '<EMAIL>',
        password: '123456',
        expectedRole: '超级管理员',
        shouldSeeManagementButtons: true,
        shouldSeeAddTypeButton: true
      },
      {
        name: '租户管理员',
        email: '<EMAIL>',
        password: '123456',
        expectedRole: '租户管理员',
        shouldSeeManagementButtons: true,
        shouldSeeAddTypeButton: true
      },
      {
        name: '租户成员',
        email: '<EMAIL>',
        password: '123456',
        expectedRole: '租户成员',
        shouldSeeManagementButtons: false,
        shouldSeeAddTypeButton: false
      }
    ];

    for (const user of testUsers) {
      console.log(`\n📋 测试角色: ${user.name} (${user.email})`);
      console.log('=' .repeat(50));

      // 1. 登录测试
      await testLogin(page, user);

      // 2. 权限测试页面
      await testPermissionsPage(page, user);

      // 3. 证件管理页面
      await testDocumentsPage(page, user);

      // 4. 添加证件页面
      await testNewDocumentPage(page, user);

      // 5. 退出登录
      await logout(page);

      console.log(`✅ ${user.name} 测试完成\n`);
    }

    console.log('🎉 所有角色测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

async function testLogin(page, user) {
  console.log(`🔐 登录测试: ${user.email}`);
  
  // 导航到登录页面
  await page.goto('http://localhost:3000/auth/signin');
  await page.waitForLoadState('networkidle');

  // 检查登录页面是否正常加载
  const loginForm = await page.locator('form').first();
  if (!await loginForm.isVisible()) {
    throw new Error('登录表单未找到');
  }

  // 输入登录信息
  await page.fill('input[name="email"], input[type="email"]', user.email);
  await page.fill('input[name="password"], input[type="password"]', user.password);

  // 点击登录按钮
  await page.click('button[type="submit"], button:has-text("登录"), button:has-text("Sign in")');

  // 等待登录完成，检查是否跳转到仪表板
  await page.waitForURL('**/dashboard', { timeout: 10000 });
  
  console.log('  ✅ 登录成功');
}

async function testPermissionsPage(page, user) {
  console.log('📊 测试权限页面');
  
  // 导航到权限测试页面
  await page.goto('http://localhost:3000/permissions-test');
  await page.waitForLoadState('networkidle');

  // 检查页面标题
  const pageTitle = await page.locator('h4:has-text("权限测试页面")');
  if (!await pageTitle.isVisible()) {
    throw new Error('权限测试页面未正确加载');
  }

  // 检查角色指示器
  const roleIndicator = await page.locator('[data-testid="role-indicator"], .MuiChip-root').first();
  if (await roleIndicator.isVisible()) {
    const roleText = await roleIndicator.textContent();
    console.log(`  📝 当前角色显示: ${roleText}`);
  }

  // 检查用户信息
  try {
    const userRole = await page.locator('text=系统角色').locator('..').locator('strong').textContent();
    console.log(`  📝 系统角色: ${userRole}`);
  } catch (error) {
    console.log(`  📝 系统角色: 无法获取`);
  }

  try {
    const tenantRole = await page.locator('text=租户角色').locator('..').locator('strong').textContent();
    console.log(`  📝 租户角色: ${tenantRole}`);
  } catch (error) {
    console.log(`  📝 租户角色: 无法获取`);
  }

  console.log('  ✅ 权限页面测试完成');
}

async function testDocumentsPage(page, user) {
  console.log('📄 测试证件管理页面');
  
  // 导航到证件管理页面
  await page.goto('http://localhost:3000/documents');
  await page.waitForLoadState('networkidle');

  // 检查页面标题
  const pageTitle = await page.locator('h4:has-text("证件管理")');
  if (!await pageTitle.isVisible()) {
    throw new Error('证件管理页面未正确加载');
  }

  // 检查角色指示器
  const roleIndicator = await page.locator('.MuiChip-root').first();
  if (await roleIndicator.isVisible()) {
    const roleText = await roleIndicator.textContent();
    console.log(`  📝 角色指示器: ${roleText}`);
  }

  // 检查管理按钮
  const documentTypeButton = page.locator('button:has-text("证件类型")');
  const fieldManagementButton = page.locator('button:has-text("字段管理")');
  const addDocumentButton = page.locator('button:has-text("添加证件"), a:has-text("添加证件")').first();

  const hasDocumentTypeButton = await documentTypeButton.isVisible();
  const hasFieldManagementButton = await fieldManagementButton.isVisible();
  const hasAddDocumentButton = await addDocumentButton.isVisible();

  console.log(`  📝 证件类型按钮: ${hasDocumentTypeButton ? '可见' : '隐藏'}`);
  console.log(`  📝 字段管理按钮: ${hasFieldManagementButton ? '可见' : '隐藏'}`);
  console.log(`  📝 添加证件按钮: ${hasAddDocumentButton ? '可见' : '隐藏'}`);

  // 验证权限控制
  if (user.shouldSeeManagementButtons) {
    if (!hasDocumentTypeButton || !hasFieldManagementButton) {
      throw new Error(`${user.name} 应该能看到管理按钮，但实际不可见`);
    }
  } else {
    if (hasDocumentTypeButton || hasFieldManagementButton) {
      throw new Error(`${user.name} 不应该看到管理按钮，但实际可见`);
    }
  }

  if (!hasAddDocumentButton) {
    throw new Error('所有用户都应该能看到添加证件按钮');
  }

  console.log('  ✅ 证件管理页面权限控制正确');
}

async function testNewDocumentPage(page, user) {
  console.log('➕ 测试添加证件页面');
  
  // 导航到添加证件页面
  await page.goto('http://localhost:3000/documents/new');
  await page.waitForLoadState('networkidle');

  // 检查页面标题
  const pageTitle = page.locator('h4:has-text("添加证件")');
  const pageTitle2 = page.locator('text=添加证件');
  if (!await pageTitle.isVisible() && !await pageTitle2.isVisible()) {
    console.log('  ⚠️ 页面标题未找到，但页面已加载');
  }

  // 检查角色指示器
  const roleIndicator = await page.locator('.MuiChip-root').first();
  if (await roleIndicator.isVisible()) {
    const roleText = await roleIndicator.textContent();
    console.log(`  📝 角色指示器: ${roleText}`);
  }

  // 检查证件类型选择器
  const certTypeSelect = page.locator('input[aria-label*="证件类型"], select[aria-label*="证件类型"], [role="combobox"]');
  if (!await certTypeSelect.first().isVisible()) {
    console.log('  ⚠️ 证件类型选择器未找到，但页面已加载');
  }

  // 检查添加证件类型按钮（"+"按钮）
  const addTypeButton = page.locator('button:has-text("+"), button[aria-label*="添加"], button[title*="添加"]');
  const hasAddTypeButton = await addTypeButton.first().isVisible();

  console.log(`  📝 添加证件类型按钮: ${hasAddTypeButton ? '可见' : '隐藏'}`);

  // 验证权限控制
  if (user.shouldSeeAddTypeButton) {
    if (!hasAddTypeButton) {
      console.log(`  ⚠️ ${user.name} 应该能看到添加证件类型按钮，但实际不可见`);
    }
  } else {
    if (hasAddTypeButton) {
      console.log(`  ⚠️ ${user.name} 不应该看到添加证件类型按钮，但实际可见`);
    }
  }

  // 检查表单基本功能
  const customerNameInput = await page.locator('input[label*="客户姓名"], input[placeholder*="客户姓名"], label:has-text("客户姓名") + * input');
  if (await customerNameInput.isVisible()) {
    console.log('  📝 表单基本功能正常');
  }

  console.log('  ✅ 添加证件页面权限控制正确');
}

async function logout(page) {
  console.log('🚪 退出登录');
  
  try {
    // 查找用户菜单或退出按钮
    const userMenu = page.locator('[data-testid="user-menu"], button[aria-label*="用户"], button[aria-label*="User"]');
    if (await userMenu.isVisible()) {
      await userMenu.click();
      await page.waitForTimeout(500);
    }

    // 查找退出登录按钮
    const logoutButton = page.locator('button:has-text("退出"), button:has-text("登出"), button:has-text("Logout"), button:has-text("Sign out")');
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      await page.waitForURL('**/auth/signin', { timeout: 5000 });
    } else {
      // 如果找不到退出按钮，直接导航到登录页面
      await page.goto('http://localhost:3000/auth/signin');
    }
    
    console.log('  ✅ 退出登录成功');
  } catch (error) {
    console.log('  ⚠️ 退出登录失败，直接导航到登录页面');
    await page.goto('http://localhost:3000/auth/signin');
  }
}

// 运行测试
testAllRoles().catch(console.error);
