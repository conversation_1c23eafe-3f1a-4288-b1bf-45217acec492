# 🚀 Vercel 生产环境部署指南

## ⚠️ 重要：保护现有数据

**现在使用安全部署模式，不会删除现有数据！**

## 🔧 环境变量配置

在 Vercel 项目设置中添加以下环境变量：

### 必需环境变量
```bash
NEXTAUTH_SECRET="your-super-secret-key-here-at-least-32-chars"
DATABASE_URL="postgresql://username:password@host:port/database"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="YourSecurePassword123!"
```

### 可选环境变量（用于 Telegram Bot）
```bash
TELEGRAM_BOT_TOKEN="123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
```

## 🚀 部署步骤

### 1. 推送代码到 GitHub
```bash
git add .
git commit -m "Update for production deployment"
git push origin main
```

### 2. Vercel 自动部署
- Vercel 会自动检测到代码更新
- 执行 `npm run deploy:safe` 命令
- **安全模式：保护现有数据，只执行必要的模式更新**

### 3. 部署后配置

#### A. Telegram Bot 配置（如果需要）
1. 登录管理员后台：`https://your-domain.vercel.app/admin/notifications`
2. 在 "Telegram配置" 部分：
   - 开启 Telegram 通知开关
   - 输入 Bot Token
   - 保存配置
3. 在 "Webhook 配置" 部分：
   - 点击 "设置Webhook" 按钮
   - 确认显示 "✅ 已配置"

#### B. 验证部署
1. 访问网站确认正常运行
2. 检查数据库数据完整性
3. 测试 Telegram Bot（如果配置了）

## 🤖 Telegram Bot 在 Vercel 上的工作原理

### Webhook 模式
- Vercel 是无服务器环境，不支持长轮询
- 使用 Webhook 接收 Telegram 消息
- API 端点：`/api/telegram/webhook`

### 设置流程
1. 配置 Bot Token
2. 点击 "设置Webhook" 按钮
3. 系统自动设置 Webhook URL：`https://your-domain.vercel.app/api/telegram/webhook`

### 功能支持
- ✅ 用户获取 Telegram ID
- ✅ 发送证件到期提醒
- ✅ 交互式命令（/start, /id, /help）
- ✅ 自动回复和指导

## 📋 部署检查清单

### 部署前
- [ ] 确认环境变量已设置
- [ ] 代码已推送到 GitHub
- [ ] 数据库连接正常

### 部署后
- [ ] 网站可正常访问
- [ ] 管理员登录正常
- [ ] 数据库数据完整
- [ ] Telegram Bot 配置（如需要）
- [ ] Webhook 设置成功（如需要）

## 🔒 安全注意事项

### 数据保护
- ✅ 使用 `deploy:safe` 命令保护现有数据
- ✅ 只执行必要的数据库模式更新
- ✅ 不会重置或删除现有数据

### 环境变量安全
- 🔐 NEXTAUTH_SECRET 必须是强随机字符串
- 🔐 DATABASE_URL 包含敏感信息，确保安全
- 🔐 TELEGRAM_BOT_TOKEN 不要泄露

### 访问控制
- 👤 管理员账户使用强密码
- 🔑 定期更新密码和密钥
- 📱 Telegram Bot 权限最小化

## 🚨 故障排除

### 部署失败
1. 检查环境变量是否正确设置
2. 查看 Vercel 构建日志
3. 确认数据库连接正常

### Telegram Bot 不工作
1. 确认 Bot Token 正确
2. 检查 Webhook 是否设置成功
3. 查看 Vercel 函数日志

### 数据库问题
1. 确认 DATABASE_URL 格式正确
2. 检查数据库服务器状态
3. 验证网络连接

## 📈 监控和维护

### 定期检查
- 🔍 监控网站运行状态
- 📊 检查数据库性能
- 🤖 验证 Telegram Bot 功能

### 更新流程
1. 在开发环境测试更改
2. 推送到 GitHub
3. Vercel 自动部署
4. 验证生产环境功能

## 🎉 部署完成

现在你的证件提醒系统已经安全部署到 Vercel！

- 🌐 **网站地址**: `https://your-domain.vercel.app`
- 👤 **管理后台**: `https://your-domain.vercel.app/admin`
- 🤖 **Telegram Bot**: 通过 Webhook 工作
- 💾 **数据安全**: 现有数据已保护

**享受你的证件提醒系统吧！** 🎊
