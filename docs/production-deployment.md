# 生产环境部署指南

## 🚀 快速部署

### 1. Vercel 部署（推荐）

#### 步骤 1: 准备数据库
```bash
# 创建 PostgreSQL 数据库（推荐使用 Supabase、PlanetScale 或 Railway）
# 获取数据库连接字符串
```

#### 步骤 2: 设置环境变量
在 Vercel 项目设置中添加：
```
NEXTAUTH_SECRET=your-super-secret-key-here
DATABASE_URL=postgresql://username:password@host:port/database
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=YourSecurePassword123!
NEXTAUTH_URL=https://your-app.vercel.app
```

#### 步骤 3: 部署
```bash
# 推送代码到 GitHub
git push origin main

# Vercel 会自动部署
# 或手动部署
vercel --prod
```

### 2. Docker 部署

#### 步骤 1: 创建环境文件
```bash
cp .env.production.example .env.production
# 编辑 .env.production 填入真实值
```

#### 步骤 2: 构建和运行
```bash
# 构建镜像
docker build -t certificate-reminder .

# 运行容器
docker run -d \
  --name certificate-reminder \
  --env-file .env.production \
  -p 3000:3000 \
  certificate-reminder
```

### 3. VPS 部署

#### 步骤 1: 服务器准备
```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
npm install -g pm2
```

#### 步骤 2: 部署应用
```bash
# 克隆代码
git clone https://github.com/yourusername/certificate-reminder.git
cd certificate-reminder

# 设置环境变量
cp .env.production.example .env.production
nano .env.production

# 运行部署脚本
npm run deploy:prod

# 使用 PM2 启动
pm2 start npm --name "certificate-reminder" -- start
pm2 save
pm2 startup
```

## 🔧 首次部署后的配置

### 1. 登录管理员账户
- 访问: `https://yourdomain.com/auth/signin`
- 使用设置的 `ADMIN_EMAIL` 和 `ADMIN_PASSWORD` 登录

### 2. 修改默认密码
- 进入用户设置
- 立即修改默认密码

### 3. 配置系统设置
- 进入管理面板 → 系统通知配置
- 配置邮件服务（SMTP 或 SendGrid）
- 配置 Telegram Bot（可选）
- 配置 PWA 推送（可选）

### 4. 创建订阅计划
- 检查默认订阅计划
- 根据需要调整价格和功能

## 🔒 安全检查清单

- [ ] 修改默认管理员密码
- [ ] 设置强密码策略
- [ ] 配置 HTTPS
- [ ] 设置防火墙规则
- [ ] 定期备份数据库
- [ ] 监控系统日志
- [ ] 更新依赖包

## 📊 监控和维护

### 数据库备份
```bash
# 每日备份
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# 自动备份脚本
echo "0 2 * * * pg_dump $DATABASE_URL > /backups/backup_\$(date +\%Y\%m\%d).sql" | crontab -
```

### 应用监控
```bash
# 使用 PM2 监控
pm2 monit

# 查看日志
pm2 logs certificate-reminder
```

## 🆙 更新部署

### 更新应用
```bash
# 拉取最新代码
git pull origin main

# 运行更新
npm run deploy:prod

# 重启应用（如果使用 PM2）
pm2 restart certificate-reminder
```

### 数据库迁移
```bash
# 安全迁移（自动备份）
npm run db:migrate:safe
```

## 🆘 故障排除

### 常见问题
1. **数据库连接失败**: 检查 `DATABASE_URL` 格式
2. **认证失败**: 检查 `NEXTAUTH_SECRET` 设置
3. **邮件发送失败**: 检查 SMTP 配置
4. **构建失败**: 检查 Node.js 版本（需要 18+）

### 回滚方案
```bash
# 回滚到上一个版本
git checkout previous-stable-tag
npm run deploy:prod
pm2 restart certificate-reminder
```
