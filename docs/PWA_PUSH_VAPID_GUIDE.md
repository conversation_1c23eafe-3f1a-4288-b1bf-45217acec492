# PWA推送配置与VAPID密钥获取指南

## 📱 PWA推送 vs 浏览器通知

### 概念区别

**浏览器通知 (Browser Notifications)**：
- 本地通知，由网页JavaScript直接触发
- 用户必须在当前页面或标签页打开时才能接收
- 不需要服务器推送，只需要用户授权
- 实现简单，但功能有限

**PWA推送 (Push Notifications)**：
- 服务器端推送通知，通过浏览器的推送服务
- 即使用户关闭了网页也能接收通知
- 需要Service Worker、VAPID密钥等技术支持
- 功能强大，但配置复杂

### 技术架构对比

```
浏览器通知流程:
网页JavaScript → 直接显示通知

PWA推送流程:
您的服务器 → 浏览器推送服务 (FCM/Mozilla等) → 用户浏览器 → 显示通知
```

## 🔑 VAPID密钥详解

### 什么是VAPID？

**VAPID** (Voluntary Application Server Identification) 是一种用于验证推送服务器身份的标准。

**作用**：
- 验证推送请求来自合法的服务器
- 防止恶意推送攻击
- 提供推送服务的身份识别

**组成部分**：
- **公钥 (Public Key)**: 用于前端订阅推送服务
- **私钥 (Private Key)**: 用于后端发送推送请求
- **主题 (Subject)**: 标识应用的联系方式（邮箱或网站）

## 🛠️ VAPID密钥获取方法

### 方法1: 使用系统内置生成功能 ⭐ 推荐

**操作步骤**：
1. 登录超级管理员账户：`<EMAIL>` / `123456`
2. 访问：`/admin/notifications` (系统通知配置)
3. 在"PWA推送配置"部分点击"生成VAPID密钥"按钮
4. 系统自动生成并填入公钥和私钥
5. 填写VAPID主题（建议格式：`mailto:<EMAIL>`）
6. 点击"保存配置"

**优点**：
- ✅ 一键生成，操作简单
- ✅ 自动填入配置字段
- ✅ 无需安装额外工具

### 方法2: 使用在线工具

**推荐工具**：
- https://vapidkeys.com/
- https://web-push-codelab.glitch.me/

**操作步骤**：
1. 访问 https://vapidkeys.com/
2. 点击 "Generate VAPID Keys"
3. 复制生成的公钥和私钥
4. 手动填入系统配置

### 方法3: 使用Node.js命令行工具

**安装工具**：
```bash
npm install -g web-push
```

**生成密钥**：
```bash
web-push generate-vapid-keys
```

**输出示例**：
```
=======================================
Public Key:
BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U

Private Key:
VCxaZ7zG2nfu7O5F3YQjQlxRtP8YkMqmSzVOHpH_-Q
=======================================
```

## ⚙️ 系统配置说明

### VAPID配置字段

**VAPID公钥 (Public Key)**：
- 用途：前端订阅推送服务时使用
- 格式：Base64编码的字符串
- 示例：`BJ2PBgxSdGvdV3-I7xoxEbtwR1jLKRo1jBk7XGWGMpgYtfTRBS4Z8uCudMd23qA4d0bGYw9iPU8qxM_V8b2b31I`

**VAPID私钥 (Private Key)**：
- 用途：后端发送推送请求时使用
- 格式：Base64编码的字符串
- 示例：`EnntxggN2CAsMWSPhvtwKjqDi87NDZZMUXAy5YpTip8`
- ⚠️ **安全提醒**：私钥请妥善保管，不要泄露

**VAPID主题 (Subject)**：
- 用途：标识应用的联系方式
- 格式：邮箱地址或网站URL
- 示例：
  - `mailto:<EMAIL>`
  - `https://yourwebsite.com`

### 配置验证

**必填字段检查**：
- ✅ VAPID公钥：必须填写
- ✅ VAPID私钥：必须填写
- ✅ VAPID主题：必须填写

**格式验证**：
- 公钥和私钥应为有效的Base64编码
- 主题应为有效的邮箱或URL格式

## 🔧 技术实现

### 后端API实现

**生成VAPID密钥API**：
```typescript
// 生成VAPID密钥对（超级管理员）
generateVapidKeys: protectedProcedure
  .mutation(async ({ ctx }) => {
    // 权限检查
    if (user?.role !== "SUPER_ADMIN") {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有超级管理员可以生成VAPID密钥",
      });
    }

    // 生成密钥
    const vapidKeys = webpush.generateVAPIDKeys();

    return {
      success: true,
      publicKey: vapidKeys.publicKey,
      privateKey: vapidKeys.privateKey,
      message: "VAPID密钥生成成功",
    };
  }),
```

### 前端界面实现

**生成按钮**：
```typescript
<Button
  variant="outlined"
  size="small"
  startIcon={<VpnKey />}
  onClick={handleGenerateVapidKeys}
  disabled={!systemConfig.pushEnabled || generateVapidMutation.isLoading}
>
  {generateVapidMutation.isLoading ? '生成中...' : '生成VAPID密钥'}
</Button>
```

**处理函数**：
```typescript
const handleGenerateVapidKeys = () => {
  if (confirm("生成新的VAPID密钥将替换现有密钥，确定要继续吗？")) {
    generateVapidMutation.mutate();
  }
};
```

## 🛡️ 安全注意事项

### 密钥管理

**私钥安全**：
- ❌ 不要在前端代码中暴露私钥
- ❌ 不要将私钥提交到版本控制系统
- ✅ 私钥只在服务器端使用
- ✅ 定期更换密钥以提高安全性

**公钥使用**：
- ✅ 公钥可以在前端代码中使用
- ✅ 公钥用于用户订阅推送服务
- ✅ 公钥可以公开，不需要保密

### 权限控制

**生成权限**：
- 只有超级管理员可以生成VAPID密钥
- 普通用户无法访问密钥生成功能

**配置权限**：
- 只有超级管理员可以配置系统级推送设置
- 商户管理员只能在系统允许范围内配置

## 📊 配置流程

### 完整配置步骤

1. **生成VAPID密钥**：
   - 登录超级管理员账户
   - 访问系统通知配置页面
   - 点击"生成VAPID密钥"按钮

2. **配置VAPID信息**：
   - 确认公钥和私钥已自动填入
   - 填写VAPID主题（联系邮箱或网站）
   - 保存配置

3. **启用PWA推送**：
   - 确保"PWA推送"开关已开启
   - 检查推送类型配置（系统推送、证件到期推送）

4. **测试推送功能**：
   - 使用测试功能验证配置是否正确
   - 检查浏览器是否能正常接收推送

### 配置验证清单

- ✅ VAPID公钥已填写且格式正确
- ✅ VAPID私钥已填写且格式正确
- ✅ VAPID主题已填写且格式正确
- ✅ PWA推送开关已启用
- ✅ 推送类型配置已设置
- ✅ 测试推送功能正常

## 🎯 使用场景

### 适用场景

**PWA推送适合**：
- 证件到期提醒
- 系统重要通知
- 实时状态更新
- 用户关闭网页后的通知

**浏览器通知适合**：
- 页面内的即时提醒
- 用户操作反馈
- 简单的状态提示

### 用户体验

**PWA推送优势**：
- 📱 即使关闭网页也能收到通知
- 🔔 通知显示在系统通知栏
- 📲 支持移动端和桌面端
- 🎯 可以精确控制推送时机

## 🎉 完成状态

✅ **PWA推送概念已解释** - 区分PWA推送和浏览器通知
✅ **VAPID密钥获取方法已提供** - 多种获取方式供选择
✅ **系统内置生成功能已实现** - 一键生成VAPID密钥
✅ **配置流程已详细说明** - 完整的配置指南
✅ **安全注意事项已强调** - 密钥管理和权限控制

现在您可以轻松配置PWA推送功能，为用户提供更好的通知体验！🎉
