# 🧪 手动测试指南 - 所有角色功能验证

## 📋 测试环境
- **应用地址**: http://localhost:3000
- **登录页面**: http://localhost:3000/auth/signin

## 👥 测试账户

### 1. 超级管理员 (SUPER_ADMIN)
- **邮箱**: `<EMAIL>`
- **密码**: `123456`
- **预期权限**: 所有功能权限

### 2. 租户管理员 (TENANT_ADMIN + ADMIN)
- **邮箱**: `<EMAIL>`
- **密码**: `123456`
- **预期权限**: 租户内管理权限

### 3. 租户成员 (TENANT_MEMBER + MEMBER)
- **邮箱**: `<EMAIL>`
- **密码**: `123456`
- **预期权限**: 基本使用权限

## 🔍 测试步骤

### 第一步：超级管理员测试

1. **登录**
   - 访问 http://localhost:3000/auth/signin
   - 输入 `<EMAIL>` / `123456`
   - 验证登录成功

2. **权限测试页面**
   - 访问 http://localhost:3000/permissions-test
   - ✅ 验证角色显示为"超级管理员"
   - ✅ 验证所有权限项都显示为 ✓

3. **证件管理页面**
   - 访问 http://localhost:3000/documents
   - ✅ 验证页面标题显示"证件管理"
   - ✅ 验证右上角显示"超级管理员"红色徽章
   - ✅ 验证可以看到"证件类型"按钮
   - ✅ 验证可以看到"字段管理"按钮
   - ✅ 验证可以看到"添加证件"按钮

4. **添加证件页面**
   - 访问 http://localhost:3000/documents/new
   - ✅ 验证页面标题显示"添加证件"
   - ✅ 验证右上角显示"超级管理员"徽章
   - ✅ 验证证件类型选择器旁有"+"按钮

5. **证件类型管理**
   - 在证件管理页面点击"证件类型"按钮
   - ✅ 验证对话框正常打开
   - ✅ 验证可以添加、编辑、删除证件类型

### 第二步：租户管理员测试

1. **退出登录**
   - 点击用户菜单退出登录

2. **登录**
   - 输入 `<EMAIL>` / `123456`
   - 验证登录成功

3. **权限测试页面**
   - 访问 http://localhost:3000/permissions-test
   - ✅ 验证角色显示为"租户管理员"
   - ✅ 验证租户管理权限为 ✓
   - ✅ 验证功能管理权限为 ✓
   - ✅ 验证系统级权限部分为 ✗

4. **证件管理页面**
   - 访问 http://localhost:3000/documents
   - ✅ 验证右上角显示"租户管理员"橙色徽章
   - ✅ 验证可以看到"证件类型"按钮
   - ✅ 验证可以看到"字段管理"按钮
   - ✅ 验证可以看到"添加证件"按钮

5. **添加证件页面**
   - 访问 http://localhost:3000/documents/new
   - ✅ 验证右上角显示"租户管理员"徽章
   - ✅ 验证证件类型选择器旁有"+"按钮

### 第三步：租户成员测试

1. **退出登录**
   - 点击用户菜单退出登录

2. **登录**
   - 输入 `<EMAIL>` / `123456`
   - 验证登录成功

3. **权限测试页面**
   - 访问 http://localhost:3000/permissions-test
   - ✅ 验证角色显示为"租户成员"
   - ✅ 验证租户管理权限为 ✗
   - ✅ 验证功能管理权限为 ✗
   - ✅ 验证基本数据操作权限为 ✓

4. **证件管理页面**
   - 访问 http://localhost:3000/documents
   - ✅ 验证右上角显示"租户成员"蓝色徽章
   - ❌ 验证**不能**看到"证件类型"按钮
   - ❌ 验证**不能**看到"字段管理"按钮
   - ✅ 验证可以看到"添加证件"按钮

5. **添加证件页面**
   - 访问 http://localhost:3000/documents/new
   - ✅ 验证右上角显示"租户成员"徽章
   - ❌ 验证证件类型选择器旁**没有**"+"按钮

6. **证件操作权限**
   - 在证件列表中点击证件的操作菜单
   - ✅ 验证可以看到"查看详情"
   - ✅ 验证可以看到"编辑"
   - ❌ 验证**不能**看到"删除"选项

## 🎯 预期结果总结

### 超级管理员
- ✅ 所有功能都可见和可用
- ✅ 红色"超级管理员"徽章
- ✅ 完整的管理权限

### 租户管理员
- ✅ 租户内管理功能可见
- ✅ 橙色"租户管理员"徽章
- ✅ 证件类型和字段管理权限

### 租户成员
- ✅ 只有基本功能可见
- ✅ 蓝色"租户成员"徽章
- ❌ 管理功能被隐藏
- ❌ 删除操作不可见

## 🚨 常见问题排查

### 如果登录失败
1. 确认应用运行在 http://localhost:3000
2. 确认数据库种子数据已正确生成
3. 检查控制台是否有错误信息

### 如果权限显示不正确
1. 刷新页面重新加载权限
2. 检查浏览器控制台错误
3. 确认用户角色数据正确

### 如果页面无法访问
1. 确认用户已正确登录
2. 检查路由权限设置
3. 查看网络请求是否正常

## ✅ 测试完成标准

所有三个角色的测试都通过，且：
- 权限控制正确生效
- 角色指示器正确显示
- 功能按钮根据权限显示/隐藏
- 用户体验流畅无错误

完成测试后，系统即可交付给客户使用！
