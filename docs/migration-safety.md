# 数据库迁移安全指南

## 迁移不会丢失数据的原因

### 1. 增量式更新
- ✅ 只修改数据库结构（表、字段、索引）
- ✅ 保留所有现有数据
- ✅ 只执行未运行过的迁移

### 2. 迁移类型示例

#### 安全迁移（不会丢失数据）
```sql
-- 添加新字段
ALTER TABLE "User" ADD COLUMN "phone" TEXT;

-- 添加新表
CREATE TABLE "NewFeature" (...);

-- 添加索引
CREATE INDEX "User_email_idx" ON "User"("email");

-- 修改字段类型（兼容的）
ALTER TABLE "User" ALTER COLUMN "name" TYPE VARCHAR(255);
```

#### 需要注意的迁移
```sql
-- 删除字段（会丢失该字段的数据）
ALTER TABLE "User" DROP COLUMN "oldField";

-- 重命名字段（需要数据迁移）
ALTER TABLE "User" RENAME COLUMN "oldName" TO "newName";
```

## 安全措施

### 1. 自动备份策略
```bash
# 每次迁移前自动备份
pg_dump $DATABASE_URL > "backup_$(date +%Y%m%d_%H%M%S).sql"
```

### 2. 迁移预览
```bash
# 查看将要执行的迁移
npx prisma migrate diff --preview-feature
```

### 3. 回滚策略
```bash
# 如果需要回滚（很少需要）
psql $DATABASE_URL < backup_20240122_143000.sql
```
