
# 📌 项目名称
**证件到期提醒管理系统（多商户版）**

## 🎯 产品定位
为个人及中小企业提供专业的证件到期提醒与管理工具，支持多商户，帮助用户有序管理客户信息与证件资料，提前预警到期风险。
提供 PWA 浏览器推送与 Telegram 消息提醒，避免重要证件过期带来的损失。

## 👥 用户与商户类型
| 类型    | 特性与权限 |
|---------|-----------|
| 个人    | 单用户使用，不可添加成员，管理自身证件 |
| 企业    | 多成员共用一个商户账户，可添加/移除成员，团队协作管理证件数据 |

商户管理者和所有者这个冲突了，角色有超级管理员、商户管理员、商户用户，商户分为公司和个人，公司商户可以添加用户，个人商户不可以增加用户
🔐 正确的用户角色系统
1. 系统级用户角色 (UserRole)
SUPER_ADMIN: 超级管理员 - 系统级管理员，可以管理所有商户
TENANT_ADMIN: 商户管理员 - 商户的管理员
TENANT_MEMBER: 商户用户 - 商户的普通用户
2. 商户类型 (TenantType)
PERSONAL: 个人商户 - 不可以添加其他用户
ENTERPRISE: 企业商户 - 可以添加多个用户
3. 权限规则
个人商户: 只有一个用户（创建者），不能邀请其他用户
企业商户: 可以有多个用户，商户管理员可以邀请和管理用户
超级管理员: 可以管理所有商户和用户

## 🏗 功能模块
### 证件管理（含客户信息）
- 客户信息（姓名、电话、邮箱、地址、公司等）
- 证件信息（类型、编号、发证机关、有效期等）
- 自定义字段（支持文本、数字、日期、文件上传）
- 标签与搜索、过滤功能

### 到期提醒
- 定时检测到期情况
- 支持多提醒节点（如提前 30/15/7/1 天）
- 通过：
  - PWA 浏览器推送（web-push）
  - Telegram Bot 消息推送

### 日历视图
- 以日历方式展示所有证件到期时间，快速查看未来到期高峰

### 多商户架构
- 企业可添加/移除成员（邀请注册或分配权限）
- 不同商户间数据隔离，安全可靠

### 订阅管理
- 试用套餐 / 标准套餐
- 手动转账凭证上传 & 后台审核
- 自动记录支付和到期时间

### 超级管理员后台
- 管理所有商户、用户、订阅订单
- 查看系统运行数据、审核转账凭证

## 🔔 通知与提醒
- 使用 `node-cron` 或独立 worker 定时每日检查到期情况
- 支持提前 N 天多次提醒
- 通知通过：
  - PWA 浏览器推送
  - Telegram Bot 消息

## 🖥 主要页面与交互
| 页面                   | 功能 |
|------------------------|------|
| 登录 / 注册            | 用户登录、注册（支持商户识别） |
| 首页 Dashboard         | 到期提醒概览，待处理事项 |
| 证件管理               | 客户 + 证件数据列表，支持搜索过滤，新增编辑 |
| 日历视图               | 以月历形式查看所有证件到期情况 |
| 通知中心               | 查看历史提醒消息，调整推送偏好 |
| 订阅套餐管理           | 选择套餐、上传转账凭证、查看审核进度 |
| 系统设置               | 修改个人资料、密码、推送设置 |
| 超级管理员后台         | 审核订单，管理商户、用户、统计报表 |
| PWA 提示               | 可安装到桌面，支持离线使用与推送 |
| Telegram 通知管理页面 | 绑定/解绑 Telegram Bot 账号 |

## ⚙ 技术架构
### 前端
| 项目         | 技术 |
|--------------|------|
| 框架         | **T3 Stack - Next.js + tRPC** |
| UI           | **MUI (Material UI)** |
| PWA          | next-pwa（Workbox） |
| 状态管理     | Zustand（局部全局共享）|
| 表单处理     | React Hook Form |

### 后端 / API
| 项目          | 技术 |
|---------------|------|
| ORM           | Prisma（PostgreSQL） |
| 认证          | NextAuth（基于 JWT + Credentials） |
| tRPC          | 提供前后端 typesafe 调用 |
| 定时任务      | node-cron（部署容器计划任务）|
| 推送          | web-push |
| Telegram Bot  | node-telegram-bot-api（独立服务或 edge function）|

## 🔐 多商户数据隔离
- 所有核心表（如 `documents`）都包含 `tenant_id`
- 查询、更新、删除均基于 `tenant_id` 做严格隔离
- 企业商户在 `memberships` 表管理成员关系与权限

## 🗄 数据库表结构（Prisma 模型简化示例）
```prisma
model Tenant {
  id            String    @id @default(uuid())
  name          String
  type          TenantType
  users         User[]
  documents     Document[]
  memberships   Membership[]
  subscriptions Subscription[]
  createdAt     DateTime  @default(now())
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String
  name      String?
  tenantId  String
  tenant    Tenant   @relation(fields: [tenantId], references: [id])
  role      UserRole
  createdAt DateTime @default(now())
}

model Document {
  id           String   @id @default(uuid())
  tenantId     String
  tenant       Tenant   @relation(fields: [tenantId], references: [id])
  customerName String
  phone        String?
  email        String?
  address      String?
  company      String?
  certType     String
  certNumber   String
  issueBy      String?
  validUntil   DateTime
  customFields Json?
  tags         String[]
  createdAt    DateTime @default(now())
}

model Membership {
  id        String   @id @default(uuid())
  tenantId  String
  tenant    Tenant   @relation(fields: [tenantId], references: [id])
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  role      String   // owner, admin, member
  createdAt DateTime @default(now())
}

model Subscription {
  id          String   @id @default(uuid())
  tenantId    String
  tenant      Tenant   @relation(fields: [tenantId], references: [id])
  plan        String
  proofUrl    String?
  status      String
  startDate   DateTime
  endDate     DateTime
  createdAt   DateTime @default(now())
}
```

## 🚀 开发结构（Monorepo 示例）
```
apps/
 ├── web/                 # Next.js (T3 Stack)
 ├── telegram-bot/        # 独立 Telegram Bot 服务
 └── cron/                # 定时任务 worker
prisma/
 └── schema.prisma        # 数据模型
packages/
 ├── ui/                  # MUI 封装组件
 └── lib/                 # 公共 utils (推送、验证、i18n)
```

## ✅ MVP 核心优先级
- 🔒 多商户结构与数据隔离
- 📂 证件管理（含客户信息、自定义字段）
- 🔔 到期提醒 + PWA 推送 + Telegram Bot
- 💳 订阅套餐（试用/标准） + 手动转账上传 & 后台审核

## 🚀 后期可拓展功能
- 审核流程短信通知
- 商户运营数据看板（DAU、到期证件数量统计）
- 自定义通知模板（短信/邮箱）
- 多语言支持（中文、英文、高棉文）

## 💡 总结亮点
✅ 多商户多成员架构，企业多人协作  
✅ 证件统一管理，客户信息与证件数据一体化  
✅ 到期多节点提醒，避免关键证件过期  
✅ 双通道通知，浏览器 & Telegram 双重推送  
✅ 灵活订阅管理，支持试用、人工审核



测试账号信息：
超级管理员：<EMAIL> / 123456
商户管理员：<EMAIL> / 123456
商户成员：<EMAIL> / 123456


 测试账户信息:
- 超级管理员: <EMAIL> / 123456
- 免费用户: <EMAIL> / 123456
- 商户管理员: <EMAIL> / 123456
- 商户成员: <EMAIL> / 123456
- 说明: 可用于测试不同角色的功能
kustdeMacBook-Air:t3notice kust$ 


应用专用密码
fciy bftx paln pdzs