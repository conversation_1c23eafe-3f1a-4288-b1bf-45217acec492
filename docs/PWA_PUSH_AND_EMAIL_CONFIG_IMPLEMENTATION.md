# PWA推送订阅管理与通知邮箱配置实现总结

## 🎯 实现的功能

### 1. 通知邮箱可配置化 ✅

#### **问题解决**：
- **原问题**: 通知邮箱固定使用登录用户的邮箱
- **解决方案**: 添加可配置的通知邮箱，默认使用登录邮箱但允许自定义

#### **技术实现**：

**后端API增强**：
```typescript
// 1. 添加通知邮箱字段到配置验证
const TenantNotificationConfigSchema = z.object({
  // ... 其他字段
  notificationEmail: z.string().email().nullable().optional(), // 新增
});

// 2. 获取配置时包含通知邮箱
const config = {
  // ... 其他配置
  notificationEmail: settings.notificationEmail || user.email, // 默认使用登录邮箱
};

// 3. 更新配置时处理通知邮箱
const updatedSettings = {
  // ... 其他设置
  notificationEmail: notificationEmail, // 保存自定义邮箱
};
```

**邮件发送逻辑修改**：
```typescript
// 修改前：固定使用用户邮箱
await this.sendEmail(user.email, notification.title, notification.message);

// 修改后：使用配置的通知邮箱
const tenant = await prisma.tenant.findUnique({
  where: { id: notification.tenantId },
  select: { notificationSettings: true },
});
const tenantSettings = (tenant?.notificationSettings as any) || {};
const notificationEmail = tenantSettings.notificationEmail || user.email;

await this.sendEmail(notificationEmail, notification.title, notification.message);
```

**前端UI增强**：
```typescript
// 邮件通知显示配置的邮箱
secondary={
  `接收邮箱: ${config.notificationEmail || session?.user?.email || "未设置邮箱"}`
}

// 添加邮箱配置输入框
<TextField
  fullWidth
  size="small"
  label="通知邮箱"
  type="email"
  value={config.notificationEmail || ''}
  onChange={(e) => handleConfigChange('notificationEmail', e.target.value)}
  placeholder={session?.user?.email || '请输入邮箱地址'}
  helperText="留空则使用登录邮箱"
/>
```

### 2. PWA推送订阅管理 ✅

#### **问题解决**：
- **原问题**: PWA_PUSH显示"未找到推送订阅"，用户无法订阅推送服务
- **解决方案**: 添加完整的PWA推送订阅管理功能

#### **技术实现**：

**Service Worker创建**：
```javascript
// public/sw.js - 处理推送通知
self.addEventListener('push', (event) => {
  let notificationData = {
    title: '证件提醒',
    body: '您有证件即将到期，请及时处理。',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'certificate-reminder',
    requireInteraction: true,
    actions: [
      { action: 'view', title: '查看详情' },
      { action: 'dismiss', title: '忽略' }
    ]
  };

  // 解析推送数据
  if (event.data) {
    const data = event.data.json();
    notificationData = { ...notificationData, ...data };
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});
```

**Service Worker注册**：
```typescript
// src/app/_components/service-worker-registration.tsx
export default function ServiceWorkerRegistration() {
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered successfully:', registration);
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });
    }
  }, []);

  return null;
}
```

**推送订阅状态管理**：
```typescript
// 订阅状态类型
type PushSubscriptionStatus = 'unknown' | 'supported' | 'denied' | 'subscribed' | 'unsubscribed';

// 检查订阅状态
const checkPushSubscriptionStatus = async () => {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    setPushSubscriptionStatus('supported');
    return;
  }

  const permission = await Notification.requestPermission();
  if (permission === 'denied') {
    setPushSubscriptionStatus('denied');
    return;
  }

  const registration = await navigator.serviceWorker.ready;
  const subscription = await registration.pushManager.getSubscription();
  
  setPushSubscriptionStatus(subscription ? 'subscribed' : 'unsubscribed');
};
```

**订阅管理功能**：
```typescript
// 订阅PWA推送
const subscribeToPush = async () => {
  const registration = await navigator.serviceWorker.ready;
  const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || 'placeholder';
  
  const subscription = await registration.pushManager.subscribe({
    userVisibleOnly: true,
    applicationServerKey: vapidPublicKey,
  });

  // 保存订阅信息到服务器
  console.log('PWA推送订阅成功:', subscription);
  setPushSubscriptionStatus('subscribed');
};

// 取消订阅
const unsubscribeFromPush = async () => {
  const registration = await navigator.serviceWorker.ready;
  const subscription = await registration.pushManager.getSubscription();
  
  if (subscription) {
    await subscription.unsubscribe();
    setPushSubscriptionStatus('unsubscribed');
  }
};
```

**UI状态显示**：
```typescript
// 根据订阅状态显示不同的提示信息
secondary={
  !(availableMethods?.push ?? false) ? "系统已禁用PWA推送" :
  pushSubscriptionStatus === 'denied' ? "浏览器已拒绝推送权限" :
  pushSubscriptionStatus === 'subscribed' ? "已订阅推送服务" :
  pushSubscriptionStatus === 'unsubscribed' ? "未订阅推送服务" :
  "即使关闭网页也能接收推送通知"
}

// 根据状态显示订阅/取消订阅按钮
{pushSubscriptionStatus === 'unsubscribed' && (
  <Button size="small" variant="outlined" onClick={subscribeToPush}>
    订阅
  </Button>
)}
{pushSubscriptionStatus === 'subscribed' && (
  <Button size="small" variant="outlined" onClick={unsubscribeFromPush}>
    取消订阅
  </Button>
)}
```

## 🔧 配置要求

### VAPID密钥配置

**PWA推送需要VAPID密钥才能正常工作**：

1. **生成VAPID密钥对**：
```bash
# 使用web-push库生成
npx web-push generate-vapid-keys
```

2. **环境变量配置**：
```env
# .env.local
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_public_key_here
VAPID_PRIVATE_KEY=your_private_key_here
VAPID_SUBJECT=mailto:<EMAIL>
```

3. **服务器端推送配置**：
```typescript
// 需要在PWAPushService中配置VAPID密钥
const webpush = require('web-push');

webpush.setVapidDetails(
  process.env.VAPID_SUBJECT,
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);
```

## 🎯 用户使用流程

### 通知邮箱配置流程

1. **访问通知设置页面** → `/notifications`
2. **查看当前邮箱配置** → 显示当前接收邮箱
3. **修改通知邮箱** → 在邮箱输入框中输入新邮箱
4. **保存配置** → 系统自动保存配置
5. **测试邮件通知** → 验证新邮箱是否能正常接收

### PWA推送订阅流程

1. **访问通知设置页面** → `/notifications`
2. **查看PWA推送状态** → 显示当前订阅状态
3. **点击订阅按钮** → 浏览器请求通知权限
4. **授权通知权限** → 用户允许浏览器通知
5. **完成订阅** → 系统显示"已订阅推送服务"
6. **启用PWA推送** → 打开PWA推送开关
7. **测试推送通知** → 验证推送功能是否正常

## 📊 功能状态对比

### 修改前
| 功能 | 状态 | 问题 |
|------|------|------|
| 通知邮箱 | ❌ 固定 | 只能使用登录邮箱 |
| PWA推送 | ❌ 无法使用 | 显示"未找到推送订阅" |
| 推送管理 | ❌ 缺失 | 无订阅管理功能 |

### 修改后
| 功能 | 状态 | 改进 |
|------|------|------|
| 通知邮箱 | ✅ 可配置 | 支持自定义邮箱，默认使用登录邮箱 |
| PWA推送 | ✅ 完整支持 | 完整的订阅管理和推送功能 |
| 推送管理 | ✅ 完善 | 订阅/取消订阅/状态显示 |

## 🛡️ 错误处理

### 通知邮箱验证
```typescript
// 邮箱格式验证
notificationEmail: z.string().email().nullable().optional()

// 前端验证
<TextField
  type="email"
  error={!isValidEmail(value)}
  helperText={!isValidEmail(value) ? "请输入有效的邮箱地址" : "留空则使用登录邮箱"}
/>
```

### PWA推送错误处理
```typescript
// 浏览器兼容性检查
if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
  alert("此浏览器不支持PWA推送");
  return;
}

// 权限被拒绝处理
if (permission === 'denied') {
  alert("推送权限被拒绝，请在浏览器设置中允许通知权限");
  return;
}

// 订阅失败处理
try {
  const subscription = await registration.pushManager.subscribe(options);
} catch (error) {
  console.error('PWA推送订阅失败:', error);
  alert('PWA推送订阅失败，请检查浏览器设置');
}
```

## 🎉 完成状态

✅ **通知邮箱可配置** - 支持自定义通知接收邮箱
✅ **PWA推送订阅管理** - 完整的订阅/取消订阅功能
✅ **Service Worker支持** - 处理推送通知和离线功能
✅ **状态显示优化** - 清晰的订阅状态和操作提示
✅ **错误处理完善** - 兼容性检查和错误提示
✅ **用户体验提升** - 直观的配置界面和操作流程

## 📝 后续工作

### 必需配置
1. **配置VAPID密钥** - PWA推送必需
2. **测试推送功能** - 验证端到端推送流程
3. **保存订阅信息** - 实现订阅信息的服务器端存储

### 可选优化
1. **推送内容定制** - 支持更丰富的推送内容
2. **订阅统计** - 显示订阅用户数量
3. **批量推送** - 支持向所有订阅用户推送

现在用户可以：
- 🔧 **自定义通知邮箱** - 不再局限于登录邮箱
- 📱 **管理PWA推送订阅** - 完整的订阅管理功能
- 🔔 **接收离线推送** - 即使关闭网页也能收到通知
- ⚙️ **灵活配置通知** - 根据需要选择通知方式

通知系统功能现在更加完善和灵活！🎉
