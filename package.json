{"name": "t3notice", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "deploy": "prisma migrate deploy && prisma generate && npm run build", "deploy:prod": "bash scripts/deploy.sh", "deploy:simple": "bash scripts/simple-deploy.sh", "deploy:safe": "bash scripts/safe-deploy.sh", "init:prod": "node scripts/production-init.js", "telegram:bot": "node scripts/start-telegram-bot.js", "dev": "next dev --turbo --port 3001", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@date-io/date-fns": "^3.2.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@mui/material-nextjs": "^7.2.0", "@mui/x-date-pickers": "^7.29.4", "@prisma/client": "^6.5.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/nodemailer": "^6.4.17", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "next-pwa": "^5.6.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.49.3", "recharts": "^3.1.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "web-push": "^3.6.7", "zod": "^3.24.2", "zustand": "^4.5.0", "@tailwindcss/postcss": "^4.0.15", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.8.2", "@faker-js/faker": "^9.9.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.7", "@types/web-push": "^3.6.4"}, "devDependencies": {"prisma": "^6.5.0", "tsx": "^4.20.3"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}