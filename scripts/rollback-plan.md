# 生产环境回滚计划

## 回滚场景

### 1. 应用代码问题（常见）
```bash
# 回滚到上一个版本
git checkout previous-stable-tag
npm run build
npm start
```
**数据库不需要回滚** - 新的迁移通常向后兼容

### 2. 数据库迁移问题（罕见）
```bash
# 停止应用
pm2 stop app

# 恢复数据库备份
psql $DATABASE_URL < backups/backup_20240122_143000.sql

# 回滚代码到迁移前的版本
git checkout pre-migration-tag
npm run build
npm start
```

## 预防措施

### 1. 测试环境验证
- 在测试环境先运行完整的升级流程
- 验证数据完整性
- 测试应用功能

### 2. 蓝绿部署
- 保持两套环境
- 新版本在绿环境测试
- 确认无误后切换流量

### 3. 数据库备份策略
- 每日自动备份
- 迁移前手动备份
- 保留多个备份版本
