import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function resetDatabase() {
  console.log('🗑️  开始清空数据库...');

  try {
    // 按照外键依赖顺序删除数据
    console.log('删除通知记录...');
    await prisma.notification.deleteMany();

    console.log('删除推送订阅...');
    await prisma.pushSubscription.deleteMany();

    console.log('删除证件记录...');
    await prisma.document.deleteMany();

    console.log('删除证件类型...');
    await prisma.documentType.deleteMany();

    console.log('删除自定义字段配置...');
    await prisma.customFieldConfig.deleteMany();

    console.log('删除成员关系...');
    await prisma.membership.deleteMany();

    console.log('删除会话...');
    await prisma.session.deleteMany();

    console.log('删除账户...');
    await prisma.account.deleteMany();

    console.log('删除用户（除超级管理员外）...');
    await prisma.user.deleteMany({
      where: {
        role: {
          not: 'SUPER_ADMIN'
        }
      }
    });

    console.log('删除商户...');
    await prisma.tenant.deleteMany();

    console.log('删除系统通知配置...');
    await prisma.systemNotificationConfig.deleteMany();

    console.log('删除验证令牌...');
    await prisma.verificationToken.deleteMany();

    // 确保超级管理员存在
    console.log('🔧 确保超级管理员账号存在...');
    
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (!existingSuperAdmin) {
      console.log('创建超级管理员账号...');
      const hashedPassword = await bcrypt.hash('123456', 12);
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '超级管理员',
          role: 'SUPER_ADMIN',
          status: 'SUBSCRIBED',
          password: hashedPassword,
          emailVerified: new Date(),
        },
      });
      
      console.log('✅ 超级管理员账号创建成功');
    } else {
      console.log('✅ 超级管理员账号已存在');
      
      // 确保超级管理员密码正确
      const hashedPassword = await bcrypt.hash('123456', 12);
      await prisma.user.update({
        where: { id: existingSuperAdmin.id },
        data: {
          password: hashedPassword,
          email: '<EMAIL>',
          name: '超级管理员',
          status: 'SUBSCRIBED',
          emailVerified: new Date(),
        },
      });
      
      console.log('✅ 超级管理员账号信息已更新');
    }

    console.log('\n🎉 数据库重置完成！');
    console.log('\n📋 当前数据库状态：');
    console.log('- 超级管理员账号: <EMAIL> / 123456');
    console.log('- 所有其他数据已清空');
    console.log('- 可以开始创建新的商户和用户进行测试');

    // 显示数据库统计
    const stats = {
      users: await prisma.user.count(),
      tenants: await prisma.tenant.count(),
      documents: await prisma.document.count(),
      documentTypes: await prisma.documentType.count(),
      memberships: await prisma.membership.count(),
      notifications: await prisma.notification.count(),
    };

    console.log('\n📊 数据库统计：');
    Object.entries(stats).forEach(([table, count]) => {
      console.log(`- ${table}: ${count} 条记录`);
    });

  } catch (error) {
    console.error('❌ 重置数据库时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行重置脚本
resetDatabase();
