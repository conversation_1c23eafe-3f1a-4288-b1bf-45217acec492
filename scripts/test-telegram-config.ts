import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testTelegramConfig() {
  console.log('🧪 测试Telegram配置保存...');

  try {
    // 查找测试用户
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        tenant: true,
        memberships: true,
      },
    });

    if (!testUser) {
      console.log('❌ 未找到测试用户');
      return;
    }

    console.log(`✅ 找到测试用户: ${testUser.email}`);
    console.log(`📧 用户角色: ${testUser.role}`);
    console.log(`🏢 关联商户: ${testUser.tenant?.name || '无'}`);
    console.log(`👥 成员关系: ${testUser.memberships.length} 个`);

    if (testUser.memberships.length > 0) {
      testUser.memberships.forEach((membership, index) => {
        console.log(`   ${index + 1}. 商户ID: ${membership.tenantId}, 角色: ${membership.role}`);
      });
    }

    // 获取商户信息
    const tenantId = testUser.currentTenantId || testUser.tenantId;
    if (!tenantId) {
      console.log('❌ 用户没有关联的商户');
      return;
    }

    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
        type: true,
        telegramChatId: true,
        notificationSettings: true,
      },
    });

    if (!tenant) {
      console.log('❌ 未找到商户');
      return;
    }

    console.log('\n📊 当前商户配置:');
    console.log(`🏢 商户名称: ${tenant.name}`);
    console.log(`📱 商户类型: ${tenant.type}`);
    console.log(`📞 Telegram ID: ${tenant.telegramChatId || '未设置'}`);
    console.log(`⚙️ 通知设置: ${JSON.stringify(tenant.notificationSettings, null, 2)}`);

    // 测试更新Telegram配置
    const testTelegramId = '123456789';
    const testSettings = {
      emailEnabled: true,
      telegramEnabled: true,
      pushEnabled: true,
      documentExpiry: true,
      documentReminder: true,
      reminderDays: [30, 7, 1],
    };

    console.log('\n🔄 测试更新配置...');
    const updatedTenant = await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        telegramChatId: testTelegramId,
        notificationSettings: testSettings,
      },
    });

    console.log('✅ 配置更新成功');
    console.log(`📞 新的Telegram ID: ${updatedTenant.telegramChatId}`);
    console.log(`⚙️ 新的通知设置: ${JSON.stringify(updatedTenant.notificationSettings, null, 2)}`);

    // 验证配置是否正确保存
    const verifyTenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      select: {
        telegramChatId: true,
        notificationSettings: true,
      },
    });

    console.log('\n🔍 验证保存结果:');
    console.log(`📞 验证Telegram ID: ${verifyTenant?.telegramChatId}`);
    console.log(`⚙️ 验证通知设置: ${JSON.stringify(verifyTenant?.notificationSettings, null, 2)}`);

    if (verifyTenant?.telegramChatId === testTelegramId) {
      console.log('✅ Telegram ID 保存成功');
    } else {
      console.log('❌ Telegram ID 保存失败');
    }

    const savedSettings = verifyTenant?.notificationSettings as any;
    if (savedSettings?.telegramEnabled === true) {
      console.log('✅ Telegram启用状态保存成功');
    } else {
      console.log('❌ Telegram启用状态保存失败');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testTelegramConfig();
