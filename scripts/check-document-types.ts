import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDocumentTypes() {
  console.log('🔍 检查证件类型数据...');

  try {
    // 获取所有商户
    const tenants = await prisma.tenant.findMany({
      include: {
        documentTypes: true,
        users: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });

    console.log(`\n📊 找到 ${tenants.length} 个商户:`);

    for (const tenant of tenants) {
      console.log(`\n🏢 商户: ${tenant.name} (ID: ${tenant.id})`);
      console.log(`   类型: ${tenant.type}`);
      console.log(`   用户数量: ${tenant.users.length}`);
      console.log(`   证件类型数量: ${tenant.documentTypes.length}`);

      if (tenant.users.length > 0) {
        console.log(`   用户列表:`);
        tenant.users.forEach(user => {
          console.log(`     - ${user.email} (${user.role})`);
        });
      }

      if (tenant.documentTypes.length > 0) {
        console.log(`   证件类型列表:`);
        tenant.documentTypes.forEach(docType => {
          console.log(`     - ${docType.name} (code: ${docType.code}, active: ${docType.isActive})`);
        });
      } else {
        console.log(`   ⚠️  该商户没有证件类型！`);
      }
    }

    // 检查是否有孤立的证件类型
    const orphanedDocTypes = await prisma.documentType.findMany({
      where: {
        tenantId: null,
      },
    });

    if (orphanedDocTypes.length > 0) {
      console.log(`\n⚠️  发现 ${orphanedDocTypes.length} 个孤立的证件类型:`);
      orphanedDocTypes.forEach(docType => {
        console.log(`   - ${docType.name} (ID: ${docType.id})`);
      });
    }

    // 检查证件数据
    const documents = await prisma.document.findMany({
      include: {
        tenant: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log(`\n📄 证件数据统计:`);
    console.log(`   总证件数量: ${documents.length}`);
    
    if (documents.length > 0) {
      const certTypes = [...new Set(documents.map(doc => doc.certType))];
      console.log(`   证件类型: ${certTypes.join(', ')}`);
    }

  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行检查脚本
checkDocumentTypes();
