import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createFreeUser() {
  console.log('🆓 创建免费用户测试账户...');

  try {
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('⚠️ 用户已存在，先删除...');
      // 删除相关数据
      await prisma.membership.deleteMany({
        where: { userId: existingUser.id }
      });
      await prisma.user.delete({
        where: { id: existingUser.id }
      });
      
      if (existingUser.tenantId) {
        await prisma.tenant.delete({
          where: { id: existingUser.tenantId }
        });
      }
    }

    // 创建商户
    const tenant = await prisma.tenant.create({
      data: {
        name: '测试免费用户商户',
        type: 'FREE',
      },
    });

    console.log(`✅ 创建商户: ${tenant.name} (类型: ${tenant.type})`);

    // 哈希密码
    const hashedPassword = await bcrypt.hash('123456', 12);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name: '测试免费用户',
        email: '<EMAIL>',
        password: hashedPassword,
        emailVerified: new Date(),
        role: 'TENANT_ADMIN',
        tenantId: tenant.id,
        currentTenantId: tenant.id,
      },
    });

    console.log(`✅ 创建用户: ${user.name} (${user.email})`);

    // 创建成员关系
    await prisma.membership.create({
      data: {
        userId: user.id,
        tenantId: tenant.id,
        role: 'ADMIN',
      },
    });

    console.log('✅ 创建成员关系');

    console.log('\n🎉 免费用户创建成功！');
    console.log('📧 邮箱: <EMAIL>');
    console.log('🔑 密码: 123456');
    console.log('🏢 商户类型: FREE (免费用户)');

  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行创建
createFreeUser();
