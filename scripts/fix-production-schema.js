#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixProductionSchema() {
  try {
    console.log('🔧 检查并修复生产环境数据库模式...');
    
    // 检查 User 表是否有 status 字段
    try {
      await prisma.$queryRaw`SELECT status FROM "User" LIMIT 1`;
      console.log('✅ User.status 字段已存在');
    } catch (error) {
      if (error.message.includes('column "status" does not exist')) {
        console.log('⚠️ User.status 字段不存在，正在添加...');
        
        // 添加 status 字段
        await prisma.$executeRaw`
          ALTER TABLE "User" 
          ADD COLUMN IF NOT EXISTS "status" TEXT DEFAULT 'ACTIVE'
        `;
        
        // 创建 UserStatus 枚举（如果不存在）
        await prisma.$executeRaw`
          DO $$ BEGIN
            CREATE TYPE "UserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
          EXCEPTION
            WHEN duplicate_object THEN null;
          END $$;
        `;
        
        // 修改字段类型为枚举
        await prisma.$executeRaw`
          ALTER TABLE "User" 
          ALTER COLUMN "status" TYPE "UserStatus" USING "status"::"UserStatus"
        `;
        
        console.log('✅ User.status 字段添加成功');
      } else {
        throw error;
      }
    }
    
    // 检查其他可能缺失的字段...
    
    console.log('🎉 数据库模式修复完成！');
    
  } catch (error) {
    console.error('❌ 模式修复失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 只在直接运行此脚本时执行
if (process.argv[1].endsWith('fix-production-schema.js')) {
  fixProductionSchema();
}

export { fixProductionSchema };
