import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createSuperAdmin() {
  console.log('🚀 开始创建超级管理员账号...');

  try {
    // 超级管理员账号信息
    const superAdminData = {
      name: 'Super Admin',
      email: '<EMAIL>',
      password: '123456', // 默认密码，建议首次登录后修改
    };

    console.log(`📧 邮箱: ${superAdminData.email}`);
    console.log(`🔑 密码: ${superAdminData.password}`);

    // 检查是否已存在超级管理员
    const existingAdmin = await prisma.user.findUnique({
      where: { email: superAdminData.email },
    });

    if (existingAdmin) {
      console.log('⚠️  超级管理员账号已存在');
      
      if (existingAdmin.role === 'SUPER_ADMIN') {
        console.log('✅ 现有账号已经是超级管理员');
        return;
      } else {
        // 如果存在但不是超级管理员，升级为超级管理员
        console.log('🔄 升级现有账号为超级管理员...');
        await prisma.user.update({
          where: { id: existingAdmin.id },
          data: { role: 'SUPER_ADMIN' },
        });
        console.log('✅ 账号已升级为超级管理员');
        return;
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(superAdminData.password, 12);

    // 创建超级管理员账号
    const superAdmin = await prisma.user.create({
      data: {
        name: superAdminData.name,
        email: superAdminData.email,
        password: hashedPassword,
        role: 'SUPER_ADMIN',
        // 超级管理员不需要关联特定商户
        tenantId: null,
        currentTenantId: null,
      },
    });

    console.log('✅ 超级管理员账号创建成功！');
    console.log(`👤 用户ID: ${superAdmin.id}`);
    console.log(`📧 邮箱: ${superAdmin.email}`);
    console.log(`👑 角色: ${superAdmin.role}`);

    // 创建系统通知配置（如果不存在）
    const existingConfig = await prisma.systemNotificationConfig.findFirst();
    
    if (!existingConfig) {
      console.log('🔧 创建默认系统通知配置...');
      await prisma.systemNotificationConfig.create({
        data: {
          telegramBotToken: null,
          telegramBotName: null,
          emailProvider: 'SMTP',
          emailApiKey: null,
          emailFromAddress: '<EMAIL>',
          emailFromName: 'T3 Notice System',
          vapidPublicKey: null,
          vapidPrivateKey: null,
          vapidSubject: 'mailto:<EMAIL>',
        },
      });
      console.log('✅ 默认系统通知配置已创建');
    }

    // 创建默认套餐（如果不存在）
    const existingPlans = await prisma.subscriptionPlan.count();
    
    if (existingPlans === 0) {
      console.log('📦 创建默认套餐...');
      
      const defaultPlans = [
        {
          name: '免费版',
          description: '适合个人用户的基础功能',
          price: 0,
          currency: 'CNY',
          billingCycle: 'MONTHLY' as const,
          features: ['最多10个证件', '基础通知', '数据导出'],
          limits: {
            maxDocuments: 10,
            maxUsers: 1,
            maxStorage: 100, // 100MB
            maxNotifications: 50,
          },
          isActive: true,
          sortOrder: 1,
        },
        {
          name: '基础版',
          description: '适合小型企业的标准功能',
          price: 99,
          currency: 'CNY',
          billingCycle: 'MONTHLY' as const,
          features: ['最多100个证件', '高级通知', '数据导出', '多用户支持'],
          limits: {
            maxDocuments: 100,
            maxUsers: 5,
            maxStorage: 500, // 500MB
            maxNotifications: 200,
          },
          isActive: true,
          sortOrder: 2,
        },
        {
          name: '专业版',
          description: '适合中型企业的专业功能',
          price: 299,
          currency: 'CNY',
          billingCycle: 'MONTHLY' as const,
          features: ['无限证件', '高级通知', '数据导出', '多用户支持', 'API访问'],
          limits: {
            maxDocuments: -1, // 无限制
            maxUsers: 20,
            maxStorage: 2048, // 2GB
            maxNotifications: -1,
          },
          isActive: true,
          sortOrder: 3,
        },
        {
          name: '企业版',
          description: '适合大型企业的完整功能',
          price: 999,
          currency: 'CNY',
          billingCycle: 'MONTHLY' as const,
          features: ['无限证件', '高级通知', '数据导出', '无限用户', 'API访问', '专属支持'],
          limits: {
            maxDocuments: -1,
            maxUsers: -1,
            maxStorage: -1, // 无限制
            maxNotifications: -1,
          },
          isActive: true,
          sortOrder: 4,
        },
      ];

      for (const planData of defaultPlans) {
        await prisma.subscriptionPlan.create({
          data: planData,
        });
      }
      
      console.log(`✅ 已创建 ${defaultPlans.length} 个默认套餐`);
    }

    console.log('\n🎉 超级管理员账号设置完成！');
    console.log('\n📋 登录信息:');
    console.log(`   邮箱: ${superAdminData.email}`);
    console.log(`   密码: ${superAdminData.password}`);
    console.log('\n⚠️  安全提醒:');
    console.log('   1. 请在首次登录后立即修改密码');
    console.log('   2. 建议启用双因素认证（如果支持）');
    console.log('   3. 定期检查系统安全设置');
    console.log('\n🔗 管理功能:');
    console.log('   • 商户管理: /admin/merchants-enhanced');
    console.log('   • 套餐管理: /admin/plans');
    console.log('   • 订单管理: /admin/orders');
    console.log('   • 通知配置: /admin/notifications');

  } catch (error) {
    console.error('❌ 创建超级管理员失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
createSuperAdmin()
  .then(() => {
    console.log('\n✨ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error);
    process.exit(1);
  });
