import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateTenantTypes() {
  console.log('开始迁移商户类型...');

  try {
    // 获取所有现有商户
    const tenants = await prisma.tenant.findMany({
      include: {
        memberships: true,
      },
    });

    console.log(`找到 ${tenants.length} 个商户`);

    for (const tenant of tenants) {
      let newType = tenant.type;

      // 如果当前类型是PERSONAL，检查是否应该设为FREE
      if (tenant.type === 'PERSONAL' && tenant.memberships.length === 1) {
        // 单用户的个人商户可能应该是FREE类型
        console.log(`商户 ${tenant.name} (${tenant.id}) 保持为 PERSONAL 类型`);
        continue;
      }

      // 如果需要，可以在这里添加其他迁移逻辑
      console.log(`商户 ${tenant.name} (${tenant.id}) 类型: ${tenant.type} -> ${newType}`);
    }

    console.log('商户类型迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行迁移
migrateTenantTypes();
