#!/bin/bash

# 超简单的生产环境部署脚本
set -e

echo "🚀 开始简单部署..."

# 1. 安装依赖
echo "📦 安装依赖..."
npm ci

# 2. 生成 Prisma 客户端
echo "⚙️ 生成 Prisma 客户端..."
npx prisma generate

# 3. 强制重置并同步数据库（这会删除所有数据！）
echo "🗄️ 强制重置数据库..."
npx prisma db push --force-reset --accept-data-loss

# 4. 初始化生产环境数据
echo "🔧 初始化生产环境..."
node scripts/production-init.js

# 5. 构建项目
echo "🔨 构建项目..."
npm run build

echo "🎉 简单部署完成！"
echo "⚠️ 注意：此脚本会删除数据库中的所有现有数据"
