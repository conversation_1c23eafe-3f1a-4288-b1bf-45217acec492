import { PrismaClient } from '@prisma/client';
import { ActivityLogService } from '../src/server/services/activity-log-service';

const prisma = new PrismaClient();

async function createTestActivityLogs() {
  try {
    console.log('🚀 开始创建测试操作日志...');

    // 获取第一个用户和商户
    const user = await prisma.user.findFirst({
      where: {
        role: 'TENANT_ADMIN',
      },
    });

    const tenant = await prisma.tenant.findFirst({
      where: {
        isActive: true,
      },
    });

    if (!user || !tenant) {
      console.log('❌ 未找到用户或商户，请先创建测试数据');
      return;
    }

    console.log(`📝 使用用户: ${user.name} (${user.email})`);
    console.log(`🏢 使用商户: ${tenant.name}`);

    // 创建一些测试操作日志
    const testLogs = [
      {
        action: '创建证件',
        details: '添加了张三的身份证',
        entityType: 'Document',
        userId: user.id,
        tenantId: tenant.id,
      },
      {
        action: '更新证件',
        details: '修改了李四的驾驶证到期时间',
        entityType: 'Document',
        userId: user.id,
        tenantId: tenant.id,
      },
      {
        action: '删除证件',
        details: '删除了王五的过期护照',
        entityType: 'Document',
        userId: user.id,
        tenantId: tenant.id,
      },
      {
        action: '创建用户',
        details: '添加了新用户赵六',
        entityType: 'User',
        userId: user.id,
        tenantId: tenant.id,
      },
      {
        action: '发送通知',
        details: '发送了证件到期提醒通知',
        entityType: 'Notification',
        userId: user.id,
        tenantId: tenant.id,
      },
    ];

    // 批量创建操作日志
    for (const logData of testLogs) {
      await ActivityLogService.log(logData);
      // 添加一些时间间隔
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('✅ 测试操作日志创建完成！');

    // 查询并显示创建的日志
    const logs = await prisma.activityLog.findMany({
      where: {
        tenantId: tenant.id,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    });

    console.log('\n📋 创建的操作日志:');
    logs.forEach((log, index) => {
      console.log(`${index + 1}. ${log.action} - ${log.details}`);
      console.log(`   操作人员: ${log.user.name || log.user.email}`);
      console.log(`   时间: ${log.createdAt.toLocaleString()}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ 创建测试操作日志失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
createTestActivityLogs();
