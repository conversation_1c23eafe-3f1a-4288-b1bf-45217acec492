#!/bin/bash

# 生产环境部署脚本
set -e

echo "🚀 开始生产环境部署..."

# 检查必要的环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

if [ -z "$NEXTAUTH_SECRET" ]; then
    echo "❌ 错误: NEXTAUTH_SECRET 环境变量未设置"
    exit 1
fi

# 1. 安装依赖
echo "📦 安装生产依赖..."
npm ci --only=production

# 2. 生成 Prisma 客户端
echo "⚙️ 生成 Prisma 客户端..."
npx prisma generate

# 3. 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
npx prisma migrate deploy

# 4. 初始化生产环境数据
echo "🔧 初始化生产环境..."
node scripts/production-init.js

# 5. 构建项目
echo "🔨 构建项目..."
npm run build

echo "🎉 生产环境部署完成！"
echo "📝 请记录以下信息："
echo "   - 超级管理员邮箱: ${ADMIN_EMAIL:-<EMAIL>}"
echo "   - 请立即登录并修改默认密码"
echo "   - 在管理面板中配置邮件和通知服务"
