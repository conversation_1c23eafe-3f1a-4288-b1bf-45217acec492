#!/bin/bash

# 生产环境部署脚本
set -e

echo "🚀 开始生产环境部署..."

# 1. 安装依赖
echo "📦 安装依赖..."
npm ci

# 2. 生成 Prisma 客户端
echo "⚙️ 生成 Prisma 客户端..."
npx prisma generate

# 3. 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
npx prisma migrate deploy

# 4. 修复生产环境数据库模式
echo "🔧 修复数据库模式..."
node scripts/fix-production-schema.js

# 5. 初始化生产环境数据
echo "🔧 初始化生产环境..."
node scripts/production-init.js

# 6. 构建项目
echo "🔨 构建项目..."
npm run build

echo "🎉 生产环境部署完成！"
