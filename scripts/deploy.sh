#!/bin/bash

# 生产环境部署脚本
set -e

echo "🚀 开始生产环境部署..."

# 1. 安装依赖
echo "📦 安装依赖..."
npm ci

# 2. 生成 Prisma 客户端
echo "⚙️ 生成 Prisma 客户端..."
npx prisma generate

# 3. 同步数据库模式
echo "🗄️ 同步数据库模式..."
npx prisma db push --accept-data-loss

# 4. 运行数据库迁移（如果有的话）
echo "🔄 运行数据库迁移..."
npx prisma migrate deploy || echo "⚠️ 没有待执行的迁移"

# 5. 初始化生产环境数据
echo "🔧 初始化生产环境..."
node scripts/production-init.js

# 6. 构建项目
echo "🔨 构建项目..."
npm run build

echo "🎉 生产环境部署完成！"
