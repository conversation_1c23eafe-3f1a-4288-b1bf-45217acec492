import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixDocumentTypes() {
  console.log('🔧 修复证件类型数据...');

  try {
    // 查找有问题的证件类型
    const problematicTypes = await prisma.documentType.findMany({
      where: {
        code: {
          contains: '___',
        },
      },
    });

    console.log(`\n🔍 找到 ${problematicTypes.length} 个有问题的证件类型:`);
    
    for (const docType of problematicTypes) {
      console.log(`   - ${docType.name} (code: ${docType.code})`);
      
      // 检查是否有证件使用了这个类型
      const documentsUsingType = await prisma.document.findMany({
        where: {
          certType: docType.name, // 使用name字段查找
        },
      });

      console.log(`     关联的证件数量: ${documentsUsingType.length}`);

      if (documentsUsingType.length === 0) {
        // 如果没有证件使用这个类型，直接删除
        await prisma.documentType.delete({
          where: { id: docType.id },
        });
        console.log(`     ✅ 已删除未使用的证件类型: ${docType.name}`);
      } else {
        // 如果有证件使用，修复code字段
        let newCode = docType.name;
        
        // 生成合适的英文代码
        switch (docType.name) {
          case '学籍证':
            newCode = 'STUDENT_CERTIFICATE';
            break;
          case '身份证':
            newCode = 'ID_CARD';
            break;
          case '护照':
            newCode = 'PASSPORT';
            break;
          default:
            // 生成基于名称的代码
            newCode = docType.name
              .replace(/[^\w\s]/g, '') // 移除特殊字符
              .replace(/\s+/g, '_')     // 空格替换为下划线
              .toUpperCase() + '_CERTIFICATE';
        }

        await prisma.documentType.update({
          where: { id: docType.id },
          data: { code: newCode },
        });
        
        console.log(`     ✅ 已修复证件类型代码: ${docType.name} -> ${newCode}`);
      }
    }

    // 检查是否有重复的证件类型名称
    const allTypes = await prisma.documentType.findMany({
      orderBy: [
        { tenantId: 'asc' },
        { name: 'asc' },
      ],
    });

    const duplicates = new Map<string, any[]>();
    
    for (const docType of allTypes) {
      const key = `${docType.tenantId}-${docType.name}`;
      if (!duplicates.has(key)) {
        duplicates.set(key, []);
      }
      duplicates.get(key)!.push(docType);
    }

    console.log(`\n🔍 检查重复的证件类型...`);
    let duplicateCount = 0;
    
    for (const [key, types] of duplicates.entries()) {
      if (types.length > 1) {
        duplicateCount++;
        console.log(`   重复类型: ${types[0].name} (${types.length} 个)`);
        
        // 保留第一个，删除其他的
        for (let i = 1; i < types.length; i++) {
          const typeToDelete = types[i];
          
          // 检查是否有证件使用
          const documentsUsingType = await prisma.document.findMany({
            where: {
              certType: typeToDelete.name,
              tenantId: typeToDelete.tenantId,
            },
          });

          if (documentsUsingType.length === 0) {
            await prisma.documentType.delete({
              where: { id: typeToDelete.id },
            });
            console.log(`     ✅ 已删除重复的证件类型`);
          } else {
            console.log(`     ⚠️  重复类型有关联证件，跳过删除`);
          }
        }
      }
    }

    if (duplicateCount === 0) {
      console.log(`   ✅ 没有发现重复的证件类型`);
    }

    // 显示修复后的统计
    const finalStats = await prisma.documentType.groupBy({
      by: ['tenantId'],
      _count: {
        id: true,
      },
    });

    console.log(`\n📊 修复后的证件类型统计:`);
    for (const stat of finalStats) {
      const tenant = await prisma.tenant.findUnique({
        where: { id: stat.tenantId },
        select: { name: true },
      });
      console.log(`   ${tenant?.name || '未知商户'}: ${stat._count.id} 个证件类型`);
    }

    console.log('\n🎉 证件类型修复完成！');

  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复脚本
fixDocumentTypes();
