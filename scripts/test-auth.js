#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('🔍 测试数据库连接和认证...');
    
    // 测试数据库连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 查找超级管理员
    const superAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (superAdmin) {
      console.log('✅ 超级管理员账户存在');
      console.log(`   ID: ${superAdmin.id}`);
      console.log(`   邮箱: ${superAdmin.email}`);
      console.log(`   角色: ${superAdmin.role}`);
      console.log(`   密码哈希: ${superAdmin.password ? '已设置' : '未设置'}`);
    } else {
      console.log('❌ 超级管理员账户不存在');
      
      // 检查是否有任何用户
      const userCount = await prisma.user.count();
      console.log(`📊 数据库中共有 ${userCount} 个用户`);
      
      if (userCount > 0) {
        const users = await prisma.user.findMany({
          select: { email: true, role: true },
          take: 5
        });
        console.log('前5个用户:');
        users.forEach(user => {
          console.log(`   - ${user.email} (${user.role})`);
        });
      }
    }

    // 测试其他测试账户
    const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    
    for (const email of testEmails) {
      const user = await prisma.user.findUnique({
        where: { email },
        select: { email: true, role: true }
      });
      
      if (user) {
        console.log(`✅ 测试账户 ${email} 存在 (${user.role})`);
      } else {
        console.log(`❌ 测试账户 ${email} 不存在`);
      }
    }

    console.log('🎉 认证测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();
