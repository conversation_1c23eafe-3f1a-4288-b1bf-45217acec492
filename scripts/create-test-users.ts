import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUsers() {
  console.log('🧪 创建测试用户...');

  try {
    // 获取现有商户
    const tenants = await prisma.tenant.findMany({
      take: 2,
      orderBy: { createdAt: 'desc' },
    });

    if (tenants.length === 0) {
      console.log('❌ 没有找到商户，请先创建商户');
      return;
    }

    const testUsers = [
      {
        name: '商户管理员',
        email: '<EMAIL>',
        password: '123456',
        role: 'TENANT_ADMIN' as const,
        tenantId: tenants[0]?.id,
      },
      {
        name: '商户成员',
        email: '<EMAIL>',
        password: '123456',
        role: 'TENANT_MEMBER' as const,
        tenantId: tenants[0]?.id,
      },
      {
        name: '商户用户',
        email: '<EMAIL>',
        password: '123456',
        role: 'TENANT_MEMBER' as const,
        tenantId: tenants[1]?.id || tenants[0]?.id,
      },
    ];

    for (const userData of testUsers) {
      // 检查用户是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email },
      });

      if (existingUser) {
        console.log(`⚠️  用户已存在: ${userData.email}`);
        continue;
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(userData.password, 12);

      // 创建用户
      const user = await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: userData.role,
          tenantId: userData.tenantId,
          currentTenantId: userData.tenantId,
        },
      });

      // 创建商户成员关系
      await prisma.membership.create({
        data: {
          userId: user.id,
          tenantId: userData.tenantId!,
          role: userData.role,
        },
      });

      console.log(`✅ 创建用户: ${userData.name} (${userData.email})`);
    }

    console.log('\n📋 测试账号列表:');
    console.log('1. 超级管理员: <EMAIL> / 123456');
    console.log('2. 商户管理员: <EMAIL> / 123456');
    console.log('3. 商户成员: <EMAIL> / 123456');
    console.log('4. 商户用户: <EMAIL> / 123456');

    console.log('\n🧪 测试步骤:');
    console.log('1. 访问: http://localhost:3001/admin/test-tenant-disable');
    console.log('2. 禁用一个商户');
    console.log('3. 使用该商户下的用户账号登录');
    console.log('4. 验证错误信息是否正确显示');

    // 显示商户信息
    console.log('\n🏢 商户信息:');
    tenants.forEach((tenant, index) => {
      console.log(`${index + 1}. ${tenant.name} (${tenant.type}) - ${tenant.isActive ? '启用' : '禁用'}`);
    });

  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
createTestUsers()
  .then(() => {
    console.log('\n✨ 测试用户创建完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 创建失败:', error);
    process.exit(1);
  });
