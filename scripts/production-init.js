#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function initProduction() {
  try {
    console.log('🚀 开始生产环境初始化...');
    
    // 1. 检查数据库连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 2. 运行迁移
    console.log('📦 检查数据库迁移状态...');
    
    // 3. 检查是否已有超级管理员
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (existingSuperAdmin) {
      console.log('✅ 超级管理员已存在，跳过创建');
      console.log(`   邮箱: ${existingSuperAdmin.email}`);
    } else {
      // 4. 创建超级管理员
      console.log('👤 创建超级管理员账户...');
      
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.ADMIN_PASSWORD || 'ChangeMe123!';
      
      if (adminPassword === 'ChangeMe123!') {
        console.log('⚠️ 警告: 使用默认密码，请在生产环境中修改！');
      }

      const hashedPassword = await bcrypt.hash(adminPassword, 12);

      const superAdmin = await prisma.user.create({
        data: {
          email: adminEmail,
          name: '系统管理员',
          password: hashedPassword,
          role: 'SUPER_ADMIN',
          emailVerified: new Date(),
        },
      });

      console.log('✅ 超级管理员创建成功');
      console.log(`   邮箱: ${superAdmin.email}`);
      console.log(`   密码: ${adminPassword}`);
      console.log('   ⚠️ 请立即登录并修改密码！');
    }

    // 5. 创建默认订阅计划
    await createDefaultPlans();

    // 6. 创建基础系统配置（其他配置在后台设置）
    await createSystemConfig();

    console.log('🎉 生产环境初始化完成！');
    
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function createDefaultPlans() {
  console.log('📋 创建默认订阅计划...');
  
  const plans = [
    {
      name: '免费试用',
      description: '适合个人用户试用',
      price: 0,
      currency: 'USD',
      duration: 30,
      features: {
        documents: 10,
        users: 1,
        customFields: 5,
        notifications: 50,
        support: '社区支持',
        telegram: true,
        pwa: true,
        export: false,
      },
      limits: {
        maxDocuments: 10,
        maxUsers: 1,
        maxCustomFields: 5,
        maxNotifications: 50,
      },
      isActive: true,
    },
    {
      name: '个人版',
      description: '适合个人和小团队',
      price: 118,
      currency: 'USD',
      duration: 365,
      features: {
        documents: 500,
        users: 3,
        customFields: 20,
        notifications: 1000,
        support: '邮件支持',
        telegram: true,
        pwa: true,
        export: true,
      },
      limits: {
        maxDocuments: 500,
        maxUsers: 3,
        maxCustomFields: 20,
        maxNotifications: 1000,
      },
      isActive: true,
    },
    {
      name: '企业版',
      description: '适合大型团队和企业',
      price: 288,
      currency: 'USD',
      duration: 365,
      features: {
        documents: -1,
        users: -1,
        customFields: -1,
        notifications: -1,
        support: '优先支持',
        telegram: true,
        pwa: true,
        export: true,
      },
      limits: {
        maxDocuments: -1,
        maxUsers: -1,
        maxCustomFields: -1,
        maxNotifications: -1,
      },
      isActive: true,
    },
  ];

  for (const planData of plans) {
    const existing = await prisma.subscriptionPlan.findFirst({
      where: { name: planData.name }
    });

    if (!existing) {
      await prisma.subscriptionPlan.create({ data: planData });
      console.log(`✅ 创建订阅计划: ${planData.name}`);
    } else {
      console.log(`⏭️ 订阅计划已存在: ${planData.name}`);
    }
  }
}

async function createSystemConfig() {
  console.log('⚙️ 创建基础系统配置...');

  const existing = await prisma.systemNotificationConfig.findFirst();

  if (!existing) {
    await prisma.systemNotificationConfig.create({
      data: {
        telegramBotToken: '',
        emailProvider: 'SMTP',
        smtpHost: '',
        smtpPort: 587,
        smtpSecure: false,
        smtpUser: '',
        smtpPassword: '',
        emailFromAddress: '',
        emailFromName: '证件提醒系统',
        vapidPublicKey: '',
        vapidPrivateKey: '',
        vapidEmail: '',
      },
    });
    console.log('✅ 基础系统配置创建成功');
    console.log('📝 请登录管理后台完成详细配置：');
    console.log('   - 邮件服务配置');
    console.log('   - Telegram Bot 配置');
    console.log('   - PWA 推送配置');
  } else {
    console.log('⏭️ 系统配置已存在');
  }
}

// 只在直接运行此脚本时执行
if (process.argv[1].endsWith('production-init.js')) {
  initProduction();
}
