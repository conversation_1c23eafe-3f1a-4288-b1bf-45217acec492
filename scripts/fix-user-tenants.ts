import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixUserTenants() {
  console.log('🔧 开始修复用户商户关联...');

  try {
    // 获取所有用户
    const users = await prisma.user.findMany({
      include: {
        memberships: true,
      },
    });

    for (const user of users) {
      console.log(`\n👤 处理用户: ${user.email} (${user.role})`);

      // 如果用户没有currentTenantId，但有成员关系
      if (!user.currentTenantId && user.memberships.length > 0) {
        const firstTenantId = user.memberships[0]?.tenantId;
        
        if (firstTenantId) {
          await prisma.user.update({
            where: { id: user.id },
            data: { currentTenantId: firstTenantId },
          });
          
          console.log(`✅ 设置 currentTenantId: ${firstTenantId}`);
        }
      }

      // 如果用户有tenantId但没有成员关系，创建成员关系
      if (user.tenantId && user.memberships.length === 0) {
        const role = user.role === 'TENANT_ADMIN' ? 'TENANT_ADMIN' : 'TENANT_MEMBER';
        
        await prisma.membership.create({
          data: {
            userId: user.id,
            tenantId: user.tenantId,
            role,
          },
        });
        
        console.log(`✅ 创建成员关系: ${role}`);

        // 同时设置currentTenantId
        if (!user.currentTenantId) {
          await prisma.user.update({
            where: { id: user.id },
            data: { currentTenantId: user.tenantId },
          });
          
          console.log(`✅ 设置 currentTenantId: ${user.tenantId}`);
        }
      }

      // 显示用户当前状态
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        include: {
          memberships: true,
        },
      });

      console.log(`📊 用户状态:`);
      console.log(`   - currentTenantId: ${updatedUser?.currentTenantId || '未设置'}`);
      console.log(`   - tenantId: ${updatedUser?.tenantId || '未设置'}`);
      console.log(`   - 成员关系数量: ${updatedUser?.memberships.length || 0}`);
      
      if (updatedUser?.memberships.length) {
        updatedUser.memberships.forEach((membership, index) => {
          console.log(`     ${index + 1}. 商户: ${membership.tenantId}, 角色: ${membership.role}`);
        });
      }
    }

    console.log('\n🎉 用户商户关联修复完成！');

  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复脚本
fixUserTenants();
