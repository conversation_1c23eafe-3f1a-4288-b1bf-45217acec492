import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testAdminTenant() {
  console.log('🧪 测试超级管理员商户管理功能...');

  try {
    // 查看当前所有商户
    const allTenants = await prisma.tenant.findMany({
      include: {
        _count: {
          select: {
            memberships: true,
            documents: true,
            subscriptions: true,
          },
        },
        memberships: {
          where: { role: 'ADMIN' },
          take: 1,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 当前系统中共有 ${allTenants.length} 个商户:\n`);

    allTenants.forEach((tenant, index) => {
      console.log(`🏢 商户 #${index + 1}:`);
      console.log(`   ID: ${tenant.id}`);
      console.log(`   名称: ${tenant.name}`);
      console.log(`   类型: ${tenant.type}`);
      console.log(`   状态: ${tenant.isActive ? '活跃' : '禁用'}`);
      console.log(`   描述: ${tenant.description || '无'}`);
      console.log(`   联系邮箱: ${tenant.email || '无'}`);
      console.log(`   联系电话: ${tenant.phone || '无'}`);
      console.log(`   地址: ${tenant.address || '无'}`);
      console.log(`   成员数: ${tenant._count.memberships}`);
      console.log(`   证件数: ${tenant._count.documents}`);
      console.log(`   订阅数: ${tenant._count.subscriptions}`);
      
      if (tenant.memberships[0]) {
        console.log(`   管理员: ${tenant.memberships[0].user.name} (${tenant.memberships[0].user.email})`);
      }
      
      console.log(`   创建时间: ${tenant.createdAt.toLocaleString()}`);
      console.log('');
    });

    // 统计信息
    const stats = {
      total: allTenants.length,
      active: allTenants.filter(t => t.isActive).length,
      byType: allTenants.reduce((acc, tenant) => {
        acc[tenant.type] = (acc[tenant.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      totalUsers: allTenants.reduce((sum, t) => sum + t._count.memberships, 0),
      totalDocuments: allTenants.reduce((sum, t) => sum + t._count.documents, 0),
    };

    console.log('📈 统计信息:');
    console.log(`   总商户数: ${stats.total}`);
    console.log(`   活跃商户: ${stats.active}`);
    console.log(`   总用户数: ${stats.totalUsers}`);
    console.log(`   总证件数: ${stats.totalDocuments}`);
    console.log('');

    console.log('📊 按类型分布:');
    Object.entries(stats.byType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} 个`);
    });

    // 检查超级管理员账号
    const superAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    console.log('\n👑 超级管理员信息:');
    if (superAdmin) {
      console.log(`   姓名: ${superAdmin.name}`);
      console.log(`   邮箱: ${superAdmin.email}`);
      console.log(`   角色: ${superAdmin.role}`);
      console.log('   ✅ 可以访问商户管理功能');
    } else {
      console.log('   ❌ 未找到超级管理员账号');
    }

    console.log('\n🔗 管理页面链接:');
    console.log('   • 登录: http://localhost:3001/auth/signin');
    console.log('   • 商户管理: http://localhost:3001/admin/merchants-enhanced');
    console.log('   • 套餐管理: http://localhost:3001/admin/plans');
    console.log('   • 订单管理: http://localhost:3001/admin/orders');

    // 测试创建一个示例商户（如果没有足够的测试数据）
    if (allTenants.length < 3) {
      console.log('\n🔧 创建测试商户数据...');
      
      const testTenants = [
        {
          name: '测试企业A',
          type: 'ENTERPRISE' as const,
          description: '这是一个测试企业商户',
          email: '<EMAIL>',
          phone: '13800138001',
          address: '北京市朝阳区测试大厦',
          isActive: true,
        },
        {
          name: '测试个人B',
          type: 'PERSONAL' as const,
          description: '这是一个测试个人商户',
          email: '<EMAIL>',
          phone: '13800138002',
          address: '上海市浦东新区测试小区',
          isActive: true,
        },
      ];

      for (const tenantData of testTenants) {
        // 检查是否已存在
        const existing = await prisma.tenant.findFirst({
          where: { name: tenantData.name },
        });

        if (!existing) {
          const newTenant = await prisma.tenant.create({
            data: tenantData,
          });
          console.log(`   ✅ 创建测试商户: ${newTenant.name}`);
        } else {
          console.log(`   ⚠️  测试商户已存在: ${tenantData.name}`);
        }
      }
    }

    console.log('\n✨ 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testAdminTenant()
  .then(() => {
    console.log('\n🎉 所有测试通过');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error);
    process.exit(1);
  });
