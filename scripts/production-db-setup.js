#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';

const prisma = new PrismaClient();

async function setupProductionDatabase() {
  try {
    console.log('🗄️ 设置生产环境数据库...');
    
    // 1. 检查数据库是否为空
    const isEmpty = await isDatabaseEmpty();
    
    if (isEmpty) {
      console.log('📝 检测到空数据库，执行完整设置...');
      await setupEmptyDatabase();
    } else {
      console.log('🔄 检测到现有数据库，执行增量更新...');
      await updateExistingDatabase();
    }
    
    console.log('✅ 数据库设置完成！');
    
  } catch (error) {
    console.error('❌ 数据库设置失败:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function isDatabaseEmpty() {
  try {
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
    `;
    return tables.length === 0;
  } catch (error) {
    return true; // 如果查询失败，假设是空数据库
  }
}

async function setupEmptyDatabase() {
  console.log('🚀 设置空数据库...');

  try {
    // 对于空数据库，强制推送完整模式
    console.log('📦 强制推送数据库模式...');
    execSync('npx prisma db push --force-reset --accept-data-loss', {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    console.log('✅ 空数据库设置完成');
  } catch (error) {
    console.error('❌ 空数据库设置失败，尝试普通推送...');
    try {
      execSync('npx prisma db push --accept-data-loss', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ 普通推送成功');
    } catch (fallbackError) {
      console.error('❌ 所有推送方式都失败:', fallbackError.message);
      throw fallbackError;
    }
  }
}

async function updateExistingDatabase() {
  console.log('🔄 更新现有数据库...');

  try {
    // 对于现有数据库，强制重置以确保模式完整
    console.log('⚠️ 强制重置数据库以确保模式完整...');

    try {
      execSync('npx prisma db push --force-reset --accept-data-loss', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ 强制重置成功');
    } catch (resetError) {
      console.log('⚠️ 强制重置失败，尝试普通同步...');

      execSync('npx prisma db push --accept-data-loss', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ 普通同步成功');
    }

    console.log('✅ 现有数据库更新完成');
  } catch (error) {
    console.error('❌ 数据库更新失败:', error.message);
    throw error;
  }
}

// 只在直接运行此脚本时执行
if (process.argv[1].endsWith('production-db-setup.js')) {
  setupProductionDatabase().catch((error) => {
    console.error('💥 数据库设置失败:', error);
    process.exit(1);
  });
}

export { setupProductionDatabase };
