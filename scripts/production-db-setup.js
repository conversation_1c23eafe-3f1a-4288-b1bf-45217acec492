#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';

const prisma = new PrismaClient();

async function setupProductionDatabase() {
  try {
    console.log('🗄️ 设置生产环境数据库...');
    
    // 1. 检查数据库是否为空
    const isEmpty = await isDatabaseEmpty();
    
    if (isEmpty) {
      console.log('📝 检测到空数据库，执行完整设置...');
      await setupEmptyDatabase();
    } else {
      console.log('🔄 检测到现有数据库，执行增量更新...');
      await updateExistingDatabase();
    }
    
    console.log('✅ 数据库设置完成！');
    
  } catch (error) {
    console.error('❌ 数据库设置失败:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function isDatabaseEmpty() {
  try {
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
    `;
    return tables.length === 0;
  } catch (error) {
    return true; // 如果查询失败，假设是空数据库
  }
}

async function setupEmptyDatabase() {
  console.log('🚀 设置空数据库...');
  
  try {
    // 对于空数据库，直接使用 db push
    console.log('📦 推送数据库模式...');
    execSync('npx prisma db push --accept-data-loss', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('✅ 空数据库设置完成');
  } catch (error) {
    console.error('❌ 空数据库设置失败:', error.message);
    throw error;
  }
}

async function updateExistingDatabase() {
  console.log('🔄 更新现有数据库...');
  
  try {
    // 对于现有数据库，先尝试迁移
    try {
      console.log('📦 尝试运行迁移...');
      execSync('npx prisma migrate deploy', { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
    } catch (migrateError) {
      console.log('⚠️ 迁移失败，尝试强制同步...');
      
      // 如果迁移失败，使用 db push
      execSync('npx prisma db push --accept-data-loss', { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
    }
    
    console.log('✅ 现有数据库更新完成');
  } catch (error) {
    console.error('❌ 数据库更新失败:', error.message);
    throw error;
  }
}

// 只在直接运行此脚本时执行
if (process.argv[1].endsWith('production-db-setup.js')) {
  setupProductionDatabase().catch((error) => {
    console.error('💥 数据库设置失败:', error);
    process.exit(1);
  });
}

export { setupProductionDatabase };
