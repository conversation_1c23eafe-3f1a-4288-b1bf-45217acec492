#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyMigration() {
  try {
    console.log('🔍 验证数据库连接...');
    
    // 测试基本连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 检查关键表是否存在
    const tables = [
      'User',
      'Tenant', 
      'Document',
      'DocumentType',
      'Subscription',
      'SubscriptionPlan'
    ];

    for (const table of tables) {
      try {
        const count = await prisma[table.toLowerCase()].count();
        console.log(`✅ 表 ${table} 存在，记录数: ${count}`);
      } catch (error) {
        console.error(`❌ 表 ${table} 不存在或有问题:`, error.message);
      }
    }

    // 检查是否有超级管理员
    const superAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });
    
    if (superAdmin) {
      console.log('✅ 超级管理员账户存在');
    } else {
      console.log('⚠️ 未找到超级管理员账户，可能需要运行种子数据');
    }

    console.log('🎉 数据库验证完成！');
    
  } catch (error) {
    console.error('❌ 数据库验证失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

verifyMigration();
