#!/bin/bash

# 安全迁移脚本
set -e

echo "🛡️ 开始安全迁移流程..."

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

# 创建备份目录
BACKUP_DIR="backups"
mkdir -p $BACKUP_DIR

# 生成备份文件名
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql"

echo "📦 创建数据库备份: $BACKUP_FILE"

# 备份数据库
if command -v pg_dump &> /dev/null; then
    pg_dump "$DATABASE_URL" > "$BACKUP_FILE"
    echo "✅ 数据库备份完成"
else
    echo "⚠️ 警告: pg_dump 未找到，跳过备份"
fi

# 检查迁移状态
echo "🔍 检查迁移状态..."
npx prisma migrate status

# 执行迁移
echo "🚀 执行数据库迁移..."
npx prisma migrate deploy

# 验证迁移
echo "✅ 验证迁移结果..."
npm run db:verify

echo "🎉 安全迁移完成！"
echo "📁 备份文件: $BACKUP_FILE"

# 清理旧备份（保留最近10个）
echo "🧹 清理旧备份文件..."
ls -t $BACKUP_DIR/backup_*.sql | tail -n +11 | xargs -r rm
echo "✅ 备份清理完成"
