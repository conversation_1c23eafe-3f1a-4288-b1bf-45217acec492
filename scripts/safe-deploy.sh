#!/bin/bash

# 安全的生产环境部署脚本 - 保护现有数据
set -e

echo "🚀 开始安全部署（保护现有数据）..."

# 1. 安装依赖
echo "📦 安装依赖..."
npm ci

# 2. 生成 Prisma 客户端
echo "⚙️ 生成 Prisma 客户端..."
npx prisma generate

# 3. 安全的数据库迁移（不会删除数据）
echo "🗄️ 执行安全的数据库迁移..."
npx prisma migrate deploy || {
  echo "⚠️ 迁移失败，尝试同步模式（不强制重置）..."
  npx prisma db push --accept-data-loss
}

# 4. 构建项目
echo "🔨 构建项目..."
npm run build

echo "🎉 安全部署完成！"
echo "✅ 现有数据已保护，仅执行了必要的模式更新"
