import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function recreateDocumentTypes() {
  console.log('🔧 重新创建默认证件类型...');

  try {
    // 获取所有商户
    const tenants = await prisma.tenant.findMany({
      include: {
        documentTypes: true,
        users: {
          where: {
            role: 'TENANT_ADMIN',
          },
          take: 1,
        },
      },
    });

    console.log(`\n📊 找到 ${tenants.length} 个商户`);

    // 默认证件类型
    const defaultDocumentTypes = [
      { name: '签证', code: 'VISA', icon: 'Flight', color: '#1976d2' },
      { name: '劳工证', code: 'WORK_PERMIT', icon: 'Work', color: '#388e3c' },
      { name: '驾驶证', code: 'DRIVER_LICENSE', icon: 'DriveEta', color: '#f57c00' },
      { name: '营业执照', code: 'BUSINESS_LICENSE', icon: 'Business', color: '#7b1fa2' },
    ];

    for (const tenant of tenants) {
      console.log(`\n🏢 处理商户: ${tenant.name}`);
      
      // 检查是否已有证件类型
      if (tenant.documentTypes.length > 0) {
        console.log(`   ✅ 已有 ${tenant.documentTypes.length} 个证件类型，跳过`);
        continue;
      }

      // 获取商户的管理员用户
      const adminUser = tenant.users[0];
      if (!adminUser) {
        console.log(`   ⚠️  找不到管理员用户，跳过`);
        continue;
      }

      console.log(`   👤 使用管理员: ${adminUser.email}`);

      // 为商户创建默认证件类型
      for (const docType of defaultDocumentTypes) {
        try {
          await prisma.documentType.create({
            data: {
              name: docType.name,
              code: docType.code,
              icon: docType.icon,
              color: docType.color,
              tenantId: tenant.id,
              createdById: adminUser.id,
              isActive: true,
            },
          });
          console.log(`     ✅ 创建证件类型: ${docType.name}`);
        } catch (error) {
          console.log(`     ❌ 创建失败: ${docType.name} - ${error}`);
        }
      }
    }

    // 显示最终统计
    console.log(`\n📊 最终统计:`);
    const finalTenants = await prisma.tenant.findMany({
      include: {
        documentTypes: true,
      },
    });

    for (const tenant of finalTenants) {
      console.log(`   ${tenant.name}: ${tenant.documentTypes.length} 个证件类型`);
      tenant.documentTypes.forEach(docType => {
        console.log(`     - ${docType.name} (${docType.code})`);
      });
    }

    console.log('\n🎉 证件类型重新创建完成！');

  } catch (error) {
    console.error('❌ 创建过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行创建脚本
recreateDocumentTypes();
