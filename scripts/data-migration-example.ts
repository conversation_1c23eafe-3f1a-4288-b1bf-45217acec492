import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 数据迁移示例：将用户的 name 字段拆分为 firstName 和 lastName
 */
async function migrateUserNames() {
  console.log('🔄 开始用户姓名数据迁移...');

  const users = await prisma.user.findMany({
    where: {
      // 只迁移还没有 firstName 的用户
      firstName: null,
    },
  });

  console.log(`📊 找到 ${users.length} 个用户需要迁移`);

  for (const user of users) {
    if (user.name) {
      const nameParts = user.name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      await prisma.user.update({
        where: { id: user.id },
        data: {
          firstName,
          lastName,
        },
      });

      console.log(`✅ 迁移用户: ${user.email}`);
    }
  }

  console.log('🎉 用户姓名数据迁移完成！');
}

// 只在直接运行此脚本时执行
if (require.main === module) {
  migrateUserNames()
    .catch((e) => {
      console.error('❌ 迁移失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { migrateUserNames };
