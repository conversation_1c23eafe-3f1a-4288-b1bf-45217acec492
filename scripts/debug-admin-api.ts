import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugAdminAPI() {
  console.log('🔍 调试超级管理员API...');

  try {
    // 1. 检查超级管理员账号
    const superAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    console.log('👑 超级管理员账号:');
    console.log(superAdmin);

    if (!superAdmin || superAdmin.role !== 'SUPER_ADMIN') {
      console.log('❌ 超级管理员账号不存在或角色不正确');
      return;
    }

    // 2. 模拟API查询 - 获取所有商户
    console.log('\n📊 模拟API查询 - 获取所有商户:');
    
    const tenants = await prisma.tenant.findMany({
      take: 50,
      orderBy: { createdAt: "desc" },
      include: {
        _count: {
          select: {
            memberships: true,
            documents: true,
            notifications: true,
            subscriptions: true,
          },
        },
        subscriptions: {
          where: { status: { in: ["ACTIVE", "TRIAL"] } },
          orderBy: { createdAt: "desc" },
          take: 1,
          include: {
            plan: {
              select: {
                name: true,
                price: true,
                billingCycle: true,
              },
            },
          },
        },
        memberships: {
          where: { role: "ADMIN" },
          take: 1,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    console.log(`找到 ${tenants.length} 个商户:`);
    
    tenants.forEach((tenant, index) => {
      console.log(`\n🏢 商户 ${index + 1}:`);
      console.log(`   ID: ${tenant.id}`);
      console.log(`   名称: ${tenant.name}`);
      console.log(`   类型: ${tenant.type}`);
      console.log(`   状态: ${tenant.isActive ? '活跃' : '禁用'}`);
      console.log(`   邮箱: ${tenant.email || '无'}`);
      console.log(`   电话: ${tenant.phone || '无'}`);
      console.log(`   地址: ${tenant.address || '无'}`);
      console.log(`   成员数: ${tenant._count.memberships}`);
      console.log(`   证件数: ${tenant._count.documents}`);
      console.log(`   订阅数: ${tenant._count.subscriptions}`);
      
      if (tenant.memberships[0]) {
        console.log(`   管理员: ${tenant.memberships[0].user.name} (${tenant.memberships[0].user.email})`);
      }
      
      if (tenant.subscriptions[0]) {
        console.log(`   当前套餐: ${tenant.subscriptions[0].plan?.name || '无'}`);
      }
    });

    // 3. 检查统计数据
    console.log('\n📈 统计数据:');
    const [
      totalTenants,
      activeTenants,
      totalUsers,
      totalDocuments,
    ] = await Promise.all([
      prisma.tenant.count(),
      prisma.tenant.count({ where: { isActive: true } }),
      prisma.user.count(),
      prisma.document.count(),
    ]);

    console.log(`   总商户数: ${totalTenants}`);
    console.log(`   活跃商户: ${activeTenants}`);
    console.log(`   总用户数: ${totalUsers}`);
    console.log(`   总证件数: ${totalDocuments}`);

    // 4. 检查按类型分组
    const tenantsByType = await prisma.tenant.groupBy({
      by: ['type'],
      _count: true,
    });

    console.log('\n📊 按类型分布:');
    tenantsByType.forEach(item => {
      console.log(`   ${item.type}: ${item._count} 个`);
    });

    // 5. 测试API响应格式
    console.log('\n🔧 API响应格式测试:');
    const apiResponse = {
      tenants: tenants.map(tenant => ({
        ...tenant,
        currentSubscription: tenant.subscriptions[0] || null,
        adminUser: tenant.memberships[0]?.user || null,
        subscriptions: undefined,
        memberships: undefined,
      })),
      pagination: {
        page: 1,
        limit: 50,
        total: totalTenants,
        totalPages: Math.ceil(totalTenants / 50),
      },
    };

    console.log(`API响应包含 ${apiResponse.tenants.length} 个商户`);
    console.log(`分页信息: 第${apiResponse.pagination.page}页，共${apiResponse.pagination.totalPages}页`);

    // 6. 检查是否有数据问题
    if (apiResponse.tenants.length === 0) {
      console.log('\n⚠️  没有找到任何商户数据');
      console.log('建议检查:');
      console.log('1. 数据库中是否有商户数据');
      console.log('2. API权限是否正确');
      console.log('3. 前端是否正确调用API');
    } else {
      console.log('\n✅ 数据查询正常，商户数据存在');
    }

  } catch (error) {
    console.error('❌ 调试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行调试
debugAdminAPI()
  .then(() => {
    console.log('\n✨ 调试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 调试失败:', error);
    process.exit(1);
  });
