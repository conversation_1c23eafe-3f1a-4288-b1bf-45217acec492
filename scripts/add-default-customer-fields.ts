import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addDefaultCustomerFields() {
  try {
    console.log('🚀 开始添加默认客户字段配置...');

    // 获取所有活跃的商户
    const tenants = await prisma.tenant.findMany({
      where: {
        isActive: true,
      },
    });

    if (tenants.length === 0) {
      console.log('❌ 没有找到活跃的商户');
      return;
    }

    console.log(`📋 找到 ${tenants.length} 个活跃商户`);

    // 默认客户字段配置
    const defaultCustomerFields = [
      {
        name: '邮箱地址',
        type: 'text',
        category: 'customer',
        placeholder: '请输入邮箱地址',
        required: false,
        sortOrder: 1,
      },
      {
        name: '地址',
        type: 'textarea',
        category: 'customer',
        placeholder: '请输入详细地址',
        required: false,
        sortOrder: 2,
      },
      {
        name: '公司名称',
        type: 'text',
        category: 'customer',
        placeholder: '请输入公司名称',
        required: false,
        sortOrder: 3,
      },
    ];

    // 为每个商户添加默认字段配置
    for (const tenant of tenants) {
      console.log(`\n🏢 处理商户: ${tenant.name}`);

      // 获取第一个管理员用户作为创建者
      const adminUser = await prisma.user.findFirst({
        where: {
          tenantId: tenant.id,
          role: {
            in: ['TENANT_ADMIN', 'SUPER_ADMIN'],
          },
        },
      });

      if (!adminUser) {
        console.log(`   ⚠️  未找到管理员用户，跳过商户 ${tenant.name}`);
        continue;
      }

      // 检查是否已经存在这些字段
      const existingFields = await prisma.customFieldConfig.findMany({
        where: {
          tenantId: tenant.id,
          category: 'customer',
          name: {
            in: defaultCustomerFields.map(f => f.name),
          },
        },
      });

      const existingFieldNames = existingFields.map(f => f.name);

      // 只添加不存在的字段
      const fieldsToAdd = defaultCustomerFields.filter(
        field => !existingFieldNames.includes(field.name)
      );

      if (fieldsToAdd.length === 0) {
        console.log(`   ✅ 所有默认字段已存在`);
        continue;
      }

      // 批量创建字段配置
      const createdFields = await prisma.customFieldConfig.createMany({
        data: fieldsToAdd.map(field => ({
          ...field,
          tenantId: tenant.id,
          createdById: adminUser.id,
          isActive: true,
        })),
      });

      console.log(`   ✅ 添加了 ${createdFields.count} 个字段配置:`);
      fieldsToAdd.forEach(field => {
        console.log(`      - ${field.name} (${field.type})`);
      });
    }

    console.log('\n🎉 默认客户字段配置添加完成！');

    // 显示统计信息
    const totalCustomerFields = await prisma.customFieldConfig.count({
      where: {
        category: 'customer',
        isActive: true,
      },
    });

    console.log(`\n📊 统计信息:`);
    console.log(`   总客户字段配置数: ${totalCustomerFields}`);

  } catch (error) {
    console.error('❌ 添加默认字段配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
addDefaultCustomerFields();
