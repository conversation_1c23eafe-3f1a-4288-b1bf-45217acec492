import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkAdmin() {
  try {
    // 查找超级管理员
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    if (admin) {
      console.log('✅ 超级管理员账号信息:');
      console.log(`   邮箱: ${admin.email}`);
      console.log(`   姓名: ${admin.name}`);
      console.log(`   角色: ${admin.role}`);
      console.log(`   创建时间: ${admin.createdAt.toLocaleString()}`);
      console.log('');
      console.log('🔑 登录信息:');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: 123456');
      console.log('');
      console.log('🌐 登录地址: http://localhost:3001/auth/signin');
    } else {
      console.log('❌ 未找到超级管理员账号');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmin();
