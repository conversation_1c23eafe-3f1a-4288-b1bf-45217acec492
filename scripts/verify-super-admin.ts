import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifySuperAdmin() {
  console.log('🔍 验证超级管理员账号...');

  try {
    // 查找所有超级管理员
    const superAdmins = await prisma.user.findMany({
      where: { role: 'SUPER_ADMIN' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        tenantId: true,
        currentTenantId: true,
      },
    });

    if (superAdmins.length === 0) {
      console.log('❌ 没有找到超级管理员账号');
      return;
    }

    console.log(`✅ 找到 ${superAdmins.length} 个超级管理员账号:\n`);

    superAdmins.forEach((admin, index) => {
      console.log(`👤 超级管理员 #${index + 1}:`);
      console.log(`   ID: ${admin.id}`);
      console.log(`   姓名: ${admin.name}`);
      console.log(`   邮箱: ${admin.email}`);
      console.log(`   角色: ${admin.role}`);
      console.log(`   创建时间: ${admin.createdAt.toLocaleString()}`);
      console.log(`   关联商户: ${admin.tenantId || '无'}`);
      console.log(`   当前商户: ${admin.currentTenantId || '无'}`);
      console.log('');
    });

    // 检查系统统计
    const [totalUsers, totalTenants, totalDocuments] = await Promise.all([
      prisma.user.count(),
      prisma.tenant.count(),
      prisma.document.count(),
    ]);

    console.log('📊 系统统计:');
    console.log(`   总用户数: ${totalUsers}`);
    console.log(`   总商户数: ${totalTenants}`);
    console.log(`   总证件数: ${totalDocuments}`);
    console.log('');

    // 检查套餐数量
    const totalPlans = await prisma.subscriptionPlan.count();
    console.log(`📦 套餐数量: ${totalPlans}`);

    // 检查系统通知配置
    const notificationConfig = await prisma.systemNotificationConfig.findFirst();
    console.log(`🔔 系统通知配置: ${notificationConfig ? '已配置' : '未配置'}`);

    console.log('\n🔗 超级管理员功能页面:');
    console.log('   • 登录页面: http://localhost:3001/auth/signin');
    console.log('   • 仪表板: http://localhost:3001/dashboard');
    console.log('   • 商户管理: http://localhost:3001/admin/merchants-enhanced');
    console.log('   • 套餐管理: http://localhost:3001/admin/plans');
    console.log('   • 订单管理: http://localhost:3001/admin/orders');
    console.log('   • 通知配置: http://localhost:3001/admin/notifications');

    console.log('\n🔑 默认登录信息:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: 123456');

  } catch (error) {
    console.error('❌ 验证失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行验证
verifySuperAdmin()
  .then(() => {
    console.log('\n✨ 验证完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 验证失败:', error);
    process.exit(1);
  });
