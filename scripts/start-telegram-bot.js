#!/usr/bin/env node

import { TelegramBotService } from '../src/server/services/telegram-bot-service.js';

async function startTelegramBot() {
  console.log('🤖 启动 Telegram Bot 服务...');
  
  try {
    await TelegramBotService.initialize();
    
    // 获取机器人信息
    const botInfo = await TelegramBotService.getBotInfo();
    if (botInfo) {
      console.log('✅ Telegram Bot 启动成功！');
      console.log(`🤖 机器人名称: ${botInfo.first_name}`);
      console.log(`👤 用户名: @${botInfo.username}`);
      console.log(`🆔 机器人ID: ${botInfo.id}`);
      console.log('📱 机器人正在监听消息...');
    }
    
    // 优雅关闭处理
    process.on('SIGINT', async () => {
      console.log('\n🛑 正在关闭 Telegram Bot...');
      await TelegramBotService.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 正在关闭 Telegram Bot...');
      await TelegramBotService.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Telegram Bot 启动失败:', error);
    process.exit(1);
  }
}

// 只在直接运行此脚本时执行
if (process.argv[1].endsWith('start-telegram-bot.js')) {
  startTelegramBot();
}

export { startTelegramBot };
