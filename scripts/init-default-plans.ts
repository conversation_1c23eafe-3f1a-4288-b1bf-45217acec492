import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 默认套餐配置
const DEFAULT_PLANS = [
  {
    name: "免费试用",
    description: "适合个人用户试用，体验基础功能",
    price: 0,
    currency: "USD",
    billingCycle: "TRIAL" as const,
    features: [
      "10个证件",
      "1个用户",
      "5个自定义字段",
      "50条通知/月",
      "基础支持"
    ],
    limits: {
      maxDocuments: 10,
      maxUsers: 1,
      maxCustomFields: 5,
      maxNotifications: 50,
      maxStorage: 100, // MB
    },
    isActive: true,
    sortOrder: 1,
  },
  {
    name: "个人商业版",
    description: "适合个人商户，提供专业的证件管理功能",
    price: 118,
    currency: "USD",
    billingCycle: "YEARLY" as const,
    features: [
      "无限证件管理",
      "1个用户",
      "无限自定义字段",
      "无限通知",
      "邮件支持",
      "数据导出",
      "Telegram通知",
      "PWA推送通知"
    ],
    limits: {
      maxDocuments: -1, // -1 表示无限制
      maxUsers: 1,
      maxCustomFields: -1,
      maxNotifications: -1,
      maxStorage: -1,
    },
    isActive: true,
    sortOrder: 2,
  },
  {
    name: "企业商业版",
    description: "适合企业用户，提供完整的团队协作和管理功能",
    price: 288,
    currency: "USD",
    billingCycle: "YEARLY" as const,
    features: [
      "无限证件管理",
      "无限用户",
      "无限自定义字段",
      "无限通知",
      "优先技术支持",
      "数据导出",
      "Telegram通知",
      "PWA推送通知",
      "API访问",
      "高级报表",
      "自定义品牌"
    ],
    limits: {
      maxDocuments: -1,
      maxUsers: -1,
      maxCustomFields: -1,
      maxNotifications: -1,
      maxStorage: -1,
    },
    isActive: true,
    sortOrder: 3,
  },
];

async function initDefaultPlans() {
  console.log('🚀 开始初始化默认套餐...');

  try {
    // 检查是否已经有套餐数据
    const existingPlans = await prisma.subscriptionPlan.count();
    
    if (existingPlans > 0) {
      console.log(`⚠️  已存在 ${existingPlans} 个套餐，跳过初始化`);
      return;
    }

    // 创建默认套餐
    for (const planData of DEFAULT_PLANS) {
      const plan = await prisma.subscriptionPlan.create({
        data: planData,
      });
      
      console.log(`✅ 创建套餐: ${plan.name} (ID: ${plan.id})`);
    }

    console.log('🎉 默认套餐初始化完成！');
    
    // 显示创建的套餐
    const plans = await prisma.subscriptionPlan.findMany({
      orderBy: { sortOrder: 'asc' },
    });
    
    console.log('\n📋 当前套餐列表:');
    plans.forEach((plan, index) => {
      console.log(`${index + 1}. ${plan.name} - $${plan.price}/${plan.billingCycle === 'YEARLY' ? '年' : plan.billingCycle === 'TRIAL' ? '试用' : '月'}`);
    });

  } catch (error) {
    console.error('❌ 初始化套餐失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行初始化
initDefaultPlans()
  .then(() => {
    console.log('✨ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });

export { initDefaultPlans };
