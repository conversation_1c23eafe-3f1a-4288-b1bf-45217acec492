# 商户资料页面套餐信息显示功能

## ✅ 功能完成！

已成功在商户资料页面添加了详细的套餐使用情况和限制信息显示。

## 🎯 新增功能

### 1. 订阅状态显示
在商户基本信息区域添加了订阅状态标签：
- **试用中** (蓝色标签)
- **已订阅** (绿色标签) 
- **待审核** (橙色标签)
- **已过期** (红色标签)

### 2. 套餐使用统计
重新设计了统计信息区域，显示实际使用情况与套餐限制的对比：

#### **证件数量**
- 显示格式：`当前数量/限制数量`
- 无限制套餐显示：`当前数量 (无限制)`
- 数据来源：`merchantInfo._count.documents`

#### **用户数量**
- 显示格式：`当前数量/限制数量`
- 无限制套餐显示：`当前数量 (无限制)`
- 数据来源：`merchantInfo.memberships.length`

#### **证件类型**
- 显示格式：`当前数量/限制数量`
- 无限制套餐显示：`当前数量 (无限制)`
- 数据来源：`merchantInfo._count.documentTypes`

#### **本月通知**
- 显示格式：`当前数量/限制数量`
- 无限制套餐显示：`当前数量 (无限制)`
- 数据来源：`merchantInfo._count.notifications`

### 3. 套餐详情信息
新增了套餐详情展示区域，包含：

#### **套餐名称**
- 显示当前订阅的套餐名称
- 如：免费试用、个人商业版、企业商业版

#### **套餐状态**
- 试用中、已激活、待审核、已过期
- 与顶部状态标签保持一致

#### **开始时间**
- 显示订阅开始的日期
- 格式：YYYY/MM/DD

#### **到期时间**
- 显示订阅到期日期
- 试用期显示试用结束时间
- 永久套餐显示"永久"

## 🎨 界面设计

### 视觉层次
1. **商户基本信息** - 顶部，包含名称、类型、状态
2. **套餐使用统计** - 中间，4个统计卡片
3. **套餐详情** - 统计下方，4个信息卡片
4. **团队成员** - 底部，用户管理区域

### 颜色系统
- **主要数据**: 主题色 (蓝色)
- **限制信息**: 次要文本色 (灰色)
- **无限制标识**: 成功色 (绿色)
- **状态标签**: 语义化颜色 (成功/信息/警告/错误)

### 响应式设计
- **桌面端**: 4列网格布局
- **平板端**: 2列网格布局
- **移动端**: 单列布局

## 📊 数据展示逻辑

### 限制显示规则
```typescript
// 有限制的情况
{currentCount}/{maxLimit}

// 无限制的情况 (maxLimit === -1)
{currentCount} (无限制)

// 无订阅的情况
{currentCount}
```

### 状态映射
```typescript
const statusMap = {
  'TRIAL': { label: '试用中', color: 'info' },
  'ACTIVE': { label: '已订阅', color: 'success' },
  'PENDING': { label: '待审核', color: 'warning' },
  'EXPIRED': { label: '已过期', color: 'error' }
}
```

## 🔧 技术实现

### 数据来源
- **商户信息**: `api.merchant.getInfo.useQuery()`
- **订阅信息**: `api.subscription.getCurrent.useQuery()`
- **统计数据**: 通过 Prisma 的 `_count` 关联查询

### 关键组件
- **统计卡片**: 使用 MUI Grid + Box 组件
- **信息卡片**: 使用背景色区分的信息展示
- **状态标签**: 使用 MUI Chip 组件

### 条件渲染
```typescript
// 只有存在订阅时才显示套餐详情
{subscription && (
  <Box sx={{ mt: 3 }}>
    {/* 套餐详情内容 */}
  </Box>
)}
```

## 📁 修改的文件

### `src/app/(dashboard)/merchant/page.tsx`
- 添加了订阅状态显示
- 重新设计了统计信息区域
- 新增了套餐详情展示区域
- 优化了数据展示逻辑

## 🎯 用户体验提升

### 1. 信息透明度
- 用户可以清楚看到当前使用情况
- 了解套餐限制和剩余额度
- 掌握订阅状态和到期时间

### 2. 决策支持
- 帮助用户判断是否需要升级套餐
- 提供使用趋势参考
- 明确套餐价值和限制

### 3. 管理便利
- 管理员可以快速了解团队使用情况
- 便于制定使用策略
- 支持资源规划决策

## 🚀 后续优化建议

### 1. 进度条显示
- 为有限制的项目添加进度条
- 可视化使用率 (如：80% 使用率)

### 2. 预警机制
- 接近限制时显示警告
- 超出限制时显示错误提示

### 3. 历史趋势
- 添加使用量历史图表
- 显示月度/年度使用趋势

### 4. 快捷操作
- 添加"升级套餐"快捷按钮
- 提供"续费"操作入口

## 📊 影响范围

- ✅ 提升了商户资料页面的信息密度
- ✅ 改善了套餐使用情况的可视化
- ✅ 增强了用户对订阅状态的感知
- ✅ 为套餐管理提供了数据支持

功能已完成，商户可以清楚地看到套餐使用情况和限制信息！🎉
