# 生产环境配置示例
# 复制此文件为 .env.production 并填入真实值

# Next Auth - 必须设置
NEXTAUTH_SECRET="your-super-secret-key-here-change-this-in-production"
NEXTAUTH_URL="https://yourdomain.com"

# 数据库 - 必须设置
DATABASE_URL="postgresql://username:password@host:port/database"

# 超级管理员账户 - 首次部署时创建
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="YourSecurePassword123!"

# 应用配置
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://yourdomain.com"

# 可选：如果使用外部邮件服务
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# 可选：如果使用 Telegram 通知
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"

# 可选：PWA 推送通知
VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"
VAPID_EMAIL="<EMAIL>"
