generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model SubscriptionPlan {
  id            String         @id @default(cuid())
  name          String         @unique
  description   String?
  price         Float
  currency      String         @default("USD")
  billingCycle  BillingCycle
  features      Json?
  limits        Json?
  isActive      Boolean        @default(true)
  sortOrder     Int            @default(0)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        Order[]
  subscriptions Subscription[]

  @@index([isActive])
  @@index([sortOrder])
}

model Tenant {
  id                   String              @id @default(cuid())
  name                 String
  type                 TenantType
  description          String?
  logo                 String?
  website              String?
  phone                String?
  email                String?
  address              String?
  settings             Json?
  isActive             Boolean             @default(true)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  telegramChatId       String?
  notificationSettings Json?
  documents            Document[]
  documentTypes        DocumentType[]
  memberships          Membership[]
  notifications        Notification[]
  orders               Order[]
  subscriptions        Subscription[]
  users                User[]
  customFieldConfigs   CustomFieldConfig[]
  activityLogs         ActivityLog[]
}

model DocumentType {
  id          String   @id @default(cuid())
  tenantId    String
  name        String
  code        String
  icon        String?
  color       String?
  description String?
  isSystem    Boolean  @default(false)
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id])
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, code])
  @@index([tenantId])
  @@index([isActive])
}

model Document {
  id            String         @id @default(cuid())
  tenantId      String
  customerName  String
  phone         String?
  email         String?
  address       String?
  company       String?
  certType      String
  certNumber    String?
  issueBy       String?
  validUntil    DateTime
  customFields  Json?
  tags          String[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  createdById   String
  createdBy     User           @relation(fields: [createdById], references: [id])
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  notifications Notification[]
  activityLogs  ActivityLog[]

  @@index([tenantId])
  @@index([validUntil])
  @@index([certType])
}

model Membership {
  id        String   @id @default(cuid())
  tenantId  String
  userId    String
  role      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
}

model Subscription {
  id            String             @id @default(cuid())
  tenantId      String
  status        SubscriptionStatus
  startDate     DateTime
  endDate       DateTime?
  trialEndDate  DateTime?
  price         Float?
  currency      String?            @default("USD")
  paymentMethod String?
  proofUrl      String?
  autoRenew     Boolean            @default(false)
  features      Json?
  limits        Json?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  planId        String
  orders        Order[]
  plan          SubscriptionPlan   @relation(fields: [planId], references: [id], onDelete: Cascade)
  tenant        Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([planId])
  @@index([status])
}

model Order {
  id             String            @id @default(cuid())
  orderNumber    String            @unique
  tenantId       String
  subscriptionId String?
  planId         String?
  amount         Float
  currency       String            @default("USD")
  status         OrderStatus
  paymentMethod  String?
  paymentId      String?
  paymentProofUrl String?          // 付款凭证URL
  adminNote      String?           // 管理员审核备注
  paidAt         DateTime?
  reviewedAt     DateTime?         // 审核时间
  reviewedBy     String?           // 审核人ID
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  plan           SubscriptionPlan? @relation(fields: [planId], references: [id])
  subscription   Subscription?     @relation(fields: [subscriptionId], references: [id])
  tenant         Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  payments       Payment[]
  logs           OrderLog[]

  @@index([tenantId])
  @@index([status])
  @@index([orderNumber])
}

model Payment {
  id            String        @id @default(cuid())
  orderId       String
  amount        Float
  currency      String        @default("USD")
  method        String
  transactionId String?
  status        PaymentStatus
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  order         Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([status])
}

model OrderLog {
  id         String   @id @default(cuid())
  orderId    String
  action     String
  note       String?
  operatorId String?
  createdAt  DateTime @default(now())
  order      Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
}

model Notification {
  id           String                @id @default(cuid())
  tenantId     String
  documentId   String?
  type         NotificationType
  title        String
  message      String
  priority     String                @default("MEDIUM")
  channels     NotificationChannel[]
  metadata     Json?
  isRead       Boolean               @default(false)
  readAt       DateTime?
  createdById  String
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  emailSent    Boolean               @default(false)
  telegramSent Boolean               @default(false)
  pushSent     Boolean               @default(false)
  sentAt       DateTime              @default(now())
  error        String?
  createdBy    User                  @relation(fields: [createdById], references: [id], onDelete: Cascade)
  document     Document?             @relation(fields: [documentId], references: [id], onDelete: Cascade)
  tenant       Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([documentId])
  @@index([sentAt])
  @@index([isRead])
  @@index([priority])
  @@index([createdAt])
}

model PushSubscription {
  id        String   @id @default(cuid())
  userId    String
  endpoint  String
  p256dh    String
  auth      String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, endpoint])
}

model ActivityLog {
  id          String           @id @default(cuid())
  action      String           // 操作类型：创建、更新、删除等
  details     String           // 操作详情
  entityType  String           // 实体类型：Document、User、Tenant等
  entityId    String?          // 实体ID

  // 操作人员信息
  userId      String
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 商户信息
  tenantId    String
  tenant      Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // 可选关联到具体文档
  documentId  String?
  document    Document?        @relation(fields: [documentId], references: [id], onDelete: SetNull)

  createdAt   DateTime         @default(now())

  @@index([userId])
  @@index([tenantId])
  @@index([entityType])
  @@index([createdAt])
}

model TelegramBinding {
  id        String   @id @default(cuid())
  userId    String   @unique
  chatId    String   @unique
  username  String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SystemNotificationConfig {
  id               String   @id @default(cuid())

  // 通知方式开关
  emailEnabled     Boolean  @default(true)
  telegramEnabled  Boolean  @default(true)
  pushEnabled      Boolean  @default(true)
  browserNotificationEnabled Boolean @default(true)

  // Telegram配置
  telegramBotToken String?
  telegramBotName  String?

  // 邮件配置
  emailProvider    String?  // SMTP, SendGrid, AWS_SES, etc.
  emailApiKey      String?
  emailFromAddress String?
  emailFromName    String?
  smtpHost         String?
  smtpPort         Int?
  smtpUser         String?
  smtpPassword     String?
  smtpSecure       Boolean  @default(true)

  // PWA推送配置
  vapidPublicKey   String?
  vapidPrivateKey  String?
  vapidSubject     String?

  // 系统推送配置
  systemPushEnabled Boolean @default(true)
  expiryPushEnabled Boolean @default(true)

  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       Json?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                   String              @id @default(cuid())
  name                 String?
  email                String?             @unique
  emailVerified        DateTime?
  image                String?
  password             String?
  role                 UserRole            @default(TENANT_MEMBER)
  status               UserStatus          @default(FREE)
  currentTenantId      String?
  tenantId             String?
  notificationSettings Json?
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  telegramChatId       String?
  telegramUsername     String?
  phone                String?
  accounts             Account[]
  documents            Document[]
  documentTypes        DocumentType[]
  memberships          Membership[]
  notifications        Notification[]
  pushSubscriptions    PushSubscription[]
  sessions             Session[]
  telegramBinding      TelegramBinding?
  tenant               Tenant?             @relation(fields: [tenantId], references: [id])
  customFieldConfigs   CustomFieldConfig[]
  activityLogs         ActivityLog[]

  @@index([tenantId])
  @@index([email])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model CustomFieldConfig {
  id           String   @id @default(cuid())
  tenantId     String
  name         String
  type         String
  category     String
  required     Boolean  @default(false)
  options      Json?
  placeholder  String?
  defaultValue String?
  isActive     Boolean  @default(true)
  sortOrder    Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  createdById  String
  createdBy    User     @relation(fields: [createdById], references: [id])
  tenant       Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, name, category])
  @@map("custom_field_configs")
}

enum TenantType {
  PERSONAL
  ENTERPRISE
  FREE
  BASIC
  PRO
}

enum UserRole {
  SUPER_ADMIN
  TENANT_ADMIN
  TENANT_MEMBER
}

enum UserStatus {
  FREE
  TRIAL
  SUBSCRIBED
  EXPIRED
}

enum SubscriptionPlanType {
  FREE
  PERSONAL
  ENTERPRISE
}

enum BillingCycle {
  MONTHLY
  YEARLY
  TRIAL
}

enum SubscriptionStatus {
  TRIAL
  ACTIVE
  EXPIRED
  PENDING
  CANCELLED
}

enum NotificationType {
  DOCUMENT_EXPIRY
  DOCUMENT_REMINDER
}

enum NotificationChannel {
  EMAIL
  TELEGRAM
  PWA_PUSH
}

enum OrderStatus {
  PENDING
  PAID
  FAILED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  REFUNDED
}
