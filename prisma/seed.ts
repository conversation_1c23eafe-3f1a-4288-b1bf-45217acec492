import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';
import dayjs from 'dayjs';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

// 设置随机种子以获得一致的结果
faker.seed(123);

// 证件类型常量
const DOCUMENT_TYPES = [
  '签证',
  '劳工证',
  '驾驶证',
  '营业执照'
] as const;

// 生成公司名称
function generateCompanyName(): string {
  const prefixes = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安'];
  const types = ['科技', '信息技术', '网络科技', '软件', '数据', '智能'];
  const suffixes = ['有限公司', '股份有限公司', '科技有限公司'];
  
  const prefix = faker.helpers.arrayElement(prefixes);
  const middle = faker.company.name().slice(0, 3);
  const type = faker.helpers.arrayElement(types);
  const suffix = faker.helpers.arrayElement(suffixes);
  
  return `${prefix}${middle}${type}${suffix}`;
}

// 生成证件号码
function generateDocumentNumber(type: string): string {
  switch (type) {
    case '签证':
      return faker.string.alphanumeric({ length: 8, casing: 'upper' });
    case '劳工证':
      return faker.string.alphanumeric({ length: 10, casing: 'upper' });
    case '驾驶证':
      return faker.string.alphanumeric({ length: 12, casing: 'upper' });
    case '营业执照':
      return faker.string.numeric(15);
    default:
      return faker.string.alphanumeric({ length: 10, casing: 'upper' });
  }
}

// 生成证件颁发机构
function generateIssuer(type: string): string {
  switch (type) {
    case '签证':
      return `${faker.location.country()}领事馆`;
    case '劳工证':
      return '人力资源和社会保障部';
    case '驾驶证':
      return `${faker.location.state()}省公安厅交通管理局`;
    case '营业执照':
      return `${faker.location.city()}市市场监督管理局`;
    default:
      return faker.company.name();
  }
}

async function main() {
  console.log('🌱 开始生成种子数据...');

  try {
    // 清理现有数据（按正确的外键依赖顺序）
     console.log('🧹 清理现有数据...');
     // 先删除所有依赖表的数据
     try {
       await prisma.notification.deleteMany({});
       await prisma.document.deleteMany({});
       await prisma.membership.deleteMany({});
       await prisma.subscription.deleteMany({});
       await prisma.pushSubscription.deleteMany({});
       await prisma.telegramBinding.deleteMany({});
       await prisma.account.deleteMany({});
       await prisma.session.deleteMany({});
       await prisma.user.deleteMany({});
       await prisma.tenant.deleteMany({});
     } catch (error) {
       console.log('注意：某些表可能不存在或为空，这是正常的');
     }

    console.log('✅ 数据清理完成');

    // 创建商户
    console.log('🏢 创建商户...');
    const tenants = [];

    // 创建免费用户商户
    for (let i = 0; i < 10; i++) {
      const tenant = await prisma.tenant.create({
        data: {
          name: `${faker.person.fullName()}的免费空间`,
          type: 'FREE',
        },
      });
      tenants.push(tenant);
    }

    // 创建个人商户
    for (let i = 0; i < 10; i++) {
      const tenant = await prisma.tenant.create({
        data: {
          name: `${faker.person.fullName()}的个人空间`,
          type: 'PERSONAL',
        },
      });
      tenants.push(tenant);
    }

    // 创建企业商户
    for (let i = 0; i < 10; i++) {
      const tenant = await prisma.tenant.create({
        data: {
          name: generateCompanyName(),
          type: 'ENTERPRISE',
        },
      });
      tenants.push(tenant);
    }

    console.log(`✅ 创建了 ${tenants.length} 个商户`);

    // 创建测试用户（用于登录）
     console.log('🔑 创建测试用户...');
     
     // 哈希测试密码
     const hashedPassword = await bcrypt.hash('123456', 10);
     
     // 创建超级管理员
     const superAdminUser = await prisma.user.create({
       data: {
         name: '超级管理员',
         email: '<EMAIL>',
         password: hashedPassword,
         emailVerified: new Date(),
         role: 'SUPER_ADMIN',
       },
     });
     
     // 创建测试免费用户商户
     const testFreeTenant = await prisma.tenant.create({
       data: {
         name: '测试免费用户',
         type: 'FREE',
       },
     });

     // 创建测试企业商户
     const testTenant = await prisma.tenant.create({
       data: {
         name: '测试企业商户',
         type: 'ENTERPRISE',
       },
     });

     // 创建免费用户
     const freeUser = await prisma.user.create({
       data: {
         name: '免费用户',
         email: '<EMAIL>',
         password: hashedPassword,
         emailVerified: new Date(),
         role: 'TENANT_ADMIN',
         tenantId: testFreeTenant.id,
         currentTenantId: testFreeTenant.id,
       },
     });

     // 创建商户管理员
     const tenantAdminUser = await prisma.user.create({
       data: {
         name: '商户管理员',
         email: '<EMAIL>',
         password: hashedPassword,
         emailVerified: new Date(),
         role: 'TENANT_ADMIN',
         tenantId: testTenant.id,
         currentTenantId: testTenant.id,
       },
     });

     // 创建商户成员
     const tenantMemberUser = await prisma.user.create({
       data: {
         name: '商户成员',
         email: '<EMAIL>',
         password: hashedPassword,
         emailVerified: new Date(),
         role: 'TENANT_MEMBER',
         tenantId: testTenant.id, // 加入测试商户
       },
     });

     // 创建用户
     console.log('👥 创建用户...');
     const users = [];

     for (let i = 0; i < 50; i++) {
       const randomTenant = tenants[Math.floor(Math.random() * tenants.length)];
       const user = await prisma.user.create({
         data: {
           name: faker.person.fullName(),
           email: `user${i + 1}@example.com`, // 确保邮箱唯一
           image: faker.image.avatar(),
           password: hashedPassword,
           emailVerified: new Date(),
           role: faker.helpers.arrayElement(['TENANT_MEMBER', 'TENANT_ADMIN']),
           tenantId: randomTenant?.id,
         },
       });
       users.push(user);

       // 为用户创建成员关系
       if (randomTenant) {
         await prisma.membership.create({
           data: {
             userId: user.id,
             tenantId: randomTenant.id,
             role: faker.helpers.arrayElement(['TENANT_MEMBER', 'TENANT_ADMIN']),
           },
         });
       }
     }

     console.log(`✅ 创建了 ${users.length} 个用户`);

    // 为测试用户创建成员关系
    await prisma.membership.create({
      data: {
        userId: freeUser.id,
        tenantId: testFreeTenant.id,
        role: 'TENANT_ADMIN',
      },
    });

    await prisma.membership.create({
      data: {
        userId: tenantAdminUser.id,
        tenantId: testTenant.id,
        role: 'TENANT_ADMIN',
      },
    });

    await prisma.membership.create({
      data: {
        userId: tenantMemberUser.id,
        tenantId: testTenant.id,
        role: 'TENANT_MEMBER',
      },
    });

    console.log('✅ 测试用户和账户创建完成');

    // 创建证件数据
    console.log('📄 创建证件数据...');
    const documents = [];
    const allUsers = [superAdminUser, tenantAdminUser, tenantMemberUser, ...users];

    for (let i = 0; i < 100; i++) {
      const randomUser = allUsers[Math.floor(Math.random() * allUsers.length)];
      const documentType = faker.helpers.arrayElement(DOCUMENT_TYPES);
      const issueDate = faker.date.past({ years: 5 });
      const expiryDate = faker.date.future({ years: faker.number.int({ min: 1, max: 10 }) });

      // 确保用户有有效的商户ID
      if (!randomUser?.tenantId) {
        continue;
      }

      const document = await prisma.document.create({
        data: {
          customerName: faker.person.fullName(),
          phone: faker.phone.number(),
          email: faker.internet.email(),
          address: faker.location.streetAddress(),
          company: faker.company.name(),
          certType: documentType,
          certNumber: generateDocumentNumber(documentType),
          issueBy: generateIssuer(documentType),
          validUntil: expiryDate,
          tags: faker.helpers.arrayElements(['重要', '紧急', '年检', '换证', '备份'], { min: 0, max: 3 }),
          customFields: {
            notes: faker.lorem.sentence(),
            importance: faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH']),
            contactPerson: faker.person.fullName(),
          },
          createdById: randomUser?.id,
          tenantId: randomUser?.tenantId,
        },
      });
      documents.push(document);
    }

    console.log(`✅ 创建了 ${documents.length} 个证件`);

    // 创建通知数据
    console.log('🔔 创建通知数据...');
    const notifications = [];

    for (let i = 0; i < 150; i++) {
      const randomUser = allUsers[Math.floor(Math.random() * allUsers.length)];
      const randomDocument = documents[Math.floor(Math.random() * documents.length)];

      // 确保用户有有效的商户ID
      if (!randomUser?.tenantId) {
        continue;
      }

      const notification = await prisma.notification.create({
        data: {
          type: faker.helpers.arrayElement(['DOCUMENT_EXPIRY', 'DOCUMENT_REMINDER']),
          title: faker.helpers.arrayElement([
            '证件即将过期提醒',
            '证件续期通知',
            '系统更新通知',
            '重要证件到期警告',
            '年检提醒',
          ]),
          message: faker.lorem.sentences(2),
          priority: faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
          isRead: faker.datatype.boolean(),
          createdById: randomUser.id,
          documentId: randomDocument?.id,
          tenantId: randomUser.tenantId,
          createdAt: faker.date.recent({ days: 30 }),
        },
      });
      notifications.push(notification);
    }

    console.log(`✅ 创建了 ${notifications.length} 个通知`);

    console.log('🎯 种子数据生成完成！');
    console.log('\n📊 生成统计:');
    console.log(`- 商户: ${tenants.length + 2} 个`);
    console.log(`- 用户: ${users.length + 4} 个`);
    console.log(`- 证件: ${documents.length} 个`);
    console.log(`- 通知: ${notifications.length} 个`);
    console.log('\n🔐 测试账户信息:');
    console.log('- 超级管理员: <EMAIL> / 123456');
    console.log('- 免费用户: <EMAIL> / 123456');
    console.log('- 商户管理员: <EMAIL> / 123456');
    console.log('- 商户成员: <EMAIL> / 123456');
    console.log('- 说明: 可用于测试不同角色的功能');
    
  } catch (error) {
    console.error('❌ 种子数据生成失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });