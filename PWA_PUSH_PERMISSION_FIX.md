# PWA推送权限控制修复总结

## 🚨 问题描述

超级管理员在系统通知配置中关闭了PWA推送功能，但是商户仍然可以在通知配置页面开启PWA推送开关，这违反了系统级权限控制的原则。

## 🔍 问题分析

### 权限控制层级
系统的通知权限应该有两个层级：
1. **系统级控制**（超级管理员）：控制整个系统是否启用某种通知方式
2. **商户级控制**（商户管理员）：在系统允许的范围内，控制自己商户的通知设置

### 问题根源
在原有的实现中：
1. **系统配置正确获取**：`getAvailableMethods` API 正确返回了系统级的配置
2. **前端显示正确**：商户页面的开关在系统禁用时会被禁用
3. **数据保存有漏洞**：但是商户的配置数据库中可能仍保留 `pushEnabled: true`
4. **权限检查不完整**：保存配置时没有强制应用系统级的禁用设置

### 技术细节

**前端逻辑**（正确）：
```typescript
// 开关显示逻辑
<Switch
  checked={config.pushEnabled && (availableMethods?.push ?? false)}
  onChange={(e) => handleConfigChange('pushEnabled', e.target.checked)}
  disabled={!(availableMethods?.push ?? false)}  // 系统禁用时开关被禁用
/>
```

**后端逻辑**（有问题）：
```typescript
// 原有的保存逻辑 - 没有考虑系统级禁用
const updatedSettings = {
  ...currentSettings,
  ...settings,  // 直接保存商户的设置，没有检查系统限制
};
```

## ✅ 修复方案

### 1. 在配置保存时强制应用系统级限制

**修复前**：
```typescript
// 直接保存商户设置，不考虑系统限制
const updatedSettings = {
  ...currentSettings,
  ...settings,
};
```

**修复后**：
```typescript
// 获取系统配置
const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
  orderBy: { createdAt: "desc" },
});

const systemSettings = systemConfig || {
  emailEnabled: true,
  telegramEnabled: true,
  pushEnabled: true,
};

// 强制应用系统级别的禁用设置
const updatedSettings = {
  ...currentSettings,
  ...settings,
  emailEnabled: settings.emailEnabled && systemSettings.emailEnabled,
  telegramEnabled: settings.telegramEnabled && systemSettings.telegramEnabled,
  pushEnabled: settings.pushEnabled && systemSettings.pushEnabled,
};
```

### 2. 在配置读取时应用系统级限制

**修复前**：
```typescript
// 只返回商户配置，不考虑系统限制
const config = {
  ...defaultConfig,
  ...settings,
  telegramChatId: tenant.telegramChatId,
  tenantId,
};
```

**修复后**：
```typescript
// 获取系统配置并应用限制
const systemConfig = await ctx.db.systemNotificationConfig.findFirst({
  orderBy: { createdAt: "desc" },
});

const systemSettings = systemConfig || systemDefaults;

const config = {
  ...defaultConfig,
  ...settings,
  // 应用系统级别的禁用设置
  emailEnabled: (settings.emailEnabled ?? defaultConfig.emailEnabled) && systemSettings.emailEnabled,
  telegramEnabled: (settings.telegramEnabled ?? defaultConfig.telegramEnabled) && systemSettings.telegramEnabled,
  pushEnabled: (settings.pushEnabled ?? defaultConfig.pushEnabled) && systemSettings.pushEnabled,
  telegramChatId: tenant.telegramChatId,
  tenantId,
};
```

## 🔧 权限控制逻辑

### 系统级控制（超级管理员）
```typescript
// 在 /admin/notifications 页面
<Switch
  checked={systemConfig.pushEnabled ?? true}
  onChange={(e) => handleSystemConfigChange('pushEnabled', e.target.checked)}
/>
```

### 商户级控制（商户管理员）
```typescript
// 在 /notifications 页面
<Switch
  checked={config.pushEnabled && (availableMethods?.push ?? false)}
  onChange={(e) => handleConfigChange('pushEnabled', e.target.checked)}
  disabled={!(availableMethods?.push ?? false)}  // 系统禁用时不可操作
/>
```

### 权限继承关系
```
系统级设置 (Super Admin)
    ↓
商户级设置 (Tenant Admin)
    ↓
最终生效设置 = 商户设置 && 系统设置
```

## 📊 修复验证

### 测试场景

**场景1：系统启用PWA推送**
- ✅ 超级管理员：可以开启/关闭系统PWA推送
- ✅ 商户管理员：可以开启/关闭自己的PWA推送
- ✅ 最终效果：商户设置生效

**场景2：系统禁用PWA推送**
- ✅ 超级管理员：PWA推送已关闭
- ✅ 商户管理员：PWA推送开关被禁用，无法开启
- ✅ 最终效果：无论商户如何设置，PWA推送都不会生效

### 数据一致性验证

**配置保存验证**：
```typescript
// 当系统禁用PWA推送时
systemConfig.pushEnabled = false;
merchantConfig.pushEnabled = true;  // 商户想开启

// 保存后的实际配置
finalConfig.pushEnabled = true && false = false;  // 被系统限制
```

**配置读取验证**：
```typescript
// 读取配置时也会应用系统限制
const config = getConfig();
// config.pushEnabled 永远不会超出系统允许的范围
```

## 🛡️ 安全增强

### 权限层级控制
1. **系统级权限**：超级管理员控制全局功能开关
2. **商户级权限**：商户管理员在系统允许范围内配置
3. **强制继承**：商户配置不能超越系统限制

### 数据完整性
1. **保存时验证**：确保保存的配置符合系统限制
2. **读取时过滤**：确保返回的配置不超出系统范围
3. **前端禁用**：在UI层面禁用被系统限制的选项

### 审计追踪
```typescript
// 记录配置变更
console.log('更新通知配置:', {
  tenantId,
  originalSettings: settings,
  systemLimits: systemSettings,
  finalSettings: updatedSettings,
});
```

## 📁 修改的文件

### 主要修改
- `src/server/api/routers/notification-config.ts`
  - `updateConfig` 方法：添加系统级限制检查
  - `getConfig` 方法：应用系统级限制过滤

### 修改内容
1. **获取系统配置**：在处理商户配置时获取系统设置
2. **强制限制应用**：使用 `&&` 逻辑确保商户设置不超出系统范围
3. **数据一致性**：保存和读取时都应用相同的限制逻辑

## 🎯 用户体验

### 超级管理员体验
- ✅ **全局控制**：可以控制整个系统的通知功能
- ✅ **即时生效**：关闭系统功能后，所有商户立即受限
- ✅ **权限明确**：清楚地知道自己的设置会影响所有商户

### 商户管理员体验
- ✅ **权限清晰**：被系统禁用的功能会显示为禁用状态
- ✅ **提示明确**：界面显示"系统已禁用XXX通知"
- ✅ **操作受限**：无法开启被系统禁用的功能

### 一般用户体验
- ✅ **功能一致**：不会收到被系统禁用的通知类型
- ✅ **体验统一**：所有商户的通知行为符合系统设置

## 🎉 完成状态

✅ **权限控制已修复** - 系统级禁用设置正确限制商户配置
✅ **数据一致性已保证** - 保存和读取配置时都应用系统限制
✅ **用户体验已改善** - 清晰的权限层级和操作反馈
✅ **安全性已增强** - 商户无法绕过系统级的功能限制

现在当超级管理员关闭PWA推送后，商户将无法开启PWA推送功能，确保了系统级权限控制的有效性！🎉
