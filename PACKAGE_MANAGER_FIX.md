# 📦 包管理器配置修正

## 🚨 发现的问题

您说得完全正确！我之前错误地使用了 `pnpm` 命令，但项目实际使用的是 **npm**。

### 🔍 证据
- ✅ 项目根目录有 `package-lock.json` 文件
- ❌ 没有 `pnpm-lock.yaml` 文件
- ✅ package.json 中的脚本使用标准 npm 格式

## ✅ 已修正的配置

### 1. **vercel.json 修正**
```json
// 修正前
{
  "buildCommand": "pnpm build",
  "installCommand": "pnpm install", 
  "devCommand": "pnpm dev"
}

// 修正后
{
  "buildCommand": "npm run build",
  "installCommand": "npm install",
  "devCommand": "npm run dev"
}
```

### 2. **部署文档修正**
所有文档中的命令都已更新为使用 npm：

```bash
# 修正前
pnpm install
pnpm build
pnpm dev
pnpm lint

# 修正后  
npm install
npm run build
npm run dev
npm run lint
```

### 3. **数据库命令修正**
```bash
# 修正前
npx prisma db seed

# 修正后 (使用 package.json 中定义的脚本)
npm run db:seed
```

## 📋 正确的命令清单

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 代码检查
npm run lint
npm run lint:fix

# 格式化代码
npm run format:check
npm run format:write

# 类型检查
npm run check
```

### 数据库命令
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行迁移 (开发环境)
npm run db:generate

# 部署迁移 (生产环境)
npm run db:migrate

# 推送 schema 变更
npm run db:push

# 生成种子数据
npm run db:seed

# 打开数据库管理界面
npm run db:studio
```

### 部署命令
```bash
# 本地构建测试
npm run build
npm run preview

# Vercel 部署
npx vercel
npx vercel --prod
```

## 🎯 为什么使用 npm？

### 项目特征
- ✅ **package-lock.json** 存在
- ✅ **标准 npm 脚本** 格式
- ✅ **T3 Stack 默认** 使用 npm
- ✅ **简单可靠** 无需额外工具

### npm vs pnpm vs yarn
| 特性 | npm | pnpm | yarn |
|------|-----|------|------|
| 项目使用 | ✅ | ❌ | ❌ |
| 锁文件 | package-lock.json | pnpm-lock.yaml | yarn.lock |
| 安装速度 | 中等 | 最快 | 快 |
| 磁盘空间 | 多 | 少 | 中等 |
| 兼容性 | 最好 | 好 | 好 |

## 🔧 Vercel 部署配置

### 正确的 vercel.json
```json
{
  "name": "certificate-reminder-system",
  "buildCommand": "npm run build",
  "outputDirectory": ".next", 
  "framework": "nextjs",
  "installCommand": "npm install",
  "devCommand": "npm run dev",
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### 环境变量 (在 Vercel Dashboard 配置)
```bash
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=your-random-secret
NEXTAUTH_URL=https://your-project-name.vercel.app
```

## 🚀 正确的部署流程

### 1. 推送代码
```bash
git add .
git commit -m "fix: correct package manager to npm"
git push origin main
```

### 2. Vercel 部署
```bash
# 使用 Vercel CLI
npx vercel

# 或通过 Dashboard
# 1. 访问 https://vercel.com/dashboard
# 2. 连接 GitHub 仓库
# 3. 配置环境变量
# 4. 部署
```

### 3. 数据库初始化
```bash
# 运行迁移
npx prisma migrate deploy

# 生成种子数据
npm run db:seed
```

## 📝 文档更新清单

已更新的文件：
- ✅ `vercel.json` - 构建命令修正
- ✅ `VERCEL_DEPLOYMENT_GUIDE.md` - 部署命令修正
- ✅ `docs/DEPLOYMENT_CHECKLIST.md` - 检查命令修正
- ✅ 所有相关文档中的包管理器命令

## 🎉 修正完成

现在所有配置都使用正确的 **npm** 命令：

- 🔧 **Vercel 配置**: 使用 `npm run build`
- 📚 **文档命令**: 统一使用 npm 语法
- 🗄️ **数据库脚本**: 使用 package.json 中定义的脚本
- 🚀 **部署流程**: 完全基于 npm 生态

感谢您的提醒！现在的配置与项目实际使用的包管理器完全一致。
