# 🚀 Vercel 部署指南

## 🚨 项目名称冲突解决方案

如果遇到 "Project already exists" 错误，请使用以下方法之一：

### 方法1: 修改 vercel.json 中的项目名称

选择一个唯一的项目名称：

```json
{
  "name": "certificate-reminder-system",  // 或使用下面的备选名称
  // ... 其他配置
}
```

### 🎯 推荐的项目名称 (选择一个)

```bash
# 英文名称
certificate-reminder-system
cert-expiry-manager
document-reminder-app
certificate-tracker
cert-management-system
expiry-notification-app

# 带用户名的名称
your-username-cert-reminder
helok-certificate-system
cert-manager-2024

# 带随机后缀的名称
certificate-reminder-v2
cert-system-pro
document-tracker-plus
```

### 方法2: 使用 Vercel CLI 指定名称

```bash
# 部署时指定项目名称
npx vercel --name certificate-reminder-system

# 或者
npx vercel --name cert-expiry-manager-2024
```

### 方法3: 通过 Vercel Dashboard 部署

1. 访问 https://vercel.com/dashboard
2. 点击 "New Project"
3. 连接 GitHub 仓库 `helok/t3notice`
4. 在项目设置中修改项目名称
5. 配置环境变量
6. 点击 "Deploy"

## ⚙️ 环境变量配置

在 Vercel Dashboard 中设置以下环境变量：

### 必需的环境变量
```bash
DATABASE_URL=postgresql://username:password@host:port/database
NEXTAUTH_SECRET=your-random-secret-string
NEXTAUTH_URL=https://your-project-name.vercel.app
```

### 生成 NEXTAUTH_SECRET
```bash
# 方法1: 使用 OpenSSL
openssl rand -base64 32

# 方法2: 使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# 方法3: 在线生成
# 访问 https://generate-secret.vercel.app/32
```

## 🗄️ 数据库配置

### 推荐的数据库服务商

1. **Supabase** (推荐)
   - 免费额度充足
   - PostgreSQL 兼容
   - 简单易用
   - 连接字符串格式: `postgresql://postgres:[password]@[host]:5432/postgres`

2. **PlanetScale**
   - MySQL 兼容 (需要修改 schema)
   - 无服务器架构
   - 连接字符串格式: `mysql://[username]:[password]@[host]/[database]?sslaccept=strict`

3. **Neon**
   - PostgreSQL 兼容
   - 无服务器架构
   - 连接字符串格式: `postgresql://[username]:[password]@[host]/[database]?sslmode=require`

### 数据库初始化

部署成功后，运行以下命令初始化数据库：

```bash
# 1. 运行数据库迁移
npx prisma migrate deploy

# 2. 生成种子数据 (创建超级管理员等)
npm run db:seed
```

## 🔧 部署后配置

### 1. 登录超级管理员
- 邮箱: `<EMAIL>`
- 密码: `123456`

### 2. 配置系统通知
访问 `/admin/notifications` 配置：
- 📧 邮件服务 (SMTP)
- 📱 Telegram Bot
- 🔔 PWA推送 (VAPID密钥)

### 3. 生成 VAPID 密钥
```bash
# 安装 web-push 工具
npm install -g web-push

# 生成密钥对
web-push generate-vapid-keys

# 输出示例:
# Public Key: BEl62iUYgUivxIkv69yViEuiBIa40HI80NqIUHI5aaZAmS6TKHWrmkiZzqjSviuF_ZjPq6RRDtp2HUGGRzVBUaA
# Private Key: your-private-key-here

# 在管理界面中配置这些密钥
```

## 🚀 快速部署命令

```bash
# 1. 确保代码已推送到 GitHub
git add .
git commit -m "fix: update project name for deployment"
git push origin main

# 2. 使用 Vercel CLI 部署
npx vercel

# 3. 按提示操作:
# - 选择项目名称 (使用上面推荐的名称)
# - 确认设置
# - 等待部署完成

# 4. 设置生产环境
npx vercel --prod
```

## 🔍 常见问题解决

### Q: 项目名称仍然冲突？
A: 尝试添加随机后缀，如 `certificate-reminder-${Date.now()}`

### Q: 数据库连接失败？
A: 检查 DATABASE_URL 格式和数据库服务状态

### Q: NextAuth 错误？
A: 确保 NEXTAUTH_SECRET 和 NEXTAUTH_URL 设置正确

### Q: 构建失败？
A: 检查 TypeScript 错误和依赖版本

## 📋 部署检查清单

- [ ] 选择唯一的项目名称
- [ ] 配置 3 个必需环境变量
- [ ] 数据库服务正常运行
- [ ] GitHub 代码已推送
- [ ] Vercel 部署成功
- [ ] 数据库迁移完成
- [ ] 超级管理员账户可登录
- [ ] 系统通知配置完成
- [ ] 功能测试通过

## 🎉 部署成功后

您的证件到期提醒管理系统将在以下地址可用：
`https://your-project-name.vercel.app`

记得在系统管理界面中完成通知配置，然后就可以开始使用了！
