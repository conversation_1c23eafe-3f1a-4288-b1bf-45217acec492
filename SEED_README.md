# 数据库种子数据说明

## 概述

本项目提供了完整的数据库种子数据生成脚本，用于快速创建测试数据，方便开发和测试登录注册功能。

## 使用方法

### 1. 生成种子数据

```bash
# 运行种子数据生成脚本
npm run db:seed
```

### 2. 重置数据库（可选）

如果需要重新生成数据，可以先重置数据库：

```bash
# 重置数据库
npx prisma migrate reset

# 重新生成种子数据
npm run db:seed
```

## 生成的测试数据

### 用户和商户
- **个人商户**: 3个
- **企业商户**: 5个
- **用户**: 15个 + 1个测试用户
- **成员关系**: 每个商户1-4个成员

### 证件数据
- **证件类型**: 身份证、护照、驾驶证、营业执照等10种类型
- **证件数量**: 每个商户5-20个证件
- **到期状态**: 包含已过期、即将过期、未来到期的证件
- **自定义字段**: 随机生成备注、重要程度、负责人等字段
- **标签**: 重要、紧急、年检、换证等标签

### 通知记录
- **通知数量**: 50条
- **通知类型**: Telegram、Web Push、邮件
- **成功率**: 80%的通知成功，20%失败（模拟真实场景）

### 订阅信息
- **订阅状态**: 试用、活跃、过期
- **订阅计划**: trial、standard、premium

## 测试账户信息

为了方便测试登录功能，系统会自动创建一个测试用户：

- **邮箱**: `<EMAIL>`
- **登录方式**: Discord OAuth
- **角色**: 商户所有者
- **商户**: "测试用户的个人空间"

### 如何使用测试账户登录

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 访问 `http://localhost:3000`

3. 点击"免费开始试用"或"立即开始"按钮

4. 选择 Discord 登录

5. 使用你的 Discord 账户登录（系统会自动匹配到测试用户）

## 数据特点

### 真实性
- 使用 `@faker-js/faker` 生成中文本地化的假数据
- 证件号码、公司名称、地址等符合中国格式
- 日期分布合理，包含过去、现在、未来的时间点

### 多样性
- 不同类型的租户（个人/企业）
- 不同角色的用户（所有者/管理员/成员）
- 不同状态的证件（有效/过期/即将过期）
- 不同类型的通知（成功/失败）

### 关联性
- 用户与租户的成员关系
- 证件与创建者的关联
- 通知与证件的关联
- 订阅与租户的关联

## 注意事项

1. **数据清理**: 种子脚本会清理现有数据，请谨慎在生产环境使用

2. **环境变量**: 确保 `DATABASE_URL` 已正确配置

3. **依赖安装**: 确保已安装所有必要的依赖：
   ```bash
   npm install
   ```

4. **数据库迁移**: 运行种子脚本前确保数据库迁移已完成：
   ```bash
   npx prisma migrate dev
   ```

## 自定义种子数据

你可以修改 `prisma/seed.ts` 文件来自定义生成的数据：

- 调整数据数量（租户、用户、证件等）
- 修改证件类型和颁发机构
- 自定义用户角色分布
- 调整日期范围和分布

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 环境变量
   - 确保数据库服务正在运行

2. **依赖缺失**
   ```bash
   npm install @faker-js/faker tsx --save-dev
   ```

3. **权限问题**
   - 确保数据库用户有创建、删除权限

4. **类型错误**
   ```bash
   npx prisma generate
   ```

## 开发建议

1. **定期重新生成**: 在开发过程中定期重新生成种子数据，保持数据的新鲜度

2. **备份重要数据**: 在生产环境中，请备份重要数据后再运行种子脚本

3. **环境隔离**: 建议在不同环境（开发、测试、生产）使用不同的数据库

4. **性能考虑**: 大量数据生成可能需要一些时间，请耐心等待

---

**提示**: 生成的所有数据都是虚假的测试数据，不包含任何真实的个人信息或敏感数据。