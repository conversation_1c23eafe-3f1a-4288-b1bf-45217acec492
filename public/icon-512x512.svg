<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <rect width="512" height="512" rx="80" fill="url(#gradient)"/>

  <!-- 证件卡片 -->
  <rect x="100" y="160" width="312" height="192" rx="20" fill="white" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>

  <!-- 证件头部 -->
  <rect x="120" y="180" width="272" height="40" rx="8" fill="#e3f2fd"/>
  <rect x="136" y="192" width="80" height="16" rx="4" fill="#1976d2"/>

  <!-- 证件内容 -->
  <rect x="136" y="240" width="160" height="8" rx="4" fill="#666"/>
  <rect x="136" y="256" width="120" height="6" rx="3" fill="#999"/>
  <rect x="136" y="272" width="140" height="6" rx="3" fill="#999"/>
  <rect x="136" y="288" width="100" height="6" rx="3" fill="#999"/>
  <rect x="136" y="304" width="80" height="6" rx="3" fill="#999"/>

  <!-- 头像区域 -->
  <rect x="320" y="240" width="64" height="80" rx="8" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
  <circle cx="352" cy="268" r="16" fill="#e0e0e0"/>
  <rect x="336" y="292" width="32" height="16" rx="4" fill="#e0e0e0"/>

  <!-- 提醒铃铛 -->
  <g transform="translate(400, 120)">
    <circle cx="32" cy="32" r="32" fill="#ff9800"/>
    <path d="M20 20c0-6.6 5.4-12 12-12s12 5.4 12 12v4c3.3 0 6 2.7 6 6v12c0 3.3-2.7 6-6 6H20c-3.3 0-6-2.7-6-6V30c0-3.3 2.7-6 6-6v-4z" fill="white"/>
    <rect x="26" y="48" width="12" height="4" rx="2" fill="white"/>
    <!-- 动画波纹 -->
    <circle cx="32" cy="32" r="40" fill="none" stroke="#ff9800" stroke-width="2" opacity="0.3"/>
    <circle cx="32" cy="32" r="48" fill="none" stroke="#ff9800" stroke-width="1" opacity="0.2"/>
  </g>

  <!-- 状态指示器 -->
  <circle cx="440" cy="140" r="16" fill="#4caf50"/>
  <circle cx="440" cy="140" r="8" fill="white"/>

  <!-- 装饰元素 -->
  <circle cx="80" cy="100" r="4" fill="rgba(255,255,255,0.3)"/>
  <circle cx="450" cy="400" r="6" fill="rgba(255,255,255,0.2)"/>
  <circle cx="60" cy="450" r="3" fill="rgba(255,255,255,0.4)"/>

  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196f3;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#1565c0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0d47a1;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>