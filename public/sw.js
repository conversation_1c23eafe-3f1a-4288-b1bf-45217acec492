if(!self.define){let e,s={};const a=(a,n)=>(a=new URL(a+".js",n).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(n,t)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let c={};const u=e=>a(e,i),r={module:{uri:i},exports:c,require:u};s[i]=Promise.all(n.map(e=>r[e]||u(e))).then(e=>(t(...e),c))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"d965ad7de978d1ecbad87aaa3a4831ab"},{url:"/_next/static/chunks/1146-f2e0ca2b61d491e5.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/125-d3eb24d3f6136b3d.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/167-317f9c03960646ca.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/1684-7f095b1ef288bc77.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/1912-3dd898b930b1a67c.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/2149-d53768deb3cde303.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/2582-576f84879994f8ad.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/2587-b0e9e598016c4685.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3191-04f3b0a260de3dbb.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3420-353cbcd6eef50252.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3443-9c52546f4bcd9a79.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3630-59422d0cd058c094.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3637-78e706a612c3545d.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/38-351d7f4976a2a608.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3883-d2513cc83fbcb8b7.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/3951-ec5d70ccaf241e14.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/4101-6a46075b1db9f437.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/4133-768bf8ac1263f152.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/4bd1b696-c23bed1d984e2104.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5158-a30843e9a144f886.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5183-66cc128e6c2714a8.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5208-943361b900942d0d.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5281-0c4951842e52b345.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5396-64dbbb6e17eb7a76.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5759-dcee37f825391f98.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/5857-c658140af1262cae.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/6096-157b642059aacad0.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/6320-7e56f4164b595aea.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/6438-116322a45540c901.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/6683-983b895eedc4772a.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/69-b658f17ace49755f.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/7540-059b27a9c52a46e0.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/7902-c472c52bb1e2cdc2.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8044-6fdf0e394eb3779d.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8240-077cd1fa37d200cc.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8542-636e30903a3dfcff.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8903-d51fdec387d5c1f1.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8904-a295b91d3e4a08c6.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/8999-60ff7c8a22339fe2.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/9199-35cd256b76b5f8b2.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/9435-173706fb60dd2056.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/9616-6de0ea18cbc28770.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/9760-210cda3ff9697136.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/9790-85ee48174dbdb879.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/bank-config/page-f53ae70108d739c2.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/merchant-settings/%5Bid%5D/page-1d238d4c6a269c54.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/merchants-enhanced/page-278b5a2128c8cb19.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/notifications/page-afd912dd62ec99e2.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/orders/page-bee65d5f18fbe3ce.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/plans/page-b5a64c4580b35c15.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/admin/test-api/page-a87b7a5ff026c0d5.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/calendar/page-9d5df08a91d2e4dd.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/dashboard/page-b4d2bc099e775c1f.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/documents/new/page-6ed476bdfc43b11a.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/documents/page-2ef8410bb98c9065.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/layout-8ad8940bf8a8ca9f.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/merchant/page-f6542bdce2daf85c.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/notifications/page-f18d09c83286b724.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/permissions-test/page-91fe486a566c3bd8.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/settings/page-65f6f586a1b24938.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/(dashboard)/upgrade/page-444a918ebb768605.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/_not-found/page-83bc162e4cbce56d.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/api/auth/%5B...nextauth%5D/route-8ed8d6559764f2fe.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/api/auth/register/route-0945a65762c6eac9.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/api/trpc/%5Btrpc%5D/route-f347a2f48dbf5163.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/api/upload/route-68546e3125ef7c55.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/auth/login/page-5025ee810b2d4098.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/auth/page-3548ed17c8d709bd.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/auth/register/page-d4f6b48ad9a5932f.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/auth/signin/page-5be53db3f18d2296.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/debug/auth/page-6defcc1956f0dc29.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/layout-fd6e82dcc3458c4a.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/page-c8f2a825132e593a.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/app/test-login/page-337de86f6f1dfbcb.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/framework-82b67a6346ddd02b.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/main-4ae072e12edcb6a4.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/main-app-901eba471003cca5.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/pages/_app-5d1abe03d322390c.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/pages/_error-3b2a1d523de49635.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-a4056d120b9b4634.js",revision:"hR66Wa33MuOQFvplHfELB"},{url:"/_next/static/css/fc858b64c929a956.css",revision:"fc858b64c929a956"},{url:"/_next/static/hR66Wa33MuOQFvplHfELB/_buildManifest.js",revision:"29a4cc081e37a83a7abee4f086eb11c2"},{url:"/_next/static/hR66Wa33MuOQFvplHfELB/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/favicon.ico",revision:"5e0db28ddce9e1491c6bd533bdfb0864"},{url:"/favicon.svg",revision:"2ba9e9fd4e552d272f3fee4c3ae4ad2e"},{url:"/icon-192x192.svg",revision:"c48de4409cffec72ebf998a47989ffd3"},{url:"/icon-512x512.svg",revision:"6a85a6e9fba55a97e6607f82e3abceab"},{url:"/manifest.json",revision:"cdb6b448185c6e3a9d34c3d1a55a46f6"},{url:"/uploads/payment-proofs/payment_1752145911608_qgwv9maoyw.png",revision:"f81b7b7fe051e36648d6ea0eb5105d12"},{url:"/uploads/payment-proofs/payment_1752781499477_zvrrskvluao.png",revision:"090b1e74e25ecb4cc91f157317fd726e"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:n})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
