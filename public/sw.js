if(!self.define){let s,e={};const a=(a,n)=>(a=new URL(a+".js",n).href,e[a]||new Promise(e=>{if("document"in self){const s=document.createElement("script");s.src=a,s.onload=e,document.head.appendChild(s)}else s=a,importScripts(a),e()}).then(()=>{let s=e[a];if(!s)throw new Error(`Module ${a} didn’t register its module`);return s}));self.define=(n,t)=>{const i=s||("document"in self?document.currentScript.src:"")||location.href;if(e[i])return;let c={};const r=s=>a(s,i),u={module:{uri:i},exports:c,require:r};e[i]=Promise.all(n.map(s=>u[s]||r(s))).then(s=>(t(...s),c))}}define(["./workbox-4754cb34"],function(s){"use strict";importScripts(),self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"c54ba85f126f471b91d9e0abcea249cb"},{url:"/_next/static/chunks/1146-f2e0ca2b61d491e5.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/125-d3eb24d3f6136b3d.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/167-317f9c03960646ca.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/1684-7f095b1ef288bc77.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/1912-3dd898b930b1a67c.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/2149-d53768deb3cde303.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/2582-576f84879994f8ad.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/2587-b0e9e598016c4685.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3191-04f3b0a260de3dbb.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3420-353cbcd6eef50252.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3443-9c52546f4bcd9a79.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3630-59422d0cd058c094.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3637-78e706a612c3545d.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/38-351d7f4976a2a608.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3883-d2513cc83fbcb8b7.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/3951-ec5d70ccaf241e14.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/4101-6a46075b1db9f437.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/4133-768bf8ac1263f152.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/4bd1b696-c23bed1d984e2104.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5158-a30843e9a144f886.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5183-66cc128e6c2714a8.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5208-943361b900942d0d.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5281-0c4951842e52b345.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5396-64dbbb6e17eb7a76.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5759-dcee37f825391f98.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/5857-c658140af1262cae.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/6096-157b642059aacad0.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/6320-7e56f4164b595aea.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/6438-116322a45540c901.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/6683-983b895eedc4772a.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/69-b658f17ace49755f.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/7540-059b27a9c52a46e0.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/7902-c472c52bb1e2cdc2.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8044-6fdf0e394eb3779d.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8240-077cd1fa37d200cc.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8542-636e30903a3dfcff.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8903-d51fdec387d5c1f1.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8904-a295b91d3e4a08c6.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/8999-60ff7c8a22339fe2.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/9199-35cd256b76b5f8b2.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/9435-173706fb60dd2056.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/9616-6de0ea18cbc28770.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/9760-210cda3ff9697136.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/9790-85ee48174dbdb879.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/bank-config/page-f53ae70108d739c2.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/merchant-settings/%5Bid%5D/page-1d238d4c6a269c54.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/merchants-enhanced/page-278b5a2128c8cb19.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/notifications/page-afd912dd62ec99e2.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/orders/page-bee65d5f18fbe3ce.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/plans/page-b5a64c4580b35c15.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/admin/test-api/page-a87b7a5ff026c0d5.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/calendar/page-9d5df08a91d2e4dd.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/dashboard/page-b4d2bc099e775c1f.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/documents/new/page-6ed476bdfc43b11a.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/documents/page-2ef8410bb98c9065.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/layout-8ad8940bf8a8ca9f.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/merchant/page-f6542bdce2daf85c.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/notifications/page-8075269cf81f4338.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/permissions-test/page-91fe486a566c3bd8.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/settings/page-65f6f586a1b24938.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/(dashboard)/upgrade/page-444a918ebb768605.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/_not-found/page-83bc162e4cbce56d.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/api/auth/%5B...nextauth%5D/route-8ed8d6559764f2fe.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/api/auth/register/route-0945a65762c6eac9.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/api/trpc/%5Btrpc%5D/route-f347a2f48dbf5163.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/api/upload/route-68546e3125ef7c55.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/auth/login/page-5025ee810b2d4098.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/auth/page-3548ed17c8d709bd.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/auth/register/page-d4f6b48ad9a5932f.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/auth/signin/page-5be53db3f18d2296.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/debug/auth/page-6defcc1956f0dc29.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/layout-fd6e82dcc3458c4a.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/page-c8f2a825132e593a.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/app/test-login/page-337de86f6f1dfbcb.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/framework-82b67a6346ddd02b.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/main-4ae072e12edcb6a4.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/main-app-901eba471003cca5.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/pages/_app-5d1abe03d322390c.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/pages/_error-3b2a1d523de49635.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-a4056d120b9b4634.js",revision:"nyaEpA35A9DS7EqLsJwIy"},{url:"/_next/static/css/f873c11a9c147a2c.css",revision:"f873c11a9c147a2c"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/_next/static/nyaEpA35A9DS7EqLsJwIy/_buildManifest.js",revision:"29a4cc081e37a83a7abee4f086eb11c2"},{url:"/_next/static/nyaEpA35A9DS7EqLsJwIy/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/favicon.ico",revision:"5e0db28ddce9e1491c6bd533bdfb0864"},{url:"/favicon.svg",revision:"2ba9e9fd4e552d272f3fee4c3ae4ad2e"},{url:"/icon-192x192.svg",revision:"c48de4409cffec72ebf998a47989ffd3"},{url:"/icon-512x512.svg",revision:"6a85a6e9fba55a97e6607f82e3abceab"},{url:"/manifest.json",revision:"cdb6b448185c6e3a9d34c3d1a55a46f6"},{url:"/uploads/payment-proofs/payment_1752145911608_qgwv9maoyw.png",revision:"f81b7b7fe051e36648d6ea0eb5105d12"},{url:"/uploads/payment-proofs/payment_1752781499477_zvrrskvluao.png",revision:"090b1e74e25ecb4cc91f157317fd726e"}],{ignoreURLParametersMatching:[]}),s.cleanupOutdatedCaches(),s.registerRoute("/",new s.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:s,response:e,event:a,state:n})=>e&&"opaqueredirect"===e.type?new Response(e.body,{status:200,statusText:"OK",headers:e.headers}):e}]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new s.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new s.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new s.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new s.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/image\?url=.+$/i,new s.StaleWhileRevalidate({cacheName:"next-image",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp3|wav|ogg)$/i,new s.CacheFirst({cacheName:"static-audio-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp4)$/i,new s.CacheFirst({cacheName:"static-video-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:js)$/i,new s.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:css|less)$/i,new s.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new s.StaleWhileRevalidate({cacheName:"next-data",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:json|xml|csv)$/i,new s.NetworkFirst({cacheName:"static-data-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;const e=s.pathname;return!e.startsWith("/api/auth/")&&!!e.startsWith("/api/")},new s.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;return!s.pathname.startsWith("/api/")},new s.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>!(self.origin===s.origin),new s.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
