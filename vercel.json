{"buildCommand": "pnpm build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "pnpm install", "devCommand": "pnpm dev", "env": {"NEXTAUTH_URL": "https://your-domain.vercel.app", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}