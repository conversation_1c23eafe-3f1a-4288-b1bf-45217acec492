{"buildCommand": "pnpm build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "pnpm install", "devCommand": "pnpm dev", "env": {"NEXTAUTH_URL": "https://your-domain.vercel.app", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url", "TELEGRAM_BOT_TOKEN": "@telegram_bot_token", "VAPID_PUBLIC_KEY": "@vapid_public_key", "VAPID_PRIVATE_KEY": "@vapid_private_key", "VAPID_EMAIL": "@vapid_email", "NEXT_PUBLIC_VAPID_PUBLIC_KEY": "@next_public_vapid_public_key"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}